import * as ActionTypes from '../Constants/MediaFileConstant'

const initElements =
{
    isLoading: false,
    errMess: null,
    mediaFiles: [],
    message: null
}

export const MediaFileReducer = (state = initElements, action) =>
{
    switch(action.type)
    {
        default:
            return {...state}
        case ActionTypes.FILE_LOADING:
            return {...state, isLoading: true}
        case ActionTypes.FILE_FAILED:
            return {...state, isLoading: false, errMess: action.payload}
        case ActionTypes.FILE_SUCCESS:
            return {...state, isLoading: false, mediaFiles: action.payload, errMess: null}
        case ActionTypes.FILE_DELETE:
            return {...state, isLoading: false, message: action.payload, errMess: null}
    }
}