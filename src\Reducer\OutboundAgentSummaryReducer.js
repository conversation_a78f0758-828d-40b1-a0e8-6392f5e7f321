import * as ActionTypes from "../Constants/OutboundAgentSummaryConstants"

const initialState = {
    columns: [],
    data: [],
    errMess: '',
    isLoading: false
}

export const OutboundAgentSummaryReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.OUTBOUND_AGENT_SUMMARY_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.OUTBOUND_AGENT_SUMMARY_COLUMNS:
            return { ...state, columns: action.payload, errMess: '' }
        case ActionTypes.OUTBOUND_AGENT_SUMMARY_SUCCESS:
            return { ...state, isLoading: false, data: action.payload, errMess: '' }
        case ActionTypes.OUTBOUND_AGENT_SUMMARY_FAILED:
            return { ...state, isLoading: false, errMess: action.payload }
        case ActionTypes.OUTBOUND_AGENT_SUMMARY_RESET:
            return { ...state, data: [] }
    }
}