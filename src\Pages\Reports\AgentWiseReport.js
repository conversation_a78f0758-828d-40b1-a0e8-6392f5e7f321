import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, DatePicker, Form, Select, Table, Input, Space, Modal } from "antd";
import apiClient from "../../Shared/apiClient";
import { openNotificationWithIcon } from "../../Shared/notification";
import { CSVLink } from "react-csv";
import { FilterOutlined, ReloadOutlined, SearchOutlined } from "@ant-design/icons";
import Highlighter from "react-highlight-words";

// const columns = [
//     {
//         title: "Agent",
//         dataIndex: "Agent",
//         key: "Agent",
//         width: 150,
//     },
//     {
//         title: "Logged In Time",
//         dataIndex: "loginTime",
//         key: "loginTime",
//         width: 200,
//     },
//     {
//         title: "Break Time",
//         dataIndex: "breakTime",
//         key: "breakTime",
//         width: 150,
//     },
//     {
//         title: "Incoming Calls",
//         dataIndex: "incomingCalls",
//         key: "incomingCalls",
//         width: 200,
//     },
//     {
//         title: "Answered Incoming Calls",
//         dataIndex: "answeredCalls",
//         key: "answeredCalls",
//         width: 200,
//     },
//     {
//         title: "Abandoned Incoming Calls",
//         dataIndex: "abandonedCalls",
//         key: "abandonedCalls",
//         width: 250,
//     },
//     {
//         title: "Answered Within  (20s)",
//         dataIndex: "thresholdCalls",
//         key: "thresholdCalls",
//         width: 250,
//     },


//     //after
//     {
//         title: "Outgoing Calls",
//         dataIndex: "outboundCalls",
//         key: "outboundCalls",
//         width: 200,
//     },
//     {
//         title: "Talk Time Incoming",
//         dataIndex: "talkTime",
//         key: "talkTime",
//         width: 200,
//     },
//     {
//         title: "Total Hold Time",
//         dataIndex: "holdTime",
//         key: "holdTime",
//         width: 200
//     },
//     {
//         title: "Average Call Handling Time",
//         dataIndex: "AHT",
//         key: "AHT",
//         width: 230,
//     },
//     {
//         title: "Agent Productivity (%)",
//         dataIndex: "agentProductivity",
//         key: "agentProductivity",
//         width: 200,
//     },
// ];

const headers = [
    { label: "Agent", key: "Agent" },
    { label: "Logged In Time", key: "loginTime" },
    { label: "Break Time", key: "breakTime" },
    { label: "Incoming Calls", key: "incomingCalls" },
    { label: "Answered Incoming Calls", key: "answeredCalls" },
    { label: "Abandoned Incoming Calls", key: "abandonedCalls" },
    { label: "Answered Within Threshold (10s)", key: "thresholdCalls" },
    { label: "Answered After Threshold (10s)", key: "afterThresholdCalls" },
    { label: "Abandoned Calls within (10s)", key: "thresholdabandonCalls" },
    { label: "Abandoned Calls After (10s)", key: "thresholdafterabandonCalls" },
    { label: "Outgoing Calls", key: "outboundCalls" },
    { label: "Talk Time Incoming", key: "talkTime" },
    { label: "Total Hold Time ", key: "holdTime" },
    { label: "Total AWC Time ", key: "awcOutput" },
    { label: "Average Call Handling Time", key: "AHT" },
    { label: "Agent Productivity (%)", key: "agentProductivity" }
];

const AgentWiseReport = () => {
    const [form] = Form.useForm();
    const [queues, setQueues] = useState([])
    const [selectQueue, setSelectQueue] = useState('')
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [filterType, setFilterType] = useState(null)
    const [monthOrDate, setMonthOrDate] = useState(null)
    const [showFilter, setShowFilter] = useState(false)
    const [resetFilter, setResetFilter] = useState(false)

    const resetFormFilter = () => {
        // dispatch(getTrunkPerHourReport())
        // dispatch(trunkPerHourReset())
        // console.log(new Date().toLocaleString() + '')
        setData([])
        form.resetFields()
        setResetFilter(true)
    }

    const onPickerChange = (date, dateString) => {
        setMonthOrDate(dateString);
    };

    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const searchInput = useRef();



    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }

    const columns = [
        {
            title: "Agent",
            dataIndex: "Agent",
            key: "Agent",
            width: 150,
            ...getColumnSearchProps('Agent')
        },
        {
            title: "Logged In Time",
            dataIndex: "loginTime",
            key: "loginTime",
            width: 200,

        },
        {
            title: "Break Time",
            dataIndex: "breakTime",
            key: "breakTime",
            width: 150,
        },
        {
            title: "Incoming Calls",
            dataIndex: "incomingCalls",
            key: "incomingCalls",
            width: 200,
            ...getColumnSearchProps('incomingCalls')
        },
        {
            title: "Answered Incoming Calls",
            dataIndex: "answeredCalls",
            key: "answeredCalls",
            width: 200,
            ...getColumnSearchProps('answeredCalls')
        },
        {
            title: "Abandoned Incoming Calls",
            dataIndex: "abandonedCalls",
            key: "abandonedCalls",
            width: 250,
            ...getColumnSearchProps('abandonedCalls')
        },
        {
            title: "Answered Within Threshold (10s)",
            dataIndex: "thresholdCalls",
            key: "thresholdCalls",
            width: 250,
            ...getColumnSearchProps('thresholdCalls')
        },
        {
            title: "Answered After Threshold (10s)",
            dataIndex: "afterThresholdCalls",
            key: "afterThresholdCalls",
            width: 250,
            ...getColumnSearchProps('afterThresholdCalls')
        },
        {
            title: "Abandoned Calls Within (10s)",
            dataIndex: "thresholdabandonCalls",
            key: "thresholdabandonCalls",
            width: 200,
            ...getColumnSearchProps('thresholdabandonCalls')
        },
        {
            title: "Abandoned Calls After (10s)",
            dataIndex: "thresholdafterabandonCalls",
            key: "thresholdafterabandonCalls",
            width: 200,
            ...getColumnSearchProps('thresholdafterabandonCalls')
        },


        //after
        {
            title: "Outgoing Calls",
            dataIndex: "outboundCalls",
            key: "outboundCalls",
            width: 200,
            ...getColumnSearchProps('outboundCalls')
        },
        {
            title: "Talk Time Incoming",
            dataIndex: "talkTime",
            key: "talkTime",
            width: 200,
            ...getColumnSearchProps('talkTime')
        },
        {
            title: "Total Hold Time",
            dataIndex: "holdTime",
            key: "holdTime",
            width: 200,
            ...getColumnSearchProps('holdTime')
        },
        {
            title: "Total ACW Time",
            dataIndex: "awcOutput",
            key: "awcOutput",
            width: 200,
            ...getColumnSearchProps('awcOutput')
        },
        {
            title: "Average Call Handling Time",
            dataIndex: "AHT",
            key: "AHT",
            width: 230,
            ...getColumnSearchProps('AHT')
        },
        {
            title: "Agent Productivity (%)",
            dataIndex: "agentProductivity",
            key: "agentProductivity",
            width: 200,
            ...getColumnSearchProps('agentProductivity')
        },
    ];

    useEffect(() => {
        apiClient.get('api/queue').then((res) => res.data && setQueues(res.data))
    }, [])

    const fetchReport = () => {
        setLoading(true);
        let payload = { queue: selectQueue, type: filterType };
        payload[filterType === 'month' ? 'month' : 'start'] = monthOrDate;
        console.log("Payload", payload)


        apiClient
            .post(`/api/report/getAgentKPIReport`, payload)
            .then((res) => {
                setData(res.data[0] || []);
                setLoading(false);
                setFilterType(null)
                setMonthOrDate(null)
                setSelectQueue(null)
                form.resetFields();
            })
            .catch((err) => {
                setLoading(false);
                openNotificationWithIcon("error", err.message);
            });
    };

    return (
        <>
            {/* <Card
                title="Agent Wise Report"
                style={{ background: "#fff", borderRadius: "2px", padding: 10 }}
                extra={
                    <Form
                        form={form}
                        layout="inline"
                        size="large"
                        style={{ marginTop: "3px" }}
                    >
                        <Form.Item name={'type'}>
                            <Select placeholder="Type" onChange={(val) => setFilterType(val)}>
                                <Select.Option value="date">Date</Select.Option>
                                <Select.Option value="month">Month</Select.Option>
                            </Select>
                        </Form.Item>

                        {filterType && <Form.Item name={'monthOrDate'}>
                            <DatePicker onChange={onPickerChange} picker={filterType} />
                        </Form.Item>}

                        <Form.Item name={"queue"}>
                            <Select
                                showSearch
                                placeholder="Select Queue"
                                style={{ width: '170px' }}
                                onChange={(e) => setSelectQueue(e)}
                            >
                                {queues.map((queue, index) => (
                                    <Select.Option key={index} value={queue.name}>{queue.name}</Select.Option>
                                ))}
                            </Select>
                        </Form.Item>
                        <Form.Item>
                            <Button
                                size="large"
                                onClick={fetchReport}
                                disabled={!selectQueue || !monthOrDate}
                            >
                                Fetch Report
                            </Button>
                        </Form.Item>
                        <Form.Item>
                            <CSVLink filename="agent_wise_report.csv" headers={headers} data={data || []}>
                                <Button disabled={data?.length === 0}>Export CSV</Button>
                            </CSVLink>
                        </Form.Item>
                    </Form>
                }
            > */}
            <Table

                title={data => <>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        {`Agent Wise Report`}
                        <Space>
                            <Button onClick={() => resetFormFilter()} type="danger" icon={<ReloadOutlined />}>Reset Filter</Button>
                            <Button onClick={() => setShowFilter(true)} icon={<FilterOutlined />}>Filter</Button>
                            <CSVLink filename="agent_wise_report.csv" headers={headers} data={data || []}>
                                <Button disabled={data?.length === 0}>Download</Button>
                            </CSVLink>
                        </Space>

                    </div>
                </>}
                columns={columns}
                dataSource={data}
                pagination={false}
                loading={loading}
                size="default"
                bordered
                rowKey={'Agent'}
                scroll={{
                    x: "calc(700px + 50%)",
                    y: 460,
                }}
            />
            {/* </Card> */}

            <AgentWiseFilter
                form={form}
                resetFilter={resetFilter}
                visible={showFilter}
                fetchReport={fetchReport}
                // onChange={onChange}
                // onDateChange={onDateChange}
                filterType={filterType}
                setFilterType={setFilterType}
                onPickerChange={onPickerChange}
                // selectedType={selectedType}
                // onSearch={onSearch}
                // month={month}
                queues={queues}
                setSelectQueue={setSelectQueue}
                // queueField={queueField}
                // setQueueField={setQueueField}
                // handleSelectChange={handleSelectChange}
                // dateFormat={dateFormat}
                // setselectedType={setselectedType}
                setVisible={setShowFilter}
            />

        </>
    );
};


export const AgentWiseFilter = ({ form, fetchReport, setSelectQueue, onPickerChange, filterType, setFilterType, queues, visible, setVisible }) => {

    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            okText="Submit"
            onOk={() => form.validateFields()
                .then(value => {
                    // dispatch(getTrunkPerHourReport({ date: filterDate, queue: queue }))
                    fetchReport()
                    setVisible(false)
                })
            }
            // okButtonProps={{
            //     loading: btnLoading,
            //     icon: <SaveOutlined />
            // }}
            title="Agent Wise Report Filter"
        >
            {/* <Spin spinning={btnLoading} indicator={<SyncOutlined spin />}> */}
            <Form
                form={form}
                layout="vertical"
            >
                <Form.Item name={'type'}>
                    <Select placeholder="Type" onChange={(val) => setFilterType(val)}>
                        <Select.Option value="date">Date</Select.Option>
                        <Select.Option value="month">Month</Select.Option>
                    </Select>
                </Form.Item>

                {filterType && <Form.Item name={'monthOrDate'}>
                    <DatePicker onChange={onPickerChange} picker={filterType} />
                </Form.Item>}

                <Form.Item name={"queue"}>
                    <Select
                        showSearch
                        placeholder="Select Queue"
                        style={{ width: '170px' }}
                        onChange={(e) => setSelectQueue(e)}
                    >
                        {queues.map((queue, index) => (
                            <Select.Option key={index} value={queue.name}>{queue.name}</Select.Option>
                        ))}
                    </Select>
                </Form.Item>
            </Form>
            {/* </Spin> */}
        </Modal>
    )
}


export default AgentWiseReport;