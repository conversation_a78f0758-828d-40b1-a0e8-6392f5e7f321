import { Redirect, Route, Switch, useLocation } from "react-router-dom";
import Dashboard from "../Pages/Dashboard";
import Users from "../Pages/Users";
import Agent from "../Pages/Agent";
import Queue from "../Pages/Queue";
import VisualIVR from "../Pages/VisualIVR";
import WorkCode from "../Pages/WorkCode";
import PauseReasons from "../Pages/PauseReasons";
import MediaFiles from "../Pages/MediaFiles";
import InboundRoutes from "../Pages/InboundRoutes";
import OutboundRoutes from "../Pages/OutboundRoutes";
import Settings from "../Pages/Settings";
import LoginReport from "../Pages/Reports/LoginReport";
import CallDetailRecords from "../Pages/Reports/CallDetailRecords";
import { Layout } from "antd";
import { useSelector } from "react-redux";
import { useState, useEffect } from "react";
import { capitalize } from "lodash";
import Role from "../Pages/Role";
import Permission from "../Pages/Permission";
import PauseReasonReport from "../Pages/Reports/PauseReasonReport";
import CallerHangup from "../Pages/Reports/CallerHangup";
import RingNoAnswer from "../Pages/Reports/RingNoAnswer";
import ExitEmpty from "../Pages/Reports/ExitEmpty";
import ExitWithTimeout from "../Pages/Reports/ExitWithTimeout";
import ConfiguredLoad from "../Pages/Reports/ConfiguredLoad";
import CompleteCaller from "../Pages/Reports/CompleteCaller";
import CompleteAgent from "../Pages/Reports/CompleteAgent";
import NotFound404 from "../Pages/404";
import Campaign from "../Pages/Campaign";
import CampaignNumbers from "./Campaign/CampaignNumbers";
import CampaignMonitor from "./Campaign/CampaignMonitor";
import Form from "../Pages/Form";
import { Scripts } from "../Pages/Scripts";
import { AgentCallReport } from "../Pages/Reports/AgentCallReport";
import { OutboundDispositionSummaryReport } from "../Pages/Reports/OutboundDispositionSummaryReport";
import { OutboundDispositionReport } from "../Pages/Reports/OutboundDispositionReport";
import { InboundDispositionSummary } from "../Pages/Reports/InboundDispositionSummary";
import { InboundDispositionReport } from "../Pages/Reports/InboundDispositionReport";
import { OutboundAgentSummary } from "../Pages/Reports/OutboundAgentSummary";
import { OutboundActivityReport } from "../Pages/Reports/OutboundActivityReport";
import { CustomNumbers } from "./Campaign/CustomNumbers";
import { AgentLoginReport } from "../Pages/Reports/AgentLoginReport";
import { CallDetailRecordReport } from "../Pages/Reports/CallDetailRecordReport";
import { MinutesOfMeeting } from "../Pages/Reports/MinutesOfMeeting";
import { AgentCallSummary } from "../Pages/Reports/AgentCallSummary";
import { FormDataReport } from "../Pages/Reports/FormDataReport";
import { InboundAgentSummary } from "../Pages/Reports/InboundAgentSummary";
import { RingNoAnswerAgentWiseSummaryReport } from "../Pages/Reports/RingNoAnswerAgentWiseSummaryReport";
import { RingNoAnswerQueueSummaryReport } from "../Pages/Reports/RingNoAnswerQueueSummaryReport";
import { ChannelOccupancy } from "../Pages/Reports/ChannelOccupancy";
import { HoldTimeReport } from "../Pages/Reports/HoldTimeReport";
import { BreakReport } from "../Pages/Reports/BreakReport";
import { CallPerAgentReport } from "../Pages/Reports/CallPerAgentReport";
import { TrunkPerHourReport } from "../Pages/Reports/TrunkPerHoursReport";
import Login2 from "./Login/Login2";
import { CampaignMonitoring } from "../Pages/CampaignMonitoring";
import { BulkRecordingDownloader } from "../Pages/Reports/BulkRecordingDownloader";
import { AbandonedCall } from "../Pages/Reports/AbandonedCall";
import { AbandonedCallHour } from "../Pages/Reports/AbandonedCallHour";
import { CIDLookUp } from "../Pages/CIDLookUp";
import { AbandonCallReportDifference } from "../Pages/Reports/AbandonCallReportDifference";
import { Supervisor } from "../Pages/Supervisor";
import { AddSupervisor } from "../Pages/add-supervisor";
import { EditSupervisor } from "../Pages/edit-supervisor";
import { AgentCallSummaryInbound } from "../Pages/Reports/AgentCallSummaryInbound";

import { VoiceMail } from "../Pages/VoiceMail";
import { CustomRouting } from "../Pages/CustomRouting";
import Bitrix from "../Pages/Bitrix";

import Ticker from "../Pages/Ticker";

// import MonthlyService from "../Pages/Reports/MonthlyService";
// import DailyCallTrend from "../Pages/Reports/DailyCallTrend";
import { ServiceRating } from "../Pages/ServiceRating";

import { AspireCustomFormDataReport } from "../Pages/Reports/AspireCustomFormDataReport";
import Login3 from "./Login/Login3";
import IvrSettings from "../Pages/IvrSettings";
import IvrFlow from "../Pages/IvrFlow";
import EditIvrFlow from "../Pages/EditIvrFlow";
import SummaryReport from "../Pages/Reports/SummaryReport";
import DtmfSetting from "../Pages/DtmfSetting";
// import { FormDetailsPage } from "../Pages/FormDetailsPage";
// import { FormDetailsPage2 } from "./Form/FormDetailsPage2";
import { SMSModuleAccessibility } from "../Pages/SMSModuleAccessibility";
import { EmailModuleAccessibility } from "../Pages/EmailModuleAccessibility";
import { SMSCategory } from "../Pages/SMSCategory";
import { SMSTemplate } from "../Pages/SMSTemplate";
import PrepaidSetting from "../Pages/Prepaid";
import EmailSetting from "../Pages/EmailSetting";
import CallingServer from "../Pages/CallingServer";
import Recording from "../Pages/Recording";
import AgentlessCampaign from "../Pages/AgentlessCampaign";
import Announcements from "../Pages/Announcements";
import { CallBackRequest } from "../Pages/Reports/CallBackRequest";
import QueueWiseReport from "../Pages/Reports/QueueWiseReport";
import MonthlyReportBykea from "../Pages/Reports/MonthlyReportBykea";
import OverallCallMetric from "../Pages/Reports/OverallCallMetric ";
import AgentStatus from "../Pages/Reports/AgentStatus";
import HourlyAbandonReport from "../Pages/Reports/HourlyAbandonReport";
import ServiceLevelReport from "../Pages/Reports/ServiceLevelReport";
import MonthlyReportMNP from "../Pages/Reports/MonthlyReportMNP";
import WorkCodeWiseReport from "../Pages/Reports/WorkCodeWiseReport";
import WorkCodeCountWiseReport from "../Pages/Reports/WorkCodeCountWise";
import AgentWiseReport from "../Pages/Reports/AgentWiseReport";
import CLIAbandonCallsReport from "../Pages/Reports/CLIAbandonCalls";

const { Content } = Layout;

const ContentComponent = () => {
    const User = useSelector((state) => state.User);

    return (
        <Content style={{ padding: User.loggedIn ? "24px" : 0 }}>
            <Switch>
                <Route exact path="/sign-in" component={Login2} />
                <PrivateRoute exact path="/">
                    <Dashboard />
                </PrivateRoute>
                <PrivateRoute path="/users">
                    <Users />
                </PrivateRoute>
                <PrivateRoute path="/agent">
                    <Agent />
                </PrivateRoute>
                <PrivateRoute path="/supervisor">
                    <Supervisor />
                </PrivateRoute>
                <PrivateRoute path="/add-supervisor">
                    <AddSupervisor />
                </PrivateRoute>
                <PrivateRoute path="/edit-supervisor/:id">
                    <EditSupervisor />
                </PrivateRoute>
                <PrivateRoute path="/queue">
                    <Queue />
                </PrivateRoute>
                <PrivateRoute path="/announcements">
                    <Announcements />
                </PrivateRoute>
                <PrivateRoute path="/ivr-flow">
                    <IvrFlow />
                </PrivateRoute>
                <PrivateRoute path="/visualIVR">
                    <VisualIVR />
                </PrivateRoute>
                <PrivateRoute path="/workCode">
                    <WorkCode />
                </PrivateRoute>
                <PrivateRoute path="/break">
                    <PauseReasons />
                </PrivateRoute>
                <PrivateRoute path="/serviceRating">
                    <ServiceRating />
                </PrivateRoute>
                <PrivateRoute path="/voiceMail">
                    <VoiceMail />
                </PrivateRoute>
                <PrivateRoute path="/ivr-settings">
                    <IvrSettings />
                </PrivateRoute>
                <PrivateRoute path="/dtmf-settings">
                    <DtmfSetting />
                </PrivateRoute>
                <PrivateRoute path="/customRouting">
                    <CustomRouting />
                </PrivateRoute>
                <PrivateRoute path="/mediaFiles">
                    <MediaFiles />
                </PrivateRoute>
                <PrivateRoute path="/inboundRoutes">
                    <InboundRoutes />
                </PrivateRoute>
                <PrivateRoute path="/outboundRoutes">
                    <OutboundRoutes />
                </PrivateRoute>
                <PrivateRoute path="/settings">
                    <Settings />
                </PrivateRoute>
                <PrivateRoute path="/module_accessibility">
                    <SMSModuleAccessibility />
                </PrivateRoute>
                <PrivateRoute path="/email_module_accessibility">
                    <EmailModuleAccessibility />
                </PrivateRoute>
                <PrivateRoute path="/sms_category">
                    <SMSCategory />
                </PrivateRoute>
                <PrivateRoute path="/sms_template">
                    <SMSTemplate />
                </PrivateRoute>
                <PrivateRoute path="/ticker">
                    <Ticker />
                </PrivateRoute>
                <PrivateRoute path="/ticker">
                    <Ticker />
                </PrivateRoute>
                <PrivateRoute path="/role">
                    <Role />
                </PrivateRoute>
                <PrivateRoute path="/permission">
                    <Permission />
                </PrivateRoute>
                <PrivateRoute exact path="/campaign">
                    <Campaign />
                </PrivateRoute>
                <PrivateRoute exact path="/monitoring">
                    <CampaignMonitoring />
                </PrivateRoute>
                <PrivateRoute path="/campaign/monitor/:id">
                    <CampaignMonitor />
                </PrivateRoute>
                <PrivateRoute path="/campaign/customNumber/:id">
                    <CustomNumbers />
                </PrivateRoute>
                <PrivateRoute path="/campaign/campaignNumber/:id">
                    <CampaignNumbers />
                </PrivateRoute>
                {/* <PrivateRoute path="/formdetails/:id">
                    <FormDetailsPage2 />
                </PrivateRoute> */}
                <PrivateRoute path="/campaign/:id">
                    <CampaignNumbers />
                </PrivateRoute>
                <PrivateRoute path="/formDataReport">
                    <FormDataReport />
                </PrivateRoute>
                <PrivateRoute path="/MergeCDRFormDataReport">
                    <AspireCustomFormDataReport />
                </PrivateRoute>
                <PrivateRoute path="/agentReport">
                    <LoginReport />
                </PrivateRoute>
                <PrivateRoute path="/cdrReport">
                    <CallDetailRecords />
                </PrivateRoute>
                <PrivateRoute path="/callDetailReport">
                    <CallDetailRecordReport />
                </PrivateRoute>
                <PrivateRoute path="/agentCallReport">
                    <AgentCallReport />
                </PrivateRoute>
                <PrivateRoute path="/outboundDispositionSummary">
                    <OutboundDispositionSummaryReport />
                </PrivateRoute>
                <PrivateRoute path="/outboundDisposition">
                    <OutboundDispositionReport />
                </PrivateRoute>
                <PrivateRoute path="/inboundDispositionSummary">
                    <InboundDispositionSummary />
                </PrivateRoute>
                <PrivateRoute path="/inboundDisposition">
                    <InboundDispositionReport />
                </PrivateRoute>
                <PrivateRoute path="/outboundAgentSummary">
                    <OutboundAgentSummary />
                </PrivateRoute>
                <PrivateRoute path="/inboundAgentSummary">
                    <InboundAgentSummary />
                </PrivateRoute>
                <PrivateRoute path="/outboundActivity">
                    <OutboundActivityReport />
                </PrivateRoute>
                <PrivateRoute path="/PauseReasonReport">
                    <PauseReasonReport />
                </PrivateRoute>
                <PrivateRoute path="/CallerHangup">
                    <CallerHangup />
                </PrivateRoute>
                <PrivateRoute path="/RingNoAnswer">
                    <RingNoAnswer />
                </PrivateRoute>
                <PrivateRoute path="/summary-report">
                    <SummaryReport />
                </PrivateRoute>
                <PrivateRoute path="/ExitEmpty">
                    <ExitEmpty />
                </PrivateRoute>
                <PrivateRoute path="/ExitWithTimeout">
                    <ExitWithTimeout />
                </PrivateRoute>
                <PrivateRoute path="/configureLoad">
                    <ConfiguredLoad />
                </PrivateRoute>
                <PrivateRoute path="/completeCaller">
                    <CompleteCaller />
                </PrivateRoute>
                <PrivateRoute path="/completeAgent">
                    <CompleteAgent />
                </PrivateRoute>
                <PrivateRoute path="/form">
                    <Form />
                </PrivateRoute>
                <PrivateRoute path="/script">
                    <Scripts />
                </PrivateRoute>
                <PrivateRoute path="/edit-ivrflow/:id">
                    <EditIvrFlow />
                </PrivateRoute>
                <PrivateRoute path="/dailyLoginReport">
                    <AgentLoginReport />
                </PrivateRoute>
                <PrivateRoute path="/minutesOfMeeting">
                    <MinutesOfMeeting />
                </PrivateRoute>
                <PrivateRoute path="/agentCallSummary-outbound">
                    <AgentCallSummary />
                </PrivateRoute>

                <PrivateRoute path="/agentCallSummary-inbound">
                    <AgentCallSummaryInbound />
                </PrivateRoute>

                <PrivateRoute path={"/RingNoAnswerAgentWiseSummaryReport"}>
                    <RingNoAnswerAgentWiseSummaryReport />
                </PrivateRoute>
                <PrivateRoute path={"/RingNoAnswerQueueSummaryReport"}>
                    <RingNoAnswerQueueSummaryReport />
                </PrivateRoute>
                <PrivateRoute path={"/ChannelOccupancy"}>
                    <ChannelOccupancy />
                </PrivateRoute>
                <PrivateRoute path={"/holdTimeReport"}>
                    <HoldTimeReport />
                </PrivateRoute>
                <PrivateRoute path={"/breakReport"}>
                    <BreakReport />
                </PrivateRoute>
                <PrivateRoute path={"/callPerAgentReport"}>
                    <CallPerAgentReport />
                </PrivateRoute>
                <PrivateRoute path={"/trunkPerHour"}>
                    <TrunkPerHourReport />
                </PrivateRoute>
                <PrivateRoute path={"/bulkRecordingDownload"}>
                    <BulkRecordingDownloader />
                </PrivateRoute>
                <PrivateRoute path={"/login2"}>
                    <Login2 />
                </PrivateRoute>
                <PrivateRoute path={"/abandonCall"}>
                    <AbandonedCall />
                </PrivateRoute>
                <PrivateRoute path={"/abandonCallHour"}>
                    <AbandonedCallHour />
                </PrivateRoute>
                <PrivateRoute path={"/AbandonCallReportDifference"}>
                    <AbandonCallReportDifference />
                </PrivateRoute>
                <PrivateRoute path={"/cid-lookup"}>
                    <CIDLookUp />
                </PrivateRoute>
                <PrivateRoute path={"/bitrix"}>
                    <Bitrix />
                </PrivateRoute>
                <PrivateRoute path={"/prepaidSetting"}>
                    <PrepaidSetting />
                </PrivateRoute>
                <PrivateRoute path={"/emailSetting"}>
                    <EmailSetting />
                </PrivateRoute>
                <PrivateRoute path={"/servers"}>
                    <CallingServer />
                </PrivateRoute>
                <PrivateRoute path={"/recordings"}>
                    <Recording />
                </PrivateRoute>
                <PrivateRoute path={"/campaigns"}>
                    < AgentlessCampaign />
                </PrivateRoute>
                <PrivateRoute path={"/callback-report"}>
                    <CallBackRequest />
                </PrivateRoute>
                <PrivateRoute path={"/QueueWiseReport"}>
                    <QueueWiseReport />
                </PrivateRoute>
                <PrivateRoute path={"/MonthlyReportBykea"}>
                    <MonthlyReportBykea />
                </PrivateRoute>

                {/* mnp report */}
                <PrivateRoute path={"/OverallCallMetric"}>
                    <OverallCallMetric />
                </PrivateRoute>
                <PrivateRoute path={"/agentStatus"}>
                    <AgentStatus />
                </PrivateRoute>
                <PrivateRoute path={"/hourlyAbandonReport"}>
                    <HourlyAbandonReport />
                </PrivateRoute>
                <PrivateRoute path={"/serviceLevelReport"}>
                    <ServiceLevelReport />
                </PrivateRoute>
                <PrivateRoute path={"/monthlyCallQueue"}>
                    <MonthlyReportMNP />
                </PrivateRoute>
                <PrivateRoute path={"/WorkCodeWiseReport"}>
                    <WorkCodeWiseReport />
                </PrivateRoute>
                <PrivateRoute path={"/WorkCodeCountWiseReport"}>
                    <WorkCodeCountWiseReport />
                </PrivateRoute>
                <PrivateRoute path={"/AgentWiseReport"}>
                    <AgentWiseReport />
                </PrivateRoute>
                <PrivateRoute path={"/CLIAbandonCallsReport"}>
                    <CLIAbandonCallsReport />
                </PrivateRoute>

                <PrivateRoute>
                    <NotFound404 />
                </PrivateRoute>
            </Switch>
        </Content>
    );
};



// A wrapper for <Route> that redirects to the login
// screen if you're not yet authenticated.
function PrivateRoute({ children, ...rest }) {
    const user = useSelector((state) => state.User);
    const [path] = useState("/sign-in");
    return (
        <Route
            {...rest}
            render={({ location }) =>
                user.loggedIn ? (
                    children
                ) : (
                    <Redirect
                        to={{
                            pathname: path,
                            state: { from: location },
                        }}
                    />
                )
            }
        />
    );
}

export default ContentComponent;