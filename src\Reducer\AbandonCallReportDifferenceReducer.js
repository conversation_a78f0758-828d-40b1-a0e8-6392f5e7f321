
const initialState = {
    data: [],
    isLoading: false,
    errMess: ''
}

export const AbandonCallReportDifferenceReducer = (state = initialState, action) => {

    switch (action.type) {
        default:
            return state
        case "AbadonedCallReportDifferenceLoading":
            return { ...state, isLoading: true }
        case "AbadonedCallReportDifferenceSuccess":
            return { ...state, isLoading: false, data: action.payload }
        case "AbadonedCallReportDifferenceFailed":
            return { ...state, isLoading: false, errMess: action.payload }
        case "AbadonedCallReportDifferenceReset":
            return { ...state, data: [] }
    }
}