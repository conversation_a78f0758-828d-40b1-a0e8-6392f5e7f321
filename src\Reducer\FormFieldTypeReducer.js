import * as ActionTypes from "../Constants/FormFieldTypeConstants"

const initial = {
    fieldTypes: [],
    message: false,
    errMess: false,
    isLoading: false
}

export const FormFieldTypeReducer = (state = initial, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.FORM_FIELD_TYPES_LOADING:
            return {...state, isLoading: true, message: false, errMess: false}
        case ActionTypes.FORM_FIELD_TYPES_SUCCESS:
            return {...state, isLoading: false, fieldTypes: action.payload, errMess: false}
        case ActionTypes.FORM_FIELD_TYPES_FAILED:
            return {...state, isLoading: false, message: false, errMess: action.payload}
    }
}