import * as ActionTypes from "../Constants/QueueConstants"
import apiClient from "../Shared/apiClient";
import { getEnums, queue } from "../Endpoints/QueueRoutes";
import { logoutUser } from "./UserActions";
import { handleError } from "../Shared/handleError";

export const getQueues = () => dispatch => {
    dispatch(loading())
    apiClient.get(queue).then(response => {
        dispatch(queuesSuccess(response.data))
    }).catch(error => dispatch(queuesFailed(handleError(error))))
}

export const fetchAllQueues = () => dispatch => {
    dispatch(loading())
    apiClient.get("api/getAllQueues").then(response => {
        dispatch(queuesAllSuccess(response.data))
    }).catch(error => dispatch(queuesFailed(handleError(error))))
}

export const createQueue = values => dispatch => {
    dispatch(loading())
    apiClient.post(queue, values).then(response => {
        dispatch(createSuccess(response.data))
        dispatch(fetchAllQueues())
    }).catch(error => dispatch(queuesFailed(handleError(error))))
}

export const updateQueue = (id, values) => dispatch => {
    dispatch(loading())
    apiClient.patch(`${queue}/${id}`, values).then(response => {
        dispatch(updateSuccess(response.data))
        dispatch(fetchAllQueues())
    }).catch(error => dispatch(queuesFailed(handleError(error))))
}

export const deleteQueue = obj => dispatch => {
    dispatch(loading())
    apiClient.delete(`${queue}/${obj}`).then(response => {
        dispatch(deleteSuccess(response.data))
        dispatch(fetchAllQueues())
    }).catch(error => dispatch(queuesFailed(handleError(error))))
}

export const getEnumValues = () => dispatch => {
    dispatch(loading())
    apiClient.get(getEnums).then(response => dispatch(enumSuccess(response.data))).catch(error => dispatch(queuesFailed(handleError(error))))
}

const loading = () => ({
    type: ActionTypes.QUEUE_LOADING
})

const enumSuccess = enums => ({
    type: ActionTypes.ENUM_SUCCESS,
    payload: enums
})

const enumFailed = error => ({
    type: ActionTypes.ENUM_FAILED,
    payload: error
})

const queuesAllSuccess = queues => ({
    type: "GET_ALL_QUEUES_SUCCESS",
    payload: queues
})

const queuesSuccess = queues => ({
    type: ActionTypes.QUEUE_SUCCESS,
    payload: queues
})

const queuesFailed = error => ({
    type: ActionTypes.QUEUE_FAILED,
    payload: error
})

const updateSuccess = message => ({
    type: ActionTypes.UPDATE_SUCCESS,
    payload: message
})

const updateFailed = error => ({
    type: ActionTypes.UPDATE_FAILED,
    payload: error
})

const deleteSuccess = message => ({
    type: ActionTypes.DELETE_SUCCESS,
    payload: message
})

const deleteFailed = error => ({
    type: ActionTypes.DELETE_FAILED,
    payload: error
})

const createSuccess = message => ({
    type: ActionTypes.CREATE_SUCCESS,
    payload: message
})

const createFailed = error => ({
    type: ActionTypes.CREATE_FAILED,
    payload: error
})