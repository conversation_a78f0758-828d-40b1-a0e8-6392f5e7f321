
import * as ActionTypes from "../Constants/AspireCustomFormDataReportConstants"
import apiClient from "../Shared/apiClient";
import {handleError} from "../Shared/handleError";

// export const getFormDataPaginated = (page = 1) => dispatch => {
//     dispatch(formDataLoading())
//     apiClient.post(`/api/report/aspireCustomFormData?page=${page}`).then(r => dispatch(formDataSuccess(r.data))).catch(e => dispatch(formDataFailed(handleError(e))))
// }


export const getFormDataReportFiltered = (data, page = 1,pageSize=15) => dispatch => {
    dispatch(formDataLoading())
    apiClient.post(`/api/report/aspireCustomFormData?page=${page}&record=${pageSize}`, data).then(r => dispatch(formDataSuccess(r.data))).catch(e => dispatch(formDataFailed(handleError(e))))
    // apiClient.post(`/api/report/aspireCustomReportFiltered?page=${page}`, data).then(r => dispatch(formDataSuccess(r.data))).catch(e => dispatch(formDataFailed(handleError(e))))
}

const formDataLoading = () => ({
    type: ActionTypes.FORM_DATA_REPORT_LOADING
})

const formDataSuccess = data => ({
    type: ActionTypes.FORM_DATA_REPORT_SUCCESS,
    payload: data
})

const formDataFailed = err => ({
    type: ActionTypes.FORM_DATA_REPORT_FAILED,
    payload: err
})