import { Button, DatePicker, Form, Input, Modal, Select, Space, Spin, Table } from "antd";
import { DownloadOutlined, FilterOutlined, ReloadOutlined, SearchOutlined, SettingOutlined, SyncOutlined } from "@ant-design/icons";
import { CSVLink } from "react-csv";
import React, { useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getFilteredInboundDisposition, getInboundDisposition, inbounddispositionReset } from "../../Actions/InboundDispositionActions";
import { getCallStatuses } from "../../Actions/CallStatusActions";
import { getAgent } from "../../Actions/AgentActions";
import { getFilteredOutboundDisposition, getOutboundDisposition } from "../../Actions/OutboundDispositionActions";
import { useState } from "react";
import Highlighter from "react-highlight-words";
import apiClient from "../../Shared/apiClient";

export const InboundDispositionReport = () => {

    const dispatch = useDispatch()
    const inboundDispositionState = useSelector(state => state.InboundDispositionReducer)
    const [filterVisible, setFilterVisible] = useState(false)
    const [resetFilter, setResetFilter] = useState(false)
    const [filterValues, setFilterValues] = useState(null)
    const [form] = Form.useForm()

    const resetFormFilter = () => {
        dispatch(inbounddispositionReset())
        form.resetFields()
        setResetFilter(true)
    }

    // useEffect(() => dispatch(getInboundDisposition()), [])

    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const searchInput = useRef()

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }

    const columns = [
        {
            title: 'Unique ID',
            dataIndex: 'uniqueid',
            key: 'uniqueid'
        },
        {
            title: 'Source',
            dataIndex: 'src',
            key: 'src',
            ...getColumnSearchProps('src')
        },
        {
            title: 'Agent',
            dataIndex: 'agent',
            key: 'agent',
            ...getColumnSearchProps('agent')
        },
        {
            title: 'Date Time',
            dataIndex: 'date_time',
            key: 'date_time'
        },
        {
            title: 'Call Status',
            dataIndex: 'call_status',
            key: 'call_status',
            ...getColumnSearchProps('call_status')
        },
        {
            title: 'Workcode',
            dataIndex: 'workcode',
            key: 'workcode',
            ...getColumnSearchProps('workcode')
        }
    ]


    const exportCsv = () => {
        // setLoading(true);
        apiClient.post('/api/report/export-inbound-disposition',
            {
                // "range": filterValues.range,
                ...filterValues
            },
            { responseType: 'blob' }).then((response) => {
                const type = response.headers['content-type']; const blob = new Blob([response.data], { type: type }); const link = document.createElement('a'); link.href = window.URL.createObjectURL(blob); link.download = 'inbound-disposition.csv'; link.click();
            }).catch((e) => {
                // setLoading(false); 
                console.log("Export error", e);
            });
    }


    return (
        <>

            <Table
                loading={{ spinning: inboundDispositionState.isLoading, indicator: <SyncOutlined spin /> }}
                dataSource={inboundDispositionState.data}
                columns={columns}
                title={data => <>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        Inbound Disposition Report
                        <Space>
                            <Button danger type="primary" onClick={() => resetFormFilter()} icon={<ReloadOutlined />}>Reset Filter</Button>
                            <Button onClick={() => setFilterVisible(true)} icon={<FilterOutlined />}>Filter</Button>
                            {/* <CSVLink data={data} filename="InboundDispositionSummary.csv">
                                <Button icon={<DownloadOutlined />} type="primary" disabled={inboundDispositionState.data.length == 0}>
                                    Download
                                </Button>
                            </CSVLink> */}
                            <Button onClick={() => { exportCsv() }} disabled={inboundDispositionState.data.length == 0} type={"primary"} target="_blank" icon={<DownloadOutlined />}>
                                Download
                            </Button>
                        </Space>
                    </div>
                </>}
            />
            <InboundDispositionFilter setFilterValues={setFilterValues} form={form} buttonLoading={inboundDispositionState.isLoading} resetField={resetFilter} visible={filterVisible} setVisible={setFilterVisible} />
        </>
    )
}

const InboundDispositionFilter = ({ setFilterValues, visible, form, setVisible, resetField, buttonLoading }) => {

    const agentState = useSelector(state => state.AgentReducer)
    const callState = useSelector(state => state.CallStatusReducer)
    const [queues, setQueues] = useState([])

    const dispatch = useDispatch()

    useEffect(() => {
        dispatch(getCallStatuses())
        dispatch(getAgent())
        console.log("agent State", agentState)
    }, [])

    useEffect(() => form.resetFields(), [resetField])
    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data), console.log('queues', queues))
    }, [])
    return (
        // <Spin spinning={agentState.isLoading || callState.isLoading} indicator={<SyncOutlined />}>
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            title="Inbound disposition filter"
            size="small"
            onOk={() => {
                form.validateFields()
                    .then((data) => {
                        dispatch(getFilteredInboundDisposition(data))
                        setFilterValues(data)
                    }
                    )
                    .catch(e => console.log(e))
                setVisible(false)
            }
            }
            okText="Submit"
            oKButtonProps={{
                loading: buttonLoading
            }}
        >
            <Form
                layout="vertical"
                form={form}
            >
                <Form.Item name="range" label="Date Range">
                    <DatePicker.RangePicker style={{ width: '100%' }} />
                </Form.Item>
                <Form.Item name="agent" label="Agent">
                    <Select>
                        {agentState.users && agentState.users.map((value, index) => <Select.Option value={value.name} key={value.username}>
                            {value.name}
                        </Select.Option>)}
                    </Select>
                </Form.Item>
                {/* <Form.Item name="dst" label="Destination">
                    <Input />
                </Form.Item> */}
                <Form.Item name="call_status" label="Call Status">
                    <Select>
                        {callState.statuses && callState.statuses.map((value, index) => <Select.Option key={index} value={value.disposition}>{value.disposition}</Select.Option>)}
                    </Select>
                </Form.Item>
                <Form.Item name="queue" label="Queue" >
                    <Select placeholder="queues" style={{ width: '50%' }}>
                        {queues.map((queue, index) => <Select.Option key={index} value={queue.name}>
                            {queue.name}
                        </Select.Option>)}
                    </Select>
                </Form.Item>
            </Form>
        </Modal>
        // </Spin>
    )
}