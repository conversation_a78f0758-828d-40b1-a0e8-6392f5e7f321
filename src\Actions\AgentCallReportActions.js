import * as ActionTypes from "../Constants/AgentCallReportConstants"
import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";

export const getAgentCallReport = (type = 'outbound') => dispatch => {
    dispatch(agentCallReportLoading())
    apiClient.post(`/api/report/agent-call`, type).then(r => dispatch(agentCallReportSuccess(r.data))).catch(e => dispatch(agentCallReportFailed(handleError(e))))
}

export const agentCallSummary = () => dispatch => {
    dispatch(agentCallReportLoading())
    apiClient('/api/agent/agentCallSummary').then(r => dispatch(agentCallReportSuccess(r.data))).catch(err => dispatch(agentCallReportFailed(handleError(err.data))))
}

export const agentCallSummaryInbound = () => dispatch => {
    dispatch(agentCallReportLoading())
    apiClient('/api/agent/agentCallSummaryInbound').then(r => dispatch(agentCallReportSuccess(r.data))).catch(err => dispatch(agentCallReportFailed(handleError(err.data))))
}


export const agentCallSummaryFiltered = val => dispatch => {
    dispatch(agentCallReportLoading())
    apiClient.post('/api/agent/agentCallSummary/filtered', val).then(r => dispatch(agentCallReportSuccess(r.data))).catch(err => dispatch(agentCallReportFailed(handleError(err.data))))
}
export const agentCallSummaryFilteredOutbound = val => dispatch => {
    dispatch(agentCallReportLoading())
    apiClient.post('/api/agent/agentCallSummary/filtered', val).then(r => dispatch(agentCallReportSuccessOutbound(r.data))).catch(err => dispatch(agentCallReportFailed(handleError(err.data))))
}

export const agentCallSummaryFilteredInbound = val => dispatch => {
    dispatch(agentCallReportLoading())
    apiClient.post('/api/agent/agentCallSummaryInbound/filtered', val).then(r => dispatch(agentCallReportSuccess(r.data))).catch(err => dispatch(agentCallReportFailed(handleError(err.data))))
}

export const getAgentCallFilteredReport = values => dispatch => {
    console.log("ss", values)
    dispatch(agentCallReportLoading())
    apiClient.post(`/api/report/agent-call-filtered`, values).then(r => dispatch(agentCallReportSuccess(r.data))).catch(e => dispatch(agentCallReportFailed(handleError(e))))
}
export const agentCallSummaryReset = () => dispatch => {
    dispatch(agentCallReportReset())

}

const agentCallReportLoading = () => ({
    type: ActionTypes.AGENT_CALL_REPORT_LOADING
})

const agentCallReportSuccess = data => ({
    type: ActionTypes.AGENT_CALL_REPORT_SUCCESS,
    payload: data
})
const agentCallReportSuccessOutbound = data => ({
    type: ActionTypes.AGENT_CALL_REPORT_SUCCESS_OUTBOUND,
    payload: data
})

const agentCallReportFailed = err => ({
    type: ActionTypes.AGENT_CALL_REPORT_FAILED,
    payload: err
})
const agentCallReportReset = () => ({
    type: ActionTypes.AGENT_CALL_REPORT_RESET,

})