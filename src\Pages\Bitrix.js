import React, { useEffect, useState } from 'react'
import { Button, Card, Col, Form, Input, Row, Spin, Switch } from 'antd'
import apiClient from '../Shared/apiClient';

function Bitrix() {
    let [form] = Form.useForm();
    const [loading, setLoading] = useState(false)
    const [record, setRecord] = useState()

    const fetchData = () => {
        setLoading(true)
        apiClient.get('api/bitrix').then((res) => {
            
            setRecord(res.data)
            setTimeout(()=>{
                setLoading(false)
            },1500)
        })
    }

    useEffect(() => {
        fetchData()
    }, [])

    useEffect(() => {
        if (record) {
            form.setFieldsValue({
                id: record['id'],
                api_key: record['api_key'],
                is_enable: record['is_enable'] === 1 ? true : false,
            })
        }
    }, [record])

    const onFinish = (values) => {
        const { id } = record;
        apiClient.put(`api/bitrix/${id}`, { ...values, is_enable: values.is_enable ? 1 : 0 }).then((res) => {
            fetchData()
        })
    }

    return (
        <Card>
            <Spin spinning={loading}>
                <Form
                    form={form}
                    onFinish={onFinish}
                >
                    <Row gutter={[5, 5]}>
                        <Col key={'3'} span={14}>
                            <Form.Item
                                label={'Api Key'}
                                name={'api_key'}
                                key={'api_key'}
                            >
                                <Input />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={[5, 5]}>
                        <Col key={'3'} span={14}>
                            <Form.Item
                                label={'Is Enable'}
                                name={'is_enable'}
                                key={'is_enable'}
                                valuePropName={record?.is_enable === 1 && "checked"}
                            >
                                <Switch />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Form.Item>
                        <Button type="primary" htmlType="submit">
                            Update
                        </Button>
                    </Form.Item>
                </Form>
            </Spin>
        </Card>
    )
}

export default Bitrix