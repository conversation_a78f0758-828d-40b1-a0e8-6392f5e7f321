
import * as ActionTypes from "../Constants/InboundDispositionConstants"

const initialState = {
    data: [],
    isLoading: false,
    errMess: ''
}

export const InboundDispositionReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.INBOUND_DISPOSITION_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.INBOUND_DISPOSITION_SUCCESS:
            return { ...state, isLoading: false, data: action.payload }
        case ActionTypes.INBOUND_DISPOSITION_FAILED:
            return { ...state, isLoading: false, errMess: action.payload }
        case ActionTypes.INBOUND_DISPOSITION_RESET:
            return { ...state, data: [] }
    }
}