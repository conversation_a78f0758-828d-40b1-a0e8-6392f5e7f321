import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, DatePicker, Form, Select, Table, Input, Space, Modal } from "antd";
import apiClient from "../../Shared/apiClient";
import { CSVLink } from "react-csv";

import {
    FilterOutlined,
    ReloadOutlined,
    SearchOutlined
} from "@ant-design/icons";
import Highlighter from "react-highlight-words";



const exportHeaders = [
    { label: 'Date', key: 'Date(time)' },
    { label: 'Total Incoming Calls', key: 'totalInbounCalls' },
    { label: 'Total Answered Calls', key: 'totalAnswerCalls' },
    { label: 'Abandoned/Lost Calls', key: 'totalAbandonCalls' },
    { label: 'Customer Service Factor', key: 'CustomerServiceFactor' },
    { label: 'Answered Calls Percentage', key: 'percentageOfAnsweredCalls' },
]

const MonthlyReportMNP = () => {
    const [form] = Form.useForm();
    const [data, setData] = useState([]);
    const [month, setMonth] = useState();
    const [fetchReportCheck, setFetchReportCheck] = useState(true);
    const [loading, setLoading] = useState(false);
    const [queues, setQueues] = useState([])
    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const searchInput = useRef();



    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }
    const [showFilter, setShowFilter] = useState(false)
    const [resetFilter, setResetFilter] = useState(false)

    const resetFormFilter = () => {
        // dispatch(getTrunkPerHourReport())
        // dispatch(trunkPerHourReset())
        // console.log(new Date().toLocaleString() + '')
        setData([])
        form.resetFields()
        setResetFilter(true)
    }

    const columns = [
        {
            title: "Date",
            dataIndex: "Date(time)",
            key: "Date(time)",
            //width: 150,
        },
        {
            title: "Total Incoming Calls",
            dataIndex: "totalInbounCalls",
            key: "totalInbounCalls",
            ...getColumnSearchProps('totalInbounCalls')
            //width: 150,
        },
        {
            title: "Total Answered Calls",
            dataIndex: "totalAnswerCalls",
            key: "totalAnswerCalls",
            ...getColumnSearchProps('totalAnswerCalls')
            //width: 150,
        },
        {
            title: "Abandoned/Lost Calls",
            dataIndex: "totalAbandonCalls",
            key: "totalAbandonCalls",
            ...getColumnSearchProps('totalAbandonCalls')
            //width: 150,
        },
        {
            title: "Customer Service Factor",
            dataIndex: "CustomerServiceFactor",
            key: "CustomerServiceFactor",
            //width: 150,
            ...getColumnSearchProps('CustomerServiceFactor'),
            render: (v) => `${v}%`
        },
        {
            title: "Answered Calls Percentage",
            dataIndex: "percentageOfAnsweredCalls",
            key: "percentageOfAnsweredCalls",
            ...getColumnSearchProps('percentageOfAnsweredCalls'),
            //width: 150,
            render: (v) => `${v}%`,
        },
    ];

    const onChange = (date, dateString) => {
        setMonth(dateString);
        setFetchReportCheck(false);
    };

    useEffect(() => {
        apiClient.get('/api/queue').then(res => setQueues(res.data)).catch(err => console.log(err.response))
    }, []);


    const fetchReport = (queue) => {
        setLoading(true);
        apiClient
            .post(`/api/report/getCallQueueSummaryReport`, { month: month, queue })
            .then((resp) => {
                setData(resp.data);
                setLoading(false);
                setFetchReportCheck(true);
                form.resetFields();
            });
    };

    return (
        <>
            {/* <Card
             
                style={{ background: "#fff", borderRadius: "2px", padding: 10 }}
            // extra={
            //     <Form
            //         form={form}
            //         layout="inline"
            //         size="large"
            //         style={{ marginTop: "3px" }}
            //         onFinish={fetchReport}
            //     >
            //         <Form.Item name={'queue'} rules={[{ required: true, message: 'please select queue first' }]}>
            //             <Select placeholder="Select Queue.">
            //                 {queues.length > 0 && queues.map((queue) => <Select.Option key={queue.name} value={queue.name}>{queue.name}</Select.Option>)}
            //             </Select>
            //         </Form.Item>
            //         <Form.Item name={"picker"}>
            //             <DatePicker
            //                 onChange={onChange}
            //                 value={month}
            //                 picker="month"
            //             />
            //         </Form.Item>
            //         <Form.Item>
            //             <Button
            //                 size="large"
            //                 htmlType="submit"
            //                 // onClick={fetchReport}
            //                 disabled={fetchReportCheck}
            //             >
            //                 Fetch Report
            //             </Button>
            //         </Form.Item>
            //         <Form.Item>
            //             <CSVLink filename="Call Queue Summary Monthly.csv" data={data} headers={exportHeaders}>
            //                 <Button disabled={data.length === 0}>Export Report</Button>
            //             </CSVLink>
            //         </Form.Item>
            //     </Form>
            // }
            > */}
            <Table
                title={data => <>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        {`Call Queue Summary Monthly`}
                        <Space>
                            <Button onClick={() => resetFormFilter()} type="danger" icon={<ReloadOutlined />}>Reset Filter</Button>
                            <Button onClick={() => setShowFilter(true)} icon={<FilterOutlined />}>Filter</Button>
                            <CSVLink filename="Call Queue Summary Monthly.csv" data={data} headers={exportHeaders}>
                                <Button disabled={data.length === 0}>Download</Button>
                            </CSVLink>
                        </Space>

                    </div>
                </>}
                columns={columns}
                dataSource={data}
                pagination={false}
                loading={loading}
                bordered
                size="middle"
                scroll={{
                    x: "calc(600px + 50%)",
                    y: 540,
                }}
            />
            {/* </Card> */}
            <MonthlyReportFilter
                form={form}
                resetFilter={resetFilter}
                visible={showFilter}
                fetchReport={fetchReport}
                onChange={onChange}
                // onDateChange={onDateChange}
                // selectedQueue={selectedQueue}
                // selectedType={selectedType}
                // onSearch={onSearch}
                month={month}
                queues={queues}

                // setselectedType={setselectedType}
                setVisible={setShowFilter} />
        </>
    );
};


export const MonthlyReportFilter = ({ form, fetchReport, month, queues, onChange, visible, setVisible }) => {

    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            okText="Submit"
            onOk={() => form.validateFields()
                .then(value => {
                    // dispatch(getTrunkPerHourReport({ date: filterDate, queue: queue }))
                    fetchReport(value.queue)
                    setVisible(false)
                })
            }
            // okButtonProps={{
            //     loading: btnLoading,
            //     icon: <SaveOutlined />
            // }}
            title="Monthly Report Filter"
        >
            {/* <Spin spinning={btnLoading} indicator={<SyncOutlined spin />}> */}
            <Form
                form={form}
                layout="vertical"
            >
                <Form.Item name={'queue'} rules={[{ required: true, message: 'please select queue first' }]}>
                    <Select placeholder="Select Queue.">
                        {queues.length > 0 && queues.map((queue) => <Select.Option key={queue.name} value={queue.name}>{queue.name}</Select.Option>)}
                    </Select>
                </Form.Item>
                <Form.Item name={"picker"}>
                    <DatePicker
                        style={{ width: "100%" }}
                        onChange={onChange}
                        value={month}
                        picker="month"
                    />
                </Form.Item>
            </Form>
            {/* </Spin> */}
        </Modal>
    )
}


export default MonthlyReportMNP;