import { Form, Input, Modal, Select, Spin } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { getQueues } from "../../Actions/QueueActions";
import { useState, useEffect } from 'react'

const EditInboundRoutes = ({ visible, onCreate, record, setVisible }) => {
    const [form] = Form.useForm()
    const [showQueue, setShowQueue] = useState(false)
    const [showIVR, setShowIVR] = useState(false)
    const dispatch = useDispatch()
    const QueueState = useSelector(state => state.QueueReducer)

    useEffect(() => {
        console.log(record)
        form.setFieldsValue(record)
        // setShowAgentFields(record ? record.type !== "Normal" : false)
        setShowQueue(record ? record.module_type === "Queue" : false)
        setShowIVR(record ? record.module_type === "IVR" : false)
    }, [record])

    const onSelectChange = (option) => {
        if (option === "IVR") {
            dispatch(getQueues())
            setShowQueue(false)
            setShowIVR(true)
        }
        else if (option === "Queue") {
            dispatch(getQueues())
            setShowQueue(true)
            setShowIVR(false)
        }
    }

    return (
        <Modal
            visible={visible}
            title={`Edit: ${record?.name}`}
            okText="Update"
            cancelText="Cancel"
            closable={true}
            onCancel={() => {
                setVisible(false)
            }}
            onOk={() => {
                form
                    .validateFields()
                    .then((values) => {
                        form.resetFields();
                        onCreate(values);
                    })
                    .catch((info) => {
                        console.log('Validate Failed:', info);
                    })
            }}
        >
            <Spin spinning={QueueState.isLoading}>
                <Form
                    form={form}
                    layout="vertical"
                    name="form_in_modal"
                >
                    <Form.Item
                        name="number"
                        label="Number"
                        rules={[
                            {
                                required: true,
                                message: 'Please input the Number',
                            },
                        ]}
                    >
                        <Input />
                    </Form.Item>

                    <Form.Item
                        name="module_type"
                        label="Type"
                        rules={[
                            {
                                required: true,
                                message: 'Please select the type',
                            },
                        ]}
                    >
                        <Select onSelect={onSelectChange}>
                            <Select.Option value="IVR" key="IVR">IVR</Select.Option>
                            <Select.Option value="Queue" key="Queue">Queue</Select.Option>
                        </Select>
                    </Form.Item>

                    {showQueue && <Form.Item
                        name="module_id"
                        label="Queue"
                        rules={[
                            {
                                required: true,
                                message: 'Please select the queue',
                            },
                        ]}
                    >
                        <Select>
                            {QueueState.queues.map((value, index) => (
                                <Select.Option value={value.name} key={value.name}>{value.name}</Select.Option>
                            ))}
                        </Select>
                    </Form.Item>}
                    {showIVR && <Form.Item
                        name="module_id"
                        label="IVR"
                        rules={[
                            {
                                required: true,
                                message: 'Please select the IVR',
                            },
                        ]}
                    >
                        <Select>
                            {QueueState.queues.map((value, index) => (
                                <Select.Option value={value.name} key={value.name}>{value.name}</Select.Option>
                            ))}
                        </Select>
                    </Form.Item>}
                </Form>
            </Spin>
        </Modal>
    )
}

export default EditInboundRoutes