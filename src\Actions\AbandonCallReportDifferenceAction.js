import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";
import { openNotificationWithIcon } from "../Shared/notification";


export const getData = filtered => dispatch => {
    dispatch({ type: "AbadonedCallReportDifferenceLoading" })
    apiClient.post("api/report/abandonCallDifferenceReport", filtered).then((r) => {
        dispatch(
            {
                type: "AbadonedCallReportDifferenceSuccess",
                payload: r?.data
            }
        )
    }
    ).catch(e => {
        // if (e?.response?.data?.message)
        //     return openNotificationWithIcon('error', e?.response?.data?.message)
        openNotificationWithIcon('error', handleError(e))
        dispatch(
            {
                type: "AbadonedCallReportDifferenceFailed",
                payload: handleError(e)
            }
        )
    }
    )
}

// export const getData = filtered => dispatch => {
//     dispatch({ type: "AbadonedCallReportDifferenceLoading" });

//     apiClient.post("api/report/abandonCallDifferenceReport", filtered)
//         .then(response => {
//             const data = response?.data;
//             console.log('API Response:', data);

//             if (Array.isArray(data)) {
//                 dispatch({ type: "AbadonedCallReportDifferenceSuccess", payload: data });
//             } else {
//                 console.error('Expected array but got:', data);
//                 dispatch({ type: "AbadonedCallReportDifferenceFailed", payload: 'Invalid data format' });
//             }
//         })
//         .catch(error => {
//             const errorMsg = handleError(error);
//             console.error('API Error:', errorMsg);
//             dispatch({ type: "AbadonedCallReportDifferenceFailed", payload: errorMsg });
//         });
// };

export const resetDifference = () => dispatch => {
    dispatch(reportDifferenceReducer())
}

const reportDifferenceReducer = () => ({
    type: "AbadonedCallReportDifferenceReset"
})