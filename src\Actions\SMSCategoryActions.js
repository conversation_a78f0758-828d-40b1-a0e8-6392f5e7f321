import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";


export const getCategory = () => dispatch => {
    dispatch({ type: 'LOADING_CATEGORY' })

    apiClient('api/sMSCategory').then(r => dispatch({ type: 'GET_CATEGORY', payload: r.data })).catch(e => dispatch({ type: 'FAILED_CATEGORY', payload: handleError(e) }))
}

export const saveCategory = data => dispatch => {
    dispatch({ type: 'LOADING_CATEGORY' })
    apiClient.post('api/sMSCategory', data).then(r => dispatch({ type: 'MUTATE_CATEGORY', payload: r.data })).catch(e => dispatch({ type: 'FAILED_CATEGORY', payload: handleError(e) }))
}

export const updateCategory = data => dispatch => {
    dispatch({ type: 'LOADING_CATEGORY' })
    apiClient.patch(`api/sMSCategory/${data.id}`, data).then(r => dispatch({ type: 'MUTATE_CATEGORY', payload: r.data })).catch(e => dispatch({ type: 'FAILED_CATEGORY', payload: handleError(e) }))
}

export const deleteCategory = data => dispatch => {
    dispatch({ type: 'LOADING_CATEGORY' })
    apiClient.delete(`api/sMSCategory/${data.id}`, data).then(r => dispatch({ type: 'MUTATE_CATEGORY', payload: r.data })).catch(e => dispatch({ type: 'FAILED_CATEGORY', payload: handleError(e) }))
}