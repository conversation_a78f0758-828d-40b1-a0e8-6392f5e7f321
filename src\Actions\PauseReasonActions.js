import * as ActionTypes from '../Constants/PauseReasonConstant'
import apiClient from "../Shared/apiClient";
import { PAUSEREASON } from "../Endpoints/PauseReasonRoutes";
import { logoutUser } from "./UserActions";

export const getPauseReason = () => dispatch => {
    dispatch(loading())
    apiClient.get(PAUSEREASON).then(response => {
        dispatch(success(response.data))
    }).catch(error => {
        console.log(error.response)
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(failedPauseReason(error.response))
        }
        else
            dispatch(failedPauseReason(error.message))
    })
}

export const createPauseReason = data => dispatch => {
    dispatch(loading())
    apiClient.post(PAUSEREASON, data).then(response => {
        dispatch(createSuccess(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(failedPauseReason(error.response))
        }
        else
            dispatch(failedPauseReason(error.message))
    })
}

export const deletePauseReason = id => dispatch => {
    dispatch(loading())
    apiClient.delete(`${PAUSEREASON}/${id}`).then(response => {
        dispatch(deleteSuccess(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(failedPauseReason(error.response))
        }
        else
            dispatch(failedPauseReason(error.message))
    })
}

export const updatePauseReason = (data, id) => dispatch => {
    dispatch(loading())
    apiClient.put(`${PAUSEREASON}/${id}`, data).then(response => {
        dispatch(updateSuccess(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(failedPauseReason(error.response))
        }
        else
            dispatch(failedPauseReason(error.message))
    })
}

export const loading = () => {
    return { type: ActionTypes.PAUESEREASON_LOADING }
}

export const success = data => {
    return { type: ActionTypes.PAUSEREASON_SUCCESS, payload: data }
}

export const createSuccess = data => {
    return { type: ActionTypes.PAUSEREASON_CREATE_SUCCESS, payload: data }
}

export const updateSuccess = data => {
    return { type: ActionTypes.PAUSEREASON_UPDATE_SUCCESS, payload: data }
}

export const deleteSuccess = data => {
    return { type: ActionTypes.PAUSEREASON_DELETE_SUCCESS, payload: data }
}

export const failedPauseReason = error => {
    return { type: ActionTypes.PAUESEREASON_FAILED, payload: error }
}