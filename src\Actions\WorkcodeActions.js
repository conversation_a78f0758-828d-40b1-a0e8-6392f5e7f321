import apiClient from "../Shared/apiClient";
import * as ActionTypes from "../Constants/WorkcodeConstant"
import { WORKCODE } from "../Endpoints/WorkcodeRoutes";

export const getWorkCode = () => dispatch => {
    dispatch(loading())
    apiClient.get(WORKCODE).then(response => {
        dispatch(success(response.data))
    }).catch(error => {
        if (error.response)
            dispatch(workcodeFailed(error.response))
        else
            dispatch(workcodeFailed(error.message))
    })

}

export const CreateWorkCode = obj => dispatch => {
    dispatch(loading())
    apiClient.post(WORKCODE, obj).then(response => {
        dispatch(createSuccess(response.data))
    }).catch(error => {
        if (error.response)
            dispatch(workcodeFailed(error.response))
        else
            dispatch(workcodeFailed(error.message))
    })
}

export const UpdateWorkCode = (obj, id) => dispatch => {
    dispatch(loading())
    apiClient.put(`${WORKCODE}/${id}`, obj).then(response => {
        dispatch(updateSuccess(response.data))
    }).catch(error => {
        console.log(error.message)
        if (error.response)
            dispatch(workcodeFailed(error.response))
        else
            dispatch(workcodeFailed(error.message))
    })
}

export const DeleteWorkCode = obj => dispatch => {
    dispatch(loading())
    apiClient.delete(`${WORKCODE}/${obj}`).then(response => {
        dispatch(deleteSuccess(response.data))
    }).catch(error => {
        if (error.response)
            dispatch(workcodeFailed(error.response))
        else
            dispatch(workcodeFailed(error.message))
    })
}

export const loading = () => ({
    type: ActionTypes.WORKCODE_LOADING
})

export const success = (data) => ({
    type: ActionTypes.WORKCODE_SUCCESS,
    payload: data
})

export const workcodeFailed = (error) => ({
    type: ActionTypes.WORKCODE_FAILED,
    payload: error
})

export const createSuccess = (data) => ({
    type: ActionTypes.WORKCODE_CREATE_SUCCESS,
    payload: data
})

export const updateSuccess = (data) => ({
    type: ActionTypes.WORKCODE_UPDATE_SUCCESS,
    payload: data
})

export const deleteSuccess = (data) => ({
    type: ActionTypes.WORKCODE_DELETE_SUCCESS,
    payload: data
})