import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, Card, DatePicker, Form, Input, Modal, Select, Space, Table } from "antd";
import { FilterOutlined, ReloadOutlined, SearchOutlined } from "@ant-design/icons";
import apiClient from "../../Shared/apiClient";
import moment from "moment";
import { openNotificationWithIcon } from "../../Shared/notification";
import { CSVLink } from "react-csv";
import Highlighter from "react-highlight-words";
const { RangePicker } = DatePicker;
const dateFormat = "YYYY-MM-DD";



const exportCSVHeader = [
    { label: "Date", key: "Date" },
    { label: "CLI", key: "callid" },
    { label: "Call Landing Time", key: "timeStart" },
    { label: "Call Dropped Time", key: "timeEnd" },
    { label: "Call Waiting Duration", key: "data3" }
];

const CLIAbandonCallsReport = () => {
    const [form] = Form.useForm();
    const [data, setData] = useState([]);
    const [to, setTo] = useState();
    const [from, setFrom] = useState();
    const [number, setNumber] = useState();
    const [loading, setLoading] = useState(false);
    const [fetchReportCheck, setFetchReportCheck] = useState(true);
    const [exportReportCheck, setExportReportCheck] = useState(true);

    const onNumberChange = (value) => {
        setFetchReportCheck(false);
        setNumber(value.target.value);
    };

    const onDateChange = (date, dateString) => {
        setFrom(moment(dateString[0]));
        setTo(moment(dateString[1]));
        setFetchReportCheck(false);
    };

    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const searchInput = useRef();

    const [showFilter, setShowFilter] = useState(false)
    const [resetFilter, setResetFilter] = useState(false)

    const resetFormFilter = () => {
        // dispatch(getTrunkPerHourReport())
        // dispatch(trunkPerHourReset())
        // console.log(new Date().toLocaleString() + '')
        setData([])
        form.resetFields()
        setResetFilter(true)
    }

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }


    const columns = [
        {
            title: "Date",
            dataIndex: "Date",
            key: "date",
        },
        {
            title: "CLI",
            dataIndex: "callid",
            key: "callid",
            ...getColumnSearchProps('callid')
        },
        {
            title: "Call Landing Time",
            dataIndex: "timeStart",
            key: "timeStart",

        },
        {
            title: "Call Dropped Time",
            dataIndex: "timeEnd",
            key: "timeEnd",
        },
        {
            title: "Call Waiting Duration",
            dataIndex: "data3",
            key: "data3",
            ...getColumnSearchProps('data3')
        },
    ];


    const fetchReport = () => {
        const formValues = form.getFieldsValue();

        setData([])
        setLoading(true);
        apiClient
            .post(`/api/report/getCLIAbandonCalls`, {
                contactNumber: formValues.contact,
                from: from?._i,
                to: to?._i,
                queue: formValues.queue

            })
            .then((resp) => {
                setData(resp.data);
                setLoading(false);
                setExportReportCheck(false);
                setTo(null)
                setFrom(null)
                form.resetFields();
            })
            .catch((err) => {
                setLoading(false);
                openNotificationWithIcon('error', err.message)
            });
    };



    return (
        <>
            {/* <Card
                title="Abandon Call CLI Details"
                style={{ background: "#fff", borderRadius: "2px", padding: 10 }}
                extra={
                    <Form
                        form={form}
                        layout="inline"
                        size="large"
                        style={{ marginTop: "3px" }}
                    >
                        <Form.Item
                            name={"contact"}
                            rules={[
                                {
                                    pattern: new RegExp(/[0-9]/),
                                    message: "Field accepts numbers only.",
                                },
                            ]}
                        >
                            <Input
                                placeholder="Enter Contact Number"
                                size="large"
                                style={{ width: "auto" }}
                                onChange={onNumberChange}
                            />
                        </Form.Item>
                        <Form.Item name={"picker"}>
                            <RangePicker format={dateFormat} onChange={onDateChange} />
                        </Form.Item>
                        <Form.Item>
                            <Button
                                size="large"
                                onClick={fetchReport}
                                disabled={fetchReportCheck}
                            >
                                Fetch Report
                            </Button>
                        </Form.Item>
                        <Form.Item>
                            <CSVLink headers={exportCSVHeader} data={data} filename="Abandon-Call-CLI-Details.csv">
                                <Button disabled={data.length === 0}>Export Report</Button>
                            </CSVLink>
                        </Form.Item>
                    </Form>
                }
            > */}
            <Table

                title={data => <>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        {`Abandon Call Cli Details`}
                        <Space>
                            <Button onClick={() => resetFormFilter()} type="danger" icon={<ReloadOutlined />}>Reset Filter</Button>
                            <Button onClick={() => setShowFilter(true)} icon={<FilterOutlined />}>Filter</Button>
                            <CSVLink headers={exportCSVHeader} data={data} filename="Abandon-Call-CLI-Details.csv">
                                <Button disabled={data.length === 0}>Export Report</Button>
                            </CSVLink>
                        </Space>

                    </div>
                </>}
                columns={columns}
                dataSource={data}
                // pagination={false}
                loading={loading}
                size="default"
                rowKey={"callid"}
                bordered
            // scroll={{
            //     x: "calc(700px + 50%)",
            //     y: 440,
            // }}
            />
            {/* </Card> */}

            <CliAbandonFilter
                form={form}
                resetFilter={resetFilter}
                visible={showFilter}
                fetchReport={fetchReport}
                onDateChange={onDateChange}
                setVisible={setShowFilter}
            />

        </>
    );
};

export const CliAbandonFilter = ({ form, fetchReport, onNumberChange, onDateChange, visible, setVisible }) => {

    const [queues, setQueues] = useState([])

    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data))
    }, [])

    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            okText="Submit"
            onOk={() => form.validateFields()
                .then(value => {
                    // dispatch(getTrunkPerHourReport({ date: filterDate, queue: queue }))
                    fetchReport()
                    setVisible(false)
                })
            }
            // okButtonProps={{
            //     loading: btnLoading,
            //     icon: <SaveOutlined />
            // }}
            title="Abandon Call Cli Details Filter"
        >
            {/* <Spin spinning={btnLoading} indicator={<SyncOutlined spin />}> */}
            <Form
                form={form}
                layout="vertical"
            >
                <Form.Item
                    name={"contact"}
                    rules={[
                        {
                            required: true,
                            message: "This Contact Number is required"
                        },
                        {
                            pattern: new RegExp(/^[0-9]+$/),
                            message: "Field accepts numbers only.",
                        },
                    ]}
                >
                    <Input
                        placeholder="Enter Contact Number"
                        size="large"
                        style={{ width: "100%" }}
                        onChange={onNumberChange}
                    />
                </Form.Item>

                <Form.Item name={"picker"} rules={[
                    {
                        required: true,
                        message: "This field is required"

                    },
                ]}>
                    <RangePicker format={dateFormat} onChange={onDateChange} style={{ width: "100%" }} />
                </Form.Item>

                <Form.Item colon={false} name="queue"  >
                    <Select placeholder="Select Queue" >
                        {queues && queues.map((queue, index) => <Select.Option key={index} value={queue.name}>
                            {queue.name}
                        </Select.Option>)}
                    </Select>
                </Form.Item>
            </Form>
            {/* </Spin> */}
        </Modal>
    )
}

export default CLIAbandonCallsReport;