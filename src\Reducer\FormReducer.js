import * as ActionTypes from "../Constants/FormConstants"

const initial = {
    forms: [],
    message: false,
    errMess: false,
    isLoading: false
}

export const FormReducer = (state = initial, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.FORMS_SUCCESS:
            return {...state, isLoading: false, errMess: false, message: false, forms: action.payload}
        case ActionTypes.FORM_LOADING:
            return {...state, isLoading: true, errMess: false, message: false}
        case ActionTypes.FORM_FAILED:
            return {...state, isLoading: false, errMess: action.payload, message: false}
        case ActionTypes.FORM_SUCCESS:
            return {...state, isLoading: false, errMess: false, message: action.payload}
    }
}