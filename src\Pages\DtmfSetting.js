import React, { useState, useEffect } from 'react';
import { Table, Button, Modal, Form, Input, message, Space, Typography, Spin, Tag } from 'antd';
import apiClient from '../Shared/apiClient';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';

const DtmfSetting = () => {

    const [dtmfSettings, setDtmfSettings] = useState([]);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [currentRecord, setCurrentRecord] = useState(null);
    const [loading, setLoading] = useState(false);
    const [form] = Form.useForm();

    useEffect(() => {
        fetchDtmfSettings();
    }, []);

    const fetchDtmfSettings = async () => {
        setLoading(true);
        try {
            const response = await apiClient.get('/api/dtmf-settings');
            setDtmfSettings(response.data);
        } catch (error) {
            message.error('Failed to load DTMF settings');
        } finally {
            setLoading(false);
        }
    };

    const showAddModal = () => {
        setIsEditing(false);
        setCurrentRecord(null);
        setIsModalVisible(true);
        form.resetFields();
    };

    const showEditModal = (record) => {
        setIsEditing(true);
        setCurrentRecord(record);
        setIsModalVisible(true);
        form.setFieldsValue({
            dtmf: record.dtmf,
            value: record.value
        });
    };

    const handleCancel = () => {
        setIsModalVisible(false);
        form.resetFields();
    };

    const handleSubmit = async (values) => {
        setLoading(true);
        try {
            if (isEditing) {
                await apiClient.put(`/api/dtmf-settings/${currentRecord.id}`, {
                    dtmf: values.dtmf,
                    value: values.value
                });
                message.success('DTMF setting updated successfully');
            } else {
                await apiClient.post('/api/dtmf-settings', {
                    dtmf: values.dtmf,
                    value: values.value
                });
                message.success('DTMF setting added successfully');
            }

            setIsModalVisible(false);
            fetchDtmfSettings();
            form.resetFields();
        } catch (error) {
            if (error.response?.data?.errors) {
                Object.keys(error.response?.data?.errors).forEach(key => {
                    message.error(error.response?.data?.errors[key]);
                });
            } else {
                message.error(error?.response?.data?.message || 'An error occurred');
            }
        } finally {
            setLoading(false);
        }
    };

    const handleDelete = async (id) => {
        setLoading(true);
        try {
            await apiClient.delete(`/api/dtmf-settings/${id}`);
            message.success('DTMF setting deleted successfully');
            fetchDtmfSettings();
        } catch (error) {
            message.error('Failed to delete DTMF setting');
        } finally {
            setLoading(false);
        }
    };

    const columns = [
        {
            title: 'DTMF',
            dataIndex: 'dtmf',
            key: 'dtmf',
            render: (text) => <Tag color="blue">{text}</Tag>
        },
        {
            title: 'Value',
            dataIndex: 'value',
            key: 'value',
        },
        {
            title: 'Actions',
            key: 'actions',
            render: (_, record) => (
                <Space>
                    <Button
                        type="primary"
                        icon={<EditOutlined />}
                        onClick={() => showEditModal(record)}
                    >
                        Edit
                    </Button>
                    <Button
                        type="primary"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => handleDelete(record.id)}
                    >
                        Delete
                    </Button>
                </Space>
            ),
        },
    ];

    return (
        <div>
            <Spin spinning={loading}>
                <Table
                    title={() => (
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            <Typography.Title level={4} style={{ margin: 0 }}>DTMF Settings</Typography.Title>
                            <Button
                                type="primary"
                                icon={<PlusOutlined />}
                                onClick={showAddModal}
                            >
                                Add DTMF Setting
                            </Button>
                        </div>
                    )}
                    columns={columns}
                    dataSource={dtmfSettings}
                    rowKey="id"
                    pagination={{ pageSize: 10 }}
                    bordered
                />

                <Modal
                    title={isEditing ? 'Edit DTMF Setting' : 'Add DTMF Setting'}
                    open={isModalVisible}
                    onCancel={handleCancel}
                    footer={null}
                    destroyOnClose={true}
                    maskClosable={false}
                    width={500}
                >
                    <Spin spinning={loading}>
                        <Form
                            form={form}
                            onFinish={handleSubmit}
                            layout="vertical"
                        >
                            <Form.Item
                                name="dtmf"
                                label="DTMF"
                                rules={[{ required: true, message: 'Please input the DTMF digit!' }]}
                            >
                                <Input placeholder="Enter DTMF digit" />
                            </Form.Item>

                            <Form.Item
                                name="value"
                                label="Value"
                                rules={[{ required: true, message: 'Please input the Value!' }]}
                            >
                                <Input placeholder="Enter value or action" />
                            </Form.Item>

                            <Form.Item style={{ marginBottom: 0, marginTop: 24 }}>
                                <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
                                    <Button onClick={handleCancel}>Cancel</Button>
                                    <Button type="primary" htmlType="submit">
                                        {isEditing ? 'Update' : 'Add'}
                                    </Button>
                                </Space>
                            </Form.Item>
                        </Form>
                    </Spin>
                </Modal>
            </Spin>
        </div>
    );
};

export default DtmfSetting;
