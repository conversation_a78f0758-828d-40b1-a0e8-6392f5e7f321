
const initState = {
    isLoading: false,
    errMess: null,
    message: null,
    data: []
}

export const SMSCategoryReducer = (state = initState, action) => {
    switch (action.type) {
        default:
            return { ...state }
        case "GET_CATEGORY":
            return { ...state, isLoading: false, message: null, data: action.payload, errMess: null }
        case "MUTATE_CATEGORY":
            return { ...state, isLoading: false, message: action.payload, errMess: null }
        case "LOADING_CATEGORY":
            return { isLoading: true }
        case "FAILED_CATEGORY":
            return { ...state, isLoading: false, errMess: action.payload, message: null }
    }
}