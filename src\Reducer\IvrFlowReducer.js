import * as ActionsType from '../Constants/IvrFlowConstants'
const initalElemnt = {
    isLoading: false,
    errMess: null,
    message: null,
    data: []
}

export const IvrFlowReducer = (state = initalElemnt, action) => {
    switch (action.type) {
        default:
            return { ...state }
        case ActionsType.IVR_FLOW_SUCCESS:
            return { ...state, isLoading: false, data: action.payload, errMess: null }
        // case ActionsType.UPDATE_SUCCESS:
        //     return { ...state, isLoading: false, message: action.payload, errMess: null }
        // case ActionsType.DELETE_SUCCESS:
        //     return { ...state, isLoading: false, message: action.payload, errMess: null }
        // case ActionsType.INBOUND_SUCCESS:
        //     return { ...state, isLoading: false, inbound: action.payload, errMess: null }
        case ActionsType.IVR_FLOW_LOADING:
            return { ...state, isLoading: true }
        case ActionsType.IVR_FLOW_FAILED:
            return { ...state, isLoading: false, errMess: action.payload }
    }
}