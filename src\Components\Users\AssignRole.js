import { Checkbox, Divider, Modal, Spin } from "antd";
import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getRole, getRoleFromUser } from "../../Actions/RoleActions";
import { openNotificationWithIcon } from "../../Shared/notification";

const AssignRole = ({ record, visible, setVisible, onCreate }) => {
    const [checkAll, setCheckAll] = useState(false);
    const [defaultCheckedList, setDefaulCheckedList] = useState([])
    const [checkedList, setCheckedList] = useState(defaultCheckedList);
    const [plainOptions, setPlainOptions] = useState(null)
    const [indeterminate, setIndeterminate] = useState(true);
    const role = useSelector(state => state.RoleReducer)
    const dispatch = useDispatch()
    const [showRequired, setShowRequired] = useState(false);

    useEffect(() => {
        if (record && visible) {
            dispatch(getRoleFromUser(record.id))
            dispatch(getRole())
        }
    }, [visible])

    useEffect(() => {
        if (role.userAssignedRole.length >= 1) {
            setDefaulCheckedList(role.userAssignedRole.map(value => value.assigned ? value.name : ''))
            setCheckedList(role.userAssignedRole.map(value => value.assigned ? value.name : ''))
        }
        if (role.role.length >= 1) {
            setPlainOptions(role.role.filter((role) => role.name !== 'agent' && role.name !== 'supervisor').map(value => value.name))
        }
        if (role.message) {
            // openNotificationWithIcon("success", role.message)
            setVisible(false)
        }

        if (visible) {
            setCheckAll(false); // Reset checkAll to false when modal opens
            setIndeterminate(false);
        }
    }, [role, visible])

    const onChange = list => {
        console.log(list)
        setCheckedList(list);
        setIndeterminate(!!list.length && list.length < plainOptions.length);
        setCheckAll(list.length === plainOptions.length);
        setShowRequired(false);
    };

    const onCheckAllChange = e => {
        console.log(e.target.checked)
        setCheckedList(e.target.checked ? plainOptions : []);
        setIndeterminate(false);
        setCheckAll(e.target.checked);
    };

    const isValid = () => {
        return checkedList.filter(role => role).length > 0;
    };

    return (
        <Modal
            centered
            title="Assign Role To User"
            visible={visible}
            destroyOnClose={true}
            closable={true}
            okText="Assign Role"
            onOk={() => {

                if (!isValid()) {
                    setShowRequired(true);
                    return;
                }else{

                    setShowRequired(false);
            
                    onCreate(checkedList)
                }
               
            }}
            onCancel={() => {
                setVisible(false)
            }}
        >
            <Spin spinning={role.isLoading}>
                <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
                    Check all
                </Checkbox>
                <Divider />
                <Checkbox.Group options={plainOptions} value={checkedList} onChange={onChange} />
                    {showRequired && (
                        <p style={{ color: 'red', marginTop: '8px' }}>
                            Please select at least one role.
                        </p>
                    )}
            </Spin>
        </Modal>
    )
}
export default AssignRole