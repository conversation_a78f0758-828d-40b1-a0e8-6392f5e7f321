const initialState = {
    data: [],
    isLoading: false,
    errMess: ''
}

export const HoldTimeReportReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case "HOLD_TIME_REPORT_LOADING":
            return { ...state, isLoading: true }
        case "HOLD_TIME_REPORT_SUCCESS":
            return { ...state, isLoading: false, data: action.payload, errMess: '' }
        case "HOLD_TIME_REPORT_FAILED":
            return { ...state, isLoading: false, errMess: action.payload }
        case "RESET_TIME_REPORT":
            return { ...state, data: [] }
    }
}