import apiClient from "../Shared/apiClient";
import * as ActionType from "../Constants/CampaignUserConstants"

const endpoint = 'campaignUser'
const addUsers = 'addUsers'

const handleError = error => {
    if (error.response) {
        return error.response.data
    } else {
        return error.message
    }
}

export const addUserInCampaign = (campaign, user) => dispatch => {
    dispatch(campaignUserLoading())
    apiClient.post(`/api/${campaign}/${user}/${endpoint}`).then(r => dispatch(getUsersInCampaign(campaign))).catch(e => dispatch(campaignUserFailed(handleError(e)))).catch(e => dispatch(campaignUserFailed(handleError(e))))
}

export const addUsersInCampaign = (campaign, users) => dispatch => {
    dispatch(campaignUserLoading())
    apiClient.post(`/api/${campaign}/campaignUsers`, users).then(r => dispatch(getUsersInCampaign(campaign))).catch(e => dispatch(campaignUserFailed(handleError(e)))).catch(e => dispatch(campaignUserFailed(handleError(e))))
}

export const getUsersInCampaign = campaign => dispatch => {
    dispatch(campaignUserLoading())
    apiClient.get(`/api/${campaign}/${endpoint}`).then(r => dispatch(campaignUserSuccess(r.data))).catch(e => dispatch(campaignUserFailed(handleError(e)))).catch(e => dispatch(campaignUserFailed(handleError(e))))
}

export const deleteUserInCampaign = (campaign, user) => dispatch => {
    dispatch(campaignUserLoading())
    apiClient.delete(`/api/${campaign}/${user}/${endpoint}`).then(r => dispatch(getUsersInCampaign(campaign))).catch(e => dispatch(campaignUserFailed(handleError(e)))).catch(e => dispatch(campaignUserFailed(handleError(e))))
}

const campaignUserLoading = () => ({
    type: ActionType.CAMPAIGN_USER_LOADING
})

const campaignUserFailed = error => ({
    type: ActionType.CAMPAIGN_USER_FAILED,
    payload: error
})

const campaignUserSuccess = users => ({
    type: ActionType.CAMPAIGN_USER_SUCCESS,
    payload: users
})

const campaignUserSuccessMessage = message => ({
    type: ActionType.CAMPAIGN_USER_SUCCESS_MESSAGE,
    payload: message
})

