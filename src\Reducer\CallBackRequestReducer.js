const initialState = {
    data: [],
    callBackRequest: [],
    isLoading: false,
    errMess: ''
}

export const CallBackRequestReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case "CALLBACKREQUEST_LOADING":
            return { ...state, isLoading: true }
        case "CALLBACKREQUEST_SUCCESS":
            return { ...state, isLoading: false, data: action.payload }
        case "CALLBACKREQUEST_PER_HOUR_SUCCESS":
            return { ...state, isLoading: false, callBackRequest: action.payload }
        case "CALLBACKREQUEST_FAILED":
            return { ...state, isLoading: false, errMess: action.payload }
        case "CALLBACKREQUEST_RESET":
            return { ...state, data: [] }
        case "ABANDONEDCALL_RESET":
            return { ...state, callBackRequest: [] }
    }
}