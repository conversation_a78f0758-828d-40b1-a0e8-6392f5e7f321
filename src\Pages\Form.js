import { useState, useEffect } from "react";
import {
    Button,
    Card,
    Checkbox,
    Input,
    Modal,
    Select,
    Space,
    Spin,
    Switch,
    Table,
    Tag,
} from "antd";
import {
    CheckCircleOutlined,
    CloseCircleOutlined,
    ContainerOutlined,
    DeleteOutlined,
    EditOutlined,
    <PERSON><PERSON><PERSON>berOutlined,
    FormatPainterOutlined,
    MinusCircleOutlined,
    PlusCircleOutlined,
    SnippetsOutlined,
} from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import {
    deleteForm,
    getForms,
    postForm,
    updateForm,
} from "../Actions/FormActions";
import { Form as AntForm } from "antd";
import { openNotificationWithIcon } from "../Shared/notification";
import { getFormFieldTypes } from "../Actions/FormFieldTypeActions";
import { getFormFields, postFormFields } from "../Actions/FormFieldActions";
import { AddForm } from "../Components/Form/AddForm";
import { FormBuilder } from "../Components/Form/FormBuilder";
import { FormFields } from "../Components/Form/FormFields";
import { FormDetailsModal } from "../Components/Form/FormDetailsModal";
import apiClient from "../Shared/apiClient";
import { FunctionBuilder } from "../Components/Form/FunctionBuilder";
import { Functions } from "../Components/Form/Functions";
// import { FormDetailsPage } from "./FormDetailsPage";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";

const Form = (props) => {
    const formState = useSelector((state) => state.FormReducer);
    const dispatch = useDispatch();
    const [formVisible, setFormVisible] = useState(false);
    const [detailsVisible, setDetailsVisible] = useState(false);
    const [item, setItem] = useState(false);
    const [formID, setFormID] = useState(null);
    const [formId, setFormId] = useState(false);
    const [formBuilderVisible, setFormBuilderVisible] = useState(false);
    const [functionBuilderVisible, setFunctionBuilderVisible] = useState(false);
    const [formFieldsVisible, setFormFieldsVisible] = useState(false);
    const [functionsVisible, setFunctionsVisible] = useState(false);

    useEffect(() => {
        dispatch(getForms());
    }, []);

    let history = useHistory();

    useEffect(() => {
        if (formState.errMess) {
            // console.log("check", formState?.errMess?.errors?.name)
            if (formState?.errMess?.errors) {
                openNotificationWithIcon("error", formState?.errMess?.errors?.name);
            }
            else {
                openNotificationWithIcon("error", formState?.errMess?.message);
            }
        }
    }, [formState.errMess]);

    useEffect(() => {
        if (formState.message)
            openNotificationWithIcon("success", formState.message);
    }, [formState.message]);

    const columns = [
        {
            title: "Id",
            dataIndex: "id",
            key: "id",
        },
        {
            title: "Name",
            dataIndex: "name",
            key: "name",
        },
        {
            title: "Queue",
            dataIndex: "queue_name",
            key: "queue_name",
        },
        // {
        //     title: "Created By",
        //     key: "owner",
        //     // render: (_, { name }) => { return (name || "n/a") },
        //     render: (text, item) => item.owner.name || "n/a",
        // },
        {
            title: "Created By",
            key: "owner",
            render: (text, item) => item.owner?.name || "n/a",
        },
        {
            title: "",
            key: "formBuilder",
            render: (text, item) => (
                <Space>
                    <Button
                        onClick={() => {
                            setFormId(item.id);
                            setFormBuilderVisible(true);
                        }}
                        icon={<ContainerOutlined />}>
                        Form builder
                    </Button>
                    {/* <Button
                        onClick={() => {
                            setFormId(item.id);
                            setFunctionBuilderVisible(true);
                        }}
                        icon={<ContainerOutlined />}
                    >
                        Function builder
                    </Button> */}
                    <Button
                        type="primary"
                        onClick={() => {
                            setFormId(item.id);
                            setFormFieldsVisible(true);
                        }}
                        icon={<FormatPainterOutlined />}
                    >
                        Form fields
                    </Button>
                    {/* <Button
                        type="primary"
                        onClick={() => {
                            setFormId(item.id);
                            setFunctionsVisible(true);
                        }}
                        icon={<FormatPainterOutlined />}
                    >
                        Functions
                    </Button> */}
                </Space>
            ),
        },
        {
            title: "Default",
            key: "default",
            render: (text, item) => item.default == 1 ? <CheckCircleOutlined style={{ color: 'green' }} /> : <CloseCircleOutlined style={{ color: 'red' }} />,
        },
        {
            title: "Actions",
            key: "actions",
            render: (text, item) => (
                <Space>
                    <Button
                        onClick={() => {


                            setFormID(item.id);
                            setDetailsVisible(true);
                            // <FormDetailsPage formID={formID} setFormID={setFormID} detailsVisible={detailsVisible} />
                            // history.push(`/formdetails/${item.id}`)
                        }}
                        icon={<SnippetsOutlined />}
                        title="Details"
                    />
                    <Button
                        onClick={() => {
                            setItem(item);
                            setFormVisible(true);
                        }}
                        icon={<EditOutlined />}
                        title="Edit"
                    />
                    <Button
                        type="danger"
                        onClick={() => dispatch(deleteForm(item.id))}
                        icon={<DeleteOutlined />}
                        title="Delete"
                    />
                </Space>
            ),
        },
    ];

    const onCreate = (values) => {
        setFormVisible(false);
        dispatch(postForm(values));
    };

    const onUpdate = (values) => {
        dispatch(updateForm(values));
        setFormVisible(false);
        setItem(false);
    };

    const formDetailsProps = {
        detailsVisible,
        setDetailsVisible,
        formID,
        setFormID,
    };
    const formProps = {
        formVisible,
        setFormVisible,
        onCreate,
        onUpdate,
        item,
        setItem,
        allForms: formState.forms
    };

    const builderProps = {
        formBuilderVisible,
        setFormBuilderVisible,
        formId,
    };

    const functionBuilderProps = {
        functionBuilderVisible,
        setFunctionBuilderVisible,
        formId,
    };

    const fieldProps = {
        formFieldsVisible,
        setFormFieldsVisible,
        formId,
    };

    const functionProps = {
        functionsVisible,
        setFunctionsVisible,
        formId,
    };

    return (
        <Card
            title="Forms"
            extra={
                <Button
                    onClick={() => setFormVisible(true)}
                    icon={<PlusCircleOutlined />}
                >
                    Add form
                </Button>
            }
        >
            <Table
                columns={columns}
                loading={formState.isLoading}
                dataSource={formState.forms}
                scroll={{ x: 1100 }}
                rowKey={"id"}
            />
            <AddForm {...formProps} />
            <FormBuilder {...builderProps} />
            <FunctionBuilder {...functionBuilderProps} />
            <FormFields {...fieldProps} />
            <Functions {...functionProps} />
            <FormDetailsModal {...formDetailsProps} />
            {/* <FormDetailsPage {...formDetailsProps} /> */}
        </Card>
    );
};

export default Form;