import { <PERSON><PERSON>, <PERSON>, Form, Switch, Spin, notification } from "antd";
import { useEffect, useState } from "react";
import apiClient from "../Shared/apiClient";


export const SMSModuleAccessibility = () => {
    const [form] = Form.useForm();
    const [smsAccessibility, setSmsAccessibility] = useState(false);
    const [isLoading, setIsLoading] = useState(false); 

    const openNotification = (message, description, type = 'success') => {
        notification[type]({
            message,
            description,
        });
    };

    useEffect(() => {
        setIsLoading(true);
        apiClient.get('api/sms-accessibility')
            .then(response => {
                setSmsAccessibility(response.data.data.agent_accessibility === 1); 
                setIsLoading(false);
            })
            .catch(() => {
                setIsLoading(false);
            });
    }, []);

    // Handle the submit (save or update)
    const handleSubmit = () => {
        const agent_accessibility = smsAccessibility ? 1 : 0;
        setIsLoading(true);
        apiClient.put('api/sms-accessibility', { agent_accessibility })
            .then(() => {
                openNotification('Success', 'SMS module accessibility updated successfully');
                setIsLoading(false);
            })
            .catch(() => {
                openNotification('Error', 'Failed to update accessibility status', 'error');
                setIsLoading(false);
            });
    };

    return (
        <Spin spinning={isLoading}>
            <Card title={"SMS Module Accessibility"}>
                <Form
                    form={form}
                    onFinish={handleSubmit}
                    layout="vertical"
                >
                    {/* SMS Accessibility Switch */}
                    <Form.Item
                        name="agent_accessibility"
                        label="Enable SMS Module"
                        initialValue={smsAccessibility ? 1 : 0}
                    >
                        <Switch
                            checked={smsAccessibility}
                            onChange={(checked) => setSmsAccessibility(checked)} // Set the switch state
                        />
                    </Form.Item>

                    <Form.Item>
                        <Button type="primary" htmlType="submit">
                            Save
                        </Button>
                    </Form.Item>
                </Form>
            </Card>
        </Spin>
    );
};
