import { useDispatch, useSelector } from "react-redux";
import {
    getRingNoAnswer,
    getRingNoAnswerAgentWiseSummaryReport,

} from "../../Actions/AgentReportActions";
import { Button, Descriptions, Input, Modal, Space, Spin, Table } from "antd";
import { CSVDownload, CSVLink } from "react-csv";

import { FilterOutlined, ReloadOutlined, SearchOutlined } from "@ant-design/icons";
import { useState, useEffect, useRef } from 'react'

import RingNoAnswerAgentSummaryReportFilter from "../../Components/Reports/RingNoAnswerAgentSumaryReportFilter";
import Highlighter from "react-highlight-words";

export const RingNoAnswerAgentWiseSummaryReport = () => {
    const report = useSelector(state => state.AgentReportReducer)
    const [record, setRecord] = useState(null)
    const [showDetails, setShowDetails] = useState(false)
    const [filter, setFilter] = useState(false)
    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')

    const dispatch = useDispatch()

    // useEffect(() => {
    //     dispatch(getRingNoAnswerAgentWiseSummaryReport())
    // }, [])

    const searchInput = useRef()

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }


    const onFilter = (values) => {
        dispatch(getRingNoAnswerAgentWiseSummaryReport(values))
        setFilter(false)
    }

    return (
        // <Spin spinning={report.isLoading}>
        <>
            <RingNoAnswerAgentSummaryReportFilter setVisible={setFilter} isLoading={report.isLoading} onCancel={() => setFilter(false)} visible={filter} onCreate={onFilter} record={"ok"} />
            <Modal
                width="100%"
                visible={showDetails}
                closable={true}
                onCancel={() => setShowDetails(false)}
                onOk={() => setShowDetails(false)}
                centered
            >
                <Descriptions style={{ padding: 20 }} title="Pause Reason Info" bordered size="small">
                    {record && Object.keys(record).map((key, index) => (
                        <Descriptions.Item label={key}>
                            {(record[key]) ? (record[key]) : ""}
                        </Descriptions.Item>))}
                </Descriptions>
            </Modal>

            <div style={{ float: 'right' }}>
                <div style={{ marginBottom: 16 }}>
                    <Space>


                        {/* <ReactExport.ExcelFile element={<Button type={"default"}>Download Excel</Button>} fileExtension={"xlsx"} filename={"CDR Report"}>
                            <ReactExport.ExcelFile.ExcelSheet data={report.agentReport} >
                                {record && Object.entries(record).map((key, index) => (
                                    <ReactExport.ExcelFile.ExcelColumn label={key} value={key} />
                                ))}
                            </ReactExport.ExcelFile.ExcelSheet>
                        </ReactExport.ExcelFile> */}
                    </Space>
                </div>
            </div>

            <Table scroll={{ x: 800 }} title={d =>
                <>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        Ring No Answer Agent Wise Summary
                        <Space>
                            <Button onClick={() => dispatch(getRingNoAnswerAgentWiseSummaryReport())} type="danger" icon={<ReloadOutlined />}>
                                Reset Filter
                            </Button>
                            <Button onClick={() => setFilter(true)} icon={<FilterOutlined />}>
                                Filter
                            </Button>
                            <Button type={"primary"} disabled={report.agentReport.length == 0} onClick={(<CSVDownload data={report.agentReport} target="_blank" />)}>
                                <CSVLink data={report.agentReport} filename="Ring No Answer Agent Wise Summary" > Download CSV</CSVLink>
                            </Button>
                        </Space>

                    </div>
                </>
            }

                bordered
                dataSource={report.agentReport}>
                <Table.Column dataIndex="Sn" key="Sn" title="S#" render={(text, record, index) => (++index)} />
                <Table.Column dataIndex="agent" key="agent" title="Agent" {...getColumnSearchProps('agent')} />
                <Table.Column dataIndex="ringtime" key="ringtime" title="Total Ring Time" sorter={(a, b) => a.ringtime - b.ringtime}  {...getColumnSearchProps('ringtime')} />
                <Table.Column dataIndex="number_of_calls" key="number_of_calls" title="Number of calls"  {...getColumnSearchProps('number_of_calls')} />
            </Table>
        </>
        // </Spin>
    )
}
