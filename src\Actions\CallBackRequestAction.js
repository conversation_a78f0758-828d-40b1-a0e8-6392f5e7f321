import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";


// export const getCallBackRequest = (value, pagination) => dispatch => {
//     dispatch(loading())
//     apiClient.get(`/api/report/getcallback-request?page=${pagination['current']}&pageSize=${pagination['pageSize']}`, value).then(r => dispatch(callBackRequestSuccess(r.data))).catch(e => dispatch(callBackRequestFailed(handleError(e))))
// }

export const getCallBackRequest = (filters, pagination) => dispatch => {
    dispatch(loading());
    
    // Prepare query parameters
    const params = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...filters // This now includes start_date and end_date if they exist
    };

    apiClient.get('/api/report/getcallback-request', { params })
        .then(r => dispatch(callBackRequestSuccess(r.data)))
        .catch(e => dispatch(callBackRequestFailed(handleError(e))));
}

export const callBackRequestReset = () => dispatch => {
    dispatch(callbackReset());
}

const loading = () => ({
    type: "CALLBACKREQUEST_LOADING"
})

const callBackRequestSuccess = data => ({
    type: "CALLBACKREQUEST_SUCCESS",
    payload: data
})

const callBackRequestFailed = err => ({
    type: "CALLBACKREQUEST_FAILED",
    payload: err
})

const callbackReset = () => ({
    type: "CALLBACKREQUEST_RESET"
})