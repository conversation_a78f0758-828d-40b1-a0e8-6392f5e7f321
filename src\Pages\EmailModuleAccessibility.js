import { <PERSON><PERSON>, <PERSON>, Form, Switch, Spin, notification } from "antd";
import { useEffect, useState } from "react";
import apiClient from "../Shared/apiClient";


export const EmailModuleAccessibility = () => {
    const [form] = Form.useForm();
    const [emailAccessibility, setEmailAccessibility] = useState(false);
    const [isLoading, setIsLoading] = useState(false); 

    const openNotification = (message, description, type = 'success') => {
        notification[type]({
            message,
            description,
        });
    };

    useEffect(() => {
        setIsLoading(true);
        apiClient.get('api/email-accessibility')
            .then(response => {
                setEmailAccessibility(response.data.data.agent_accessibility === 1); 
                setIsLoading(false);
            })
            .catch(() => {
                setIsLoading(false);
            });
    }, []);

    // Handle the submit (save or update)
    const handleSubmit = () => {
        const agent_accessibility = emailAccessibility ? 1 : 0;
        setIsLoading(true);
        apiClient.put('api/email-accessibility', { agent_accessibility })
            .then(() => {
                openNotification('Success', 'Email module accessibility updated successfully');
                setIsLoading(false);
            })
            .catch(() => {
                openNotification('Error', 'Failed to update accessibility status', 'error');
                setIsLoading(false);
            });
    };

    return (
        <Spin spinning={isLoading}>
            <Card title={"Email Module Accessibility"}>
                <Form
                    form={form}
                    onFinish={handleSubmit}
                    layout="vertical"
                >
                    {/* Email Accessibility Switch */}
                    <Form.Item
                        name="agent_accessibility"
                        label="Enable Email Module"
                        initialValue={emailAccessibility ? 1 : 0}
                    >
                        <Switch
                            checked={emailAccessibility}
                            onChange={(checked) => setEmailAccessibility(checked)} // Set the switch state
                        />
                    </Form.Item>

                    <Form.Item>
                        <Button type="primary" htmlType="submit">
                            Save
                        </Button>
                    </Form.Item>
                </Form>
            </Card>
        </Spin>
    );
};
