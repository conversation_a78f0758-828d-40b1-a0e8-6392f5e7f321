import {<PERSON><PERSON>, <PERSON>, Divider, Form, Input, Spin, Switch} from "antd";
import apiClient from "../Shared/apiClient";
import {useEffect, useState} from "react";

export const CustomRouting = () => {

  const [loading, setLoading] = useState(false)
  const [form] = Form.useForm()

  useEffect(() => {
    setLoading(true)
    getCustomRouting()
      .then(r => form.setFieldsValue(r.data))
      .then(_ => setLoading(false))
      .catch(e => console.log(e))
  }, [form])

  const getCustomRouting = () => {
    return apiClient.get('/api/custom-routing/getCustomRouting')
  }

  const saveCustomRouting = values => {
    return apiClient.patch('/api/custom-routing/saveCustomRouting', values)
  }

  const handleFormSubmit = values => {
    setLoading(true)
    saveCustomRouting(values)
      .then(r => console.log(r.data))
      .then(_ => setLoading(false))
      .catch(e => console.log(e))
  }

  return(
    <Spin spinning={loading}>
      <Card size="small" title="Custom Routing">
        <p>
          On every incoming call, custom routing script will check incoming calls, if there is a lead of that number in CRM then it will first try to connect with the lead owner, if that user is unavailable then it will be sent to Queue.
        </p>
        <Divider />
        <Form
          layout="vertical"
          wrapperCol={{
            span: 12,
          }}
          onFinish={handleFormSubmit}
          form={form}
        >
          <Form.Item
            label="CRM API"
            name="crm_api"
            rules={[{
              required: true
            }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="API for fetching user details"
            name="crm_api_user"
            rules={[{
              required: true
            }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="API for successful notification"
            name="crm_api_successful"
            rules={[{
              required: true
            }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="API for unsuccessful notification"
            name="crm_api_unsuccessful"
            rules={[{
              required: true
            }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="status"
            label="Enable Custom Routing"
            valuePropName="checked"
            rules={[
              {
                required: true,
                message: 'This field is required.'
              }
            ]}
          >
            <Switch size="large" />
          </Form.Item>
          <Form.Item>
            <Button htmlType="submit" type="primary">Submit</Button>
          </Form.Item>
        </Form>
      </Card>
    </Spin>
  )
}