import * as ActionTypes from "../Constants/FormConstants"
import apiClient from "../Shared/apiClient"
import { FORM } from "../Endpoints/FormRoutes"

const handleError = error => {
    console.log(error.message)
    if (error.response) {
        return error.response.data
    } else {
        return error.message
    }
}

export const getForms = () => dispatch => {
    dispatch(formLoading())
    apiClient.get(FORM).then(d => dispatch(formsSuccess(d.data))).catch(e => dispatch(formFailed(handleError(e)))).catch(e => dispatch(formFailed(handleError(e))))
}

export const postForm = data => dispatch => {
    dispatch(formLoading())
    apiClient.post(FORM, data).then(d => dispatch(formSuccess(d.data))).then(() => dispatch(getForms())).catch(e => dispatch(formFailed(handleError(e)))).catch(e => dispatch(formFailed(handleError(e))))
}

export const updateForm = data => dispatch => {
    apiClient.patch(`${FORM}/${data.id}`, data).then(d => dispatch(formSuccess(d.data))).then(() => dispatch(getForms())).catch(e => dispatch(formFailed(handleError(e))))
}

export const deleteForm = data => dispatch => {
    apiClient.delete(`${FORM}/${data}`).then(d => dispatch(formSuccess(d.data))).then(() => dispatch(getForms())).catch(e => dispatch(formFailed(handleError(e))))
}

const formsSuccess = forms => ({
    type: ActionTypes.FORMS_SUCCESS,
    payload: forms
})

const formSuccess = message => ({
    type: ActionTypes.FORM_SUCCESS,
    payload: message
})

const formLoading = _ => ({
    type: ActionTypes.FORM_LOADING
})

const formFailed = errMess => ({
    type: ActionTypes.FORM_FAILED,
    payload: errMess
})