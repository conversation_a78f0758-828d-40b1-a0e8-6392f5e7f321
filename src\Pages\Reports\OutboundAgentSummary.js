import { Button, Input, Space, Table, Typography } from "antd";
import { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
    getFilteredOutboundAgentSummary,
    getOutboundSummaryReport,
    getOutboundSummaryReportColumns,
    outboundSummaryAgentReset
} from "../../Actions/OutboundAgentSummaryActions";
import { openNotificationWithIcon } from "../../Shared/notification";
import { DownloadOutlined, FilterOutlined, PlusCircleOutlined, ReloadOutlined, SearchOutlined, SettingOutlined, SyncOutlined } from "@ant-design/icons";
import { CSVLink } from "react-csv";
import OutboundAgentSummaryFilter from "../../Components/Reports/OutboundAgentSummaryFilter";
import Highlighter from "react-highlight-words";

export const OutboundAgentSummary = () => {

    const dispatch = useDispatch()
    const [visible, setVisible] = useState()
    const [isLoading, setIsLoading] = useState()
    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')

    const create = (val) => {
        // dispatch(getOutboundSummaryReport())
        dispatch(getFilteredOutboundAgentSummary(val))
        dispatch(getOutboundSummaryReportColumns())
        setVisible(false)
    }

    const outboundSummaryState = useSelector(state => state.OutboundAgentSummaryReducer)

    const searchInput = useRef()

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }


    const [columns, setColumns] = useState([
        { title: 'S#', dataIndex: 'id', key: 'id', render: (text, record, index) => index + 1 },
        { title: 'id', dataIndex: 'channel', key: 'channel', ...getColumnSearchProps('channel') },
        { title: 'Agent', dataIndex: 'username', key: 'username', ...getColumnSearchProps('username') },
        // { title: 'Answered', dataIndex: 'ANSWERED', key: 'ANSWERED' },
        // { title: 'No Answered', dataIndex: 'NO ANSWER', key: 'NO ANSWER' },
        // { title: 'Busy', dataIndex: 'BUSY', key: 'BUSY' },
        // { title: 'Failed', dataIndex: 'FAILED', key: 'FAILED' },
    ])

    useEffect(() => {
        if (outboundSummaryState.errMess !== '') openNotificationWithIcon('error', outboundSummaryState.errMess)
    }, [outboundSummaryState.errMess])

    const resetFormFilter = () => {
        dispatch(outboundSummaryAgentReset())
    }

    // useEffect(() => {
    //     console.log("Columns redux", outboundSummaryState.columns)

    // }, [])

    // useEffect(() => dispatch(getOutboundSummaryReport()), [outboundSummaryState.columns])

    return (<>

        <Table
            loading={{ spinning: outboundSummaryState.isLoading, indicator: <SyncOutlined spin /> }}
            dataSource={outboundSummaryState.data}
            columns={columns.concat(outboundSummaryState.columns.map(v => ({ title: v, dataIndex: v, key: v })))}
            title={data => <>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    Outbound Agent Summary
                    <Space>
                        <OutboundAgentSummaryFilter visible={visible} setVisible={setVisible} onCreate={create} isLoading={isLoading} />
                        <Button
                            danger
                            type="primary"
                            icon={<ReloadOutlined />}
                            onClick={() => {
                                resetFormFilter();
                            }}>
                            Reset Filter
                        </Button>
                        <Button onClick={() => setVisible(true)} icon={<FilterOutlined />}>
                            Filter
                        </Button>
                        <CSVLink data={outboundSummaryState.data || null}
                            filename="OutboundAgentSummary.csv">
                            <Button disabled={outboundSummaryState.data.length == 0} icon={<DownloadOutlined />} type="primary">
                                Download
                            </Button>
                        </CSVLink>
                    </Space>
                </div>
            </>}
            summary={data => {
                let sum = 0
                let total = outboundSummaryState.columns.map(v => {
                    let tot = 0
                    data.forEach(val => {
                        tot += val[v]
                    })

                    return tot
                })

                return (<>
                    <Table.Summary.Row>
                        <Table.Summary.Cell colSpan={3}>Total</Table.Summary.Cell>
                        {/* <Table.Summary.Cell colSpan={1}>
                            <Typography.Text style={{ display: 'flex', alignItems: 'center' }} strong>{data?.length || 0}</Typography.Text>
                        </Table.Summary.Cell> */}
                        {total.map((value, index) => <>
                            <Table.Summary.Cell colSpan={1}>
                                <Typography.Text style={{ display: 'flex', alignItems: 'center' }} strong>{value || 0}</Typography.Text>
                            </Table.Summary.Cell>
                        </>)}
                    </Table.Summary.Row>

                </>)

            }}
        />
    </>
    )
}