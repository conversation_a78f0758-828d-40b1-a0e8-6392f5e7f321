import * as ActionTypes from "../Constants/ScriptConstants"
import apiClient from "../Shared/apiClient";
import {SCRIPT} from "../Endpoints/ScriptRoutes";
import {handleError} from "../Shared/handleError";

export const getScripts = () => dispatch => {
    dispatch(scriptLoading())
    return apiClient.get(SCRIPT).then(d => dispatch(scriptsSuccess(d.data))).catch(e => dispatch(scriptFailed(handleError(e))))
}

export const postScript = data => dispatch => {
    dispatch(scriptLoading())
    return apiClient.post(SCRIPT, data).then(d => dispatch(scriptSuccess(d.data))).then(() => dispatch(getScripts())).catch(e => dispatch(scriptFailed(handleError(e))))
}

export const patchScript = data => dispatch => {
    dispatch(scriptLoading())
    return apiClient.patch(`${SCRIPT}/${data.id}`, data).then(d => dispatch(scriptSuccess(d.data))).then(() => dispatch(getScripts())).catch(e => dispatch(scriptFailed(handleError(e))))
}

export const deleteScript = data => dispatch => {
    dispatch(scriptLoading())
    return apiClient.delete(`${SCRIPT}/${data}`).then(d => dispatch(scriptSuccess(d.data))).then(() => dispatch(getScripts())).catch(e => dispatch(scriptFailed(handleError(e))))
}

const scriptLoading = () => ({
    type: ActionTypes.SCRIPT_LOADING
})

const scriptSuccess = message => ({
    type: ActionTypes.SCRIPT_SUCCESS,
    payload: message
})

const scriptFailed = errMess => ({
    type: ActionTypes.SCRIPT_FAILED,
    payload: errMess
})

const scriptsSuccess = scripts => ({
    type: ActionTypes.SCRIPTS_SUCCESS,
    payload: scripts
})