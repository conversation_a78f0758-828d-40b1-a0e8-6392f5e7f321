
import { Button, Input, Space, Spin, Table, Typography } from "antd";
import { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { openNotificationWithIcon } from "../../Shared/notification";
import { DownloadOutlined, FilterOutlined, PlusCircleOutlined, ReloadOutlined, SearchOutlined, SettingOutlined, SyncOutlined } from "@ant-design/icons";
import { CSVLink } from "react-csv";
import InboundAgentSummaryFilter from "../../Components/Reports/InboundAgentSummaryFilter";
import {
    getFilteredInboundAgentSummary,
    getInboundSummaryReport,
    getInboundSummaryReportColumns,
    inboundAgentSumReset
} from "../../Actions/InboundAgentSummaryActions";
import Highlighter from "react-highlight-words";

export const InboundAgentSummary = () => {

    const dispatch = useDispatch()
    const [visible, setVisible] = useState()
    const [isLoading, setIsLoading] = useState()
    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')

    const create = (val) => {
        dispatch(getFilteredInboundAgentSummary(val))
        dispatch(getInboundSummaryReportColumns())
        setVisible()
        // setVisible(fa)

    }

    const inboundSummaryState = useSelector(state => state.InboundAgentSummaryReducer)
    console.log("check", inboundSummaryState.isLoading)

    // const filteredInboundAgentState = useSelector(state => state.)

    const searchInput = useRef()

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }


    const [columns, setColumns] = useState([
        { title: 'S#', dataIndex: '', key: '', render: (text, record, index) => index + 1 },
        // { title: 'id', dataIndex: 'id', key: 'id', ...getColumnSearchProps('id') },
        { title: 'Agent', dataIndex: 'name', key: 'name', }
    ])
    const resetFormFilter = () => {
        dispatch(inboundAgentSumReset())
    }

    useEffect(() => {
        if (inboundSummaryState.errMess !== '') openNotificationWithIcon('error', inboundSummaryState.errMess)
    }, [inboundSummaryState.errMess])



    // useEffect(() => dispatch(getInboundSummaryReport()), [inboundSummaryState.columns])
    return (<>

        <Table
            loading={{ spinning: inboundSummaryState.isLoading, indicator: <Spin /> }}
            dataSource={inboundSummaryState.data}
            columns={columns.concat(inboundSummaryState.columns.map(v => ({ title: v, dataIndex: v, key: v })))}
            scroll={{ x: 1100 }}
            title={data => <>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    Inbound Agent Summary
                    <Space>
                        <InboundAgentSummaryFilter visible={visible} setVisible={setVisible} onCreate={create} isLoading={isLoading} />
                        <Button danger
                            type="primary"
                            icon={<ReloadOutlined />}
                            onClick={() => {
                                resetFormFilter();
                            }}>
                            Reset Filter
                        </Button>
                        <Button onClick={() => setVisible(true)} icon={<FilterOutlined />}>
                            Filter
                        </Button>
                        <CSVLink data={inboundSummaryState.data || null} filename="InboundAgentSummary.csv">
                            <Button disabled={inboundSummaryState.data.length == 0} icon={<DownloadOutlined />} type="primary">
                                Download CSV
                            </Button>
                        </CSVLink>
                    </Space>
                </div>
            </>}
            summary={data => {
                console.log("check", data)
                let sum = 0
                let total = inboundSummaryState.columns.map(v => {
                    let tot = 0
                    data.forEach(val => {
                        tot += parseInt(val[v])
                    })
                    return tot
                })
                console.log("total", total)
                return (<>
                    <Table.Summary.Row>
                        <Table.Summary.Cell colSpan={2}>Total</Table.Summary.Cell>
                        {/* <Table.Summary.Cell colSpan={1}>
                            <Typography.Text style={{ display: 'flex', alignItems: 'center' }} strong>{data?.length || 0}</Typography.Text>
                        </Table.Summary.Cell> */}
                        {total.map((value, index) => <>
                            <Table.Summary.Cell colSpan={1}>
                                <Typography.Text style={{ display: 'flex', alignItems: 'center' }} strong>{value || 0}</Typography.Text>
                            </Table.Summary.Cell>
                        </>)}
                    </Table.Summary.Row>
                </>)
            }}
        />
    </>
    )
}