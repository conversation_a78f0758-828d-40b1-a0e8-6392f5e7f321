
import * as ActionTypes from "../Constants/InboundDispositionSummaryConstants"

const initialState = {
    data: [],
    isLoading: false,
    errMess: ''
}

export const InboundDispositionSummaryReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.INBOUND_DISPOSITION_SUMMARY_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.INBOUND_DISPOSITION_SUMMARY_RESET:
            return { ...state, data: [] }
        case ActionTypes.INBOUND_DISPOSITION_SUMMARY_SUCCESS:
            return { ...state, isLoading: false, data: action.payload }
        case ActionTypes.INBOUND_DISPOSITION_SUMMARY_FAILED:
            return { ...state, isLoading: false, errMess: action.payload }
    }
}