import {
    Button,
    Checkbox,
    Form as AntForm,
    Input,
    Modal,
    Radio,
    Select,
    Space,
    Spin,
} from "antd";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getFormFieldTypes } from "../../Actions/FormFieldTypeActions";
import { openNotificationWithIcon } from "../../Shared/notification";
import { MinusCircleOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { postFormFields } from "../../Actions/FormFieldActions";
import apiClient from "../../Shared/apiClient";

export const FunctionBuilder = ({
    functionBuilderVisible,
    setFunctionBuilderVisible,
    formId,
}) => {
    const [form] = AntForm.useForm();
    const [functionBuilderForm] = AntForm.useForm();

    const [fields, setFields] = useState([]);
    const [funcType, setFuncType] = useState([]);
    const [referenceField, setReferenceField] = useState([]);
    const [loading, setLoading] = useState(false)
    const formState = useSelector((state) => state.FormReducer);
    const dispatch = useDispatch();

    useEffect(() => {
        if (functionBuilderVisible) {
            setLoading(true)
            apiClient.get('api/functionType').then(res => {
                setFuncType(res.data)
                setLoading(false)
            }).catch(err => {
                openNotificationWithIcon("success", err.message)
                setLoading(false)
            })
            apiClient.get(`api/referenceField?form_id=${formId}`).then(res => {
                setReferenceField(res.data)
                setLoading(false)
            }).catch(err => {
                openNotificationWithIcon("success", err.message)
                setLoading(false)
            })
        }
    }, [functionBuilderVisible]);

    //   useEffect(() => {
    //     if (errMess) openNotificationWithIcon("error", errMess);
    //   }, [errMess]);

    //   useEffect(() => {
    //     if (message) openNotificationWithIcon("success", message);
    //   }, [message]);



    const validateWhiteSpace = (rule, value, callback) => {
        if (value && (/\s/.test(value) || /[A-Z]/.test(value))) {
            callback(
                "Capital letters and Spaces are not allowed, use underscore instead of space and small letter instead of Capital letter."
            );
        } else {
            callback();
        }
    };

    return (
        <Modal
            title="Function builder"
            visible={functionBuilderVisible}
            footer={false}
            onCancel={() => setFunctionBuilderVisible(false)}
        //   onOk={}
        >
            <Spin spinning={loading}>
                <AntForm
                    form={functionBuilderForm}
                    name="form_builder"
                    initialValues={{ required: true }}
                    layout="vertical"
                    onFinish={(values) => {
                        functionBuilderForm
                            .validateFields()
                            .then((values) => {
                                apiClient.post(`api/formFunction`, { ...values, form_id: formId }).then(res => console.log('object res', res)).catch(err => openNotificationWithIcon("error", err.message))
                                functionBuilderForm.resetFields();
                                setFunctionBuilderVisible(false);
                            })
                            .catch((e) => console.log(e));
                    }}
                >
                    <AntForm.Item label="Funtion Type" name="function_type_id" required>
                        <Select>
                            {funcType &&
                                funcType.map((value, index) => (
                                    <Select.Option key={value.id} value={value.id}>
                                        {value.function_name}
                                    </Select.Option>
                                ))}
                        </Select>
                    </AntForm.Item>
                    <AntForm.Item label="Reference Field" name="reference_field_id" >
                        <Select>
                            {referenceField &&
                                referenceField.map((value, index) => (
                                    <Select.Option key={value.id} value={value.id}>
                                        {value.name}
                                    </Select.Option>
                                ))}
                        </Select>
                    </AntForm.Item>
                    <AntForm.Item
                        label="Name"
                        name="name"
                        required
                        rules={[{ validator: validateWhiteSpace }]}
                    >
                        <Input placeholder="Name" />
                    </AntForm.Item>
                    <AntForm.Item label="Label" name="label" required>
                        <Input placeholder="Label" />
                    </AntForm.Item>
                    <AntForm.Item>
                        <Button htmlType="submit" style={{ color: "#fff", backgroundColor: "#35A989" }}>
                            Save Function
                        </Button>
                    </AntForm.Item>
                </AntForm>
            </Spin>
        </Modal >
    );
};
