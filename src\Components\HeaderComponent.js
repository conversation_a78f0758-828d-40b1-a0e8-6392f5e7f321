import React, { useState } from "react";
import { Layout, Menu, Form, Modal, Input, notification, Space, Button, Row, Col, Dropdown } from "antd";
import {
    MenuFoldOutlined,
    MenuUnfoldOutlined,
    UserSwitchOutlined,
    QuestionCircleOutlined
} from "@ant-design/icons";
import { useDispatch } from "react-redux";
import { logoutUser } from "../Actions/UserActions";
import apiClient from "../Shared/apiClient";

const { Header } = Layout;
const openNotificationWithIcon = (type, message) => {
    notification[type]({
        message: type === 'success' ? 'Success' : 'Error',
        description: message
    });
};

const btnStyle = {
    boxShadow: "none",
    background: "none"
}

const HeaderComponent = ({ setToggled, User, isToggled }) => {
    const [passwordModal, setPasswordModal] = useState(false);
    const dispatch = useDispatch();
    let [form] = Form.useForm();

    const handlePasswordModal = () => {
        setPasswordModal(true);
    };

    const onChangePassword = (values) => {
        apiClient.post('/api/changePassword', values).then((res) => {
            openNotificationWithIcon('success', res.data)
            form.resetFields()
            setPasswordModal(!passwordModal)
            dispatch(logoutUser())
        }).catch(err => {
            openNotificationWithIcon('error', err.response?.data?.message)
            form.resetFields()
            setPasswordModal(!passwordModal)
        })
    }

    const menu = (
        <Menu>
            <Menu.Item key={1} onClick={handlePasswordModal}>Change Password</Menu.Item>
            <Menu.Item key={2} onClick={() => dispatch(logoutUser())}>Logout</Menu.Item>
        </Menu>
    )

    return (
        <>
            <Header
                style={{
                    padding: 0,
                    background: '#fff'
                }}
            >
                <Row justify="space-between" style={{ padding: '0 20px' }}>
                    <Col>
                        <Button
                            icon={isToggled ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                            style={{ ...btnStyle, border: "none", color: '#000' }}
                            onClick={() => setToggled(!isToggled)}
                        />
                    </Col>
                    <Col>
                        <Space>
                            <Button
                                shape="circle"
                                title="Help"
                                icon={<QuestionCircleOutlined />}
                                style={btnStyle}
                                href="https://manuals.telecard.com.pk/admin"
                                target="_blank"
                            />

                            <Dropdown overlay={menu}>
                                <Button icon={<UserSwitchOutlined />} style={{ ...btnStyle, border: 'none' }}>
                                    {sessionStorage.getItem('username')}
                                </Button>
                            </Dropdown>
                        </Space>
                    </Col>
                </Row>

            </Header>
            <ChangePasswordModal
                visible={passwordModal}
                handleOk={onChangePassword}
                form={form}
                onCancel={() => {
                    setPasswordModal(!passwordModal);
                    form.resetFields();
                }}
            />
        </>
    );
};

const ChangePasswordModal = ({ visible, handleOk, onCancel, form }) => {
    return (
        <Modal
            title="Change Password"
            visible={visible}
            onOk={() => {
                form.validateFields().then((values) => {
                    handleOk(values);
                });
            }}
            onCancel={onCancel}
        >
            <Form size="large" form={form}>
                <Form.Item
                    name="current_password"
                    rules={[
                        {
                            required: true,
                            message: "Please input your password!",
                        },
                    ]}
                >
                    <Input.Password placeholder="Enter Current Password." />
                </Form.Item>

                <Form.Item
                    name="new_password"
                    rules={[
                        {
                            required: true,
                            message: "Please input your password!",
                        },
                    ]}
                    hasFeedback
                >
                    <Input.Password placeholder="Enter New Password." />
                </Form.Item>

                <Form.Item
                    name="new_confirm_password"
                    dependencies={["new_password"]}
                    hasFeedback
                    rules={[
                        {
                            required: true,
                            message: "Please confirm your password!",
                        },
                        ({ getFieldValue }) => ({
                            validator(_, value) {
                                if (!value || getFieldValue("new_password") === value) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(
                                    new Error("The two passwords that you entered do not match!")
                                );
                            },
                        }),
                    ]}
                >
                    <Input.Password placeholder="Enter Confirm Password." />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default HeaderComponent;
