import * as ActionTypes from "../Constants/FormFieldConstants"
import apiClient from "../Shared/apiClient"
import { FORM_FIELD } from "../Endpoints/FormFieldRoutes"

const handleError = error => {
    if (error.response) {
        return error.response.data
    } else {
        return error.message
    }
}

export const getFormFields = form => dispatch => {
    dispatch(formFieldLoading())
    apiClient.get(`/api/${form}/formField`).then(d => dispatch(formFieldsSuccess(d.data))).catch(e => dispatch(formFieldFailed(handleError(e))))
}

export const postFormFields = (data, formId) => dispatch => {
    console.log(data)
    dispatch(formFieldLoading())
    apiClient.post(`/api/${formId}/formField`, data).then(d => dispatch(formFieldSuccess(d.data))).catch(e => dispatch(formFieldFailed(handleError(e))))
}

export const deleteFormField = (formFieldId, formId) => dispatch => {
    dispatch(formFieldLoading())
    apiClient.delete(`/api/${formId}/formField/${formFieldId}`).then(d => dispatch(formFieldSuccess(d.data))).then(() => dispatch(getFormFields(formId))).catch(e => dispatch(formFieldFailed(handleError(e)))).catch(e => dispatch(formFieldFailed(handleError(e))))
}

const formFieldSuccess = message => ({
    type: ActionTypes.FORM_FIELD_SUCCESS,
    payload: message
})

const formFieldFailed = err => ({
    type: ActionTypes.FORM_FIELD_FAILED,
    payload: err
})

const formFieldLoading = () => ({
    type: ActionTypes.FORM_FIELD_LOADING
})

const formFieldsSuccess = fields => ({
    type: ActionTypes.FORM_FIELDS_SUCCESS,
    payload: fields
})