import * as ActionsType from '../Constants/InboundRoutesConstant'
const initalElemnt = {
    isLoading: false,
    errMess: null,
    message: null,
    inbound: []
}

export const InboundRoutesReducer = (state = initalElemnt, action) =>
{
    switch (action.type)
    {
        default:
            return {...state}
        case ActionsType.CREATE_SUCCESS:
            return {...state, isLoading: false, message: action.payload, errMess: null}
        case ActionsType.UPDATE_SUCCESS:
            return {...state, isLoading: false, message: action.payload, errMess: null}
        case ActionsType.DELETE_SUCCESS:
            return {...state, isLoading: false, message: action.payload, errMess: null}
        case ActionsType.INBOUND_SUCCESS:
            return {...state, isLoading: false, inbound: action.payload, errMess: null}
        case ActionsType.INBOUND_LOADING:
            return {...state, isLoading: true}
        case ActionsType.INBOUND_FAILED:
            return {...state, errMess: action.payload}
    }
}