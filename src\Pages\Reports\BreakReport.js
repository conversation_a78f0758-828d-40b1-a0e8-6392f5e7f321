import { useDispatch, useSelector } from "react-redux";
import React, { useEffect, useRef, useState } from "react";
import { getAgent } from "../../Actions/AgentActions";
import apiClient from "../../Shared/apiClient";

import { openNotificationWithIcon } from "../../Shared/notification";
import { Button, DatePicker, Form, Input, Modal, Select, Space, Spin, Table } from "antd";
import {
    CloudDownloadOutlined,
    DownloadOutlined,
    FilterOutlined,
    ReloadOutlined,
    SaveOutlined,
    SearchOutlined,
    SyncOutlined
} from "@ant-design/icons";
import { CSVLink } from "react-csv";
import { getAccountCodes, getCallStatuses } from "../../Actions/CallStatusActions";
import { BreakReportReducer } from "../../Reducer/BreakReportReducer";
import { breakReportReset, getBreakReport, getBreakReportFiltered, getColumn } from "../../Actions/BreakReportActions";
import { PauseResonReducer } from "../../Reducer/PauseReasonReducer";
import { getPauseReason } from "../../Actions/PauseReasonActions";
import Highlighter from "react-highlight-words";


export const BreakReport = () => {

    const [form] = Form.useForm()

    const breakReportReducer = useSelector(state => state?.BreakReportReducer)
    const [showFilter, setShowFilter] = useState(false)
    const [resetFilter, setResetFilter] = useState(false)
    const [visible, setVisible] = useState(false)
    const dispatch = useDispatch()
    const agentState = useSelector(state => state.AgentReducer)
    const [columns, setColumns] = useState([])

    const [searchText, setSearchText] = useState("");
    const [searchedColumn, setSearchedColumn] = useState("");
    const [data, setData] = useState()
    useEffect(() => {
        dispatch(getAgent())
        dispatch(getColumn())
        // dispatch(getBreakReport(null))
    }, [])

    const searchInput = useRef();

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({
            setSelectedKeys,
            selectedKeys,
            confirm,
            clearFilters,
        }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={(e) =>
                        setSelectedKeys(e.target.value ? [e.target.value] : [])
                    }
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: "block" }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button
                        onClick={() => handleReset(clearFilters)}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0]);
                            setSearchedColumn(dataIndex);
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined style={{ color: filtered ? "#1890ff" : undefined }} />
        ),
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex]
                    .toString()
                    .toLowerCase()
                    .includes(value.toLowerCase())
                : "",
        onFilterDropdownVisibleChange: (visible) => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: (text) =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: "#ffc069", padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ""}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0]);
        setSearchedColumn(dataIndex);
    };

    const handleReset = (clearFilters) => {
        clearFilters({ confirm: true });
        setSearchText("");
    };

    useEffect(() => {
        if (breakReportReducer.errMess !== '') openNotificationWithIcon('error', breakReportReducer.errMess)
    }, [breakReportReducer.errMess])

    const resetFormFilter = () => {
        // dispatch(getBreakReport(null))
        dispatch(breakReportReset())
        form.resetFields()
        setResetFilter(true)
    }

    let exportHeaders = breakReportReducer?.data?.length > 0 ? Object.keys(breakReportReducer?.data[0]).map((key) => {
        return {
            label: key,
            key: key
        }
    }) : []

    const toCapitalize = (str = "") => {
        const toSmall = str.toLowerCase()
        const word = toSmall.charAt(0).toUpperCase() + toSmall.slice(1)
        return word
    }

    // const exportHeaders = [
    //     { label: 'Agent Name', key: 'agentName' },
    //     { label: 'Time In', key: 'Time-in' },
    //     { label: 'Time Out', key: 'Time-out' },
    //     { label: 'Status', key: 'Status' },
    // ]


    useEffect(() => {

        const filteredColumns = breakReportReducer.columns.filter((v) => ((v != 'Others') && (v != 'Busy')))

        if (breakReportReducer.data[1]) {
            setColumns(breakReportReducer.data?.map(v => ({
                title: v,
                dataIndex: v,
                key: v,
                ...getColumnSearchProps(v)
            })), ...columns)
        }
        if (filteredColumns) {
            setColumns(filteredColumns?.map((v) => {
                // if ((v != "Others")) {
                return (
                    {
                        title: v,
                        dataIndex: v,
                        key: v,
                        ...getColumnSearchProps(v)
                    }
                )
                // }
            }

            ), ...columns)
        }



    }, [breakReportReducer])

    // const columns = [
    //     {
    //         title: "Agent Id",
    //         dataIndex: "AgentId",
    //         key: "AgentId",
    //         ...getColumnSearchProps('AgentId')
    //     },
    //     {
    //         title: "Agent Name",
    //         dataIndex: "AgentName",
    //         key: "AgentName",
    //         ...getColumnSearchProps('AgentName')
    //     },
    //     // {
    //     //     title: "After login set not ready",
    //     //     dataIndex: "After login set not ready",
    //     //     key: "After login set not ready",
    //     //     // ...getColumnSearchProps('After login set not ready')
    //     // },
    //     {
    //         title: "Bio Break",
    //         dataIndex: "Bio Break",
    //         key: "Bio Break",
    //         // ...getColumnSearchProps('Bio Break')
    //     },
    //     {
    //         title: "Counselling/Training",
    //         dataIndex: "Counselling/Training",
    //         key: "Counselling/Training",
    //         // ...getColumnSearchProps('Counselling/Training'),

    //     },
    //     {
    //         title: "Namaz with lunch break",
    //         dataIndex: "Namaz with lunch break",
    //         key: "Namaz with lunch break",
    //         // ...getColumnSearchProps('Namaz with lunch break'),

    //     },
    //     {
    //         title: "Working on CXM",
    //         dataIndex: "Working on CXM",
    //         key: "Working on CXM",

    //     },
    //     {
    //         title: "Support on HFC Desk",
    //         dataIndex: "Support on HFC Desk",
    //         key: "Support on HFC Desk",
    //         // ...getColumnSearchProps('Support on HFC Desk'),

    //     },
    //     {
    //         title: "Total",
    //         dataIndex: "Total",
    //         key: "Total",


    //     },
    // ];





    // useEffect(() => {
    //     console.log("aa", columns)
    //     console.log('break', breakReportReducer.data[1])
    // }, [])

    return (
        <>

            <Table
                title={data => <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>

                    Break Report
                    <Space>
                        <Button onClick={() => resetFormFilter()} type="danger" icon={<ReloadOutlined />}>Reset Filter</Button>
                        <Button onClick={() => setShowFilter(true)} icon={<FilterOutlined />}>Filter</Button>
                        <CSVLink data={breakReportReducer?.data || []} headers={columns.map(col => {
                            return {
                                key: col.dataIndex,
                                label: col.title
                            }
                        })} filename="BreakReport.csv">
                            <Button
                                type={"primary"}
                                target="_blank"
                                icon={<DownloadOutlined />}
                                disabled={breakReportReducer.data == ''}>
                                Download</Button>
                        </CSVLink>
                    </Space>
                </div>}
                dataSource={breakReportReducer?.data || []}
                columns={columns}
                rowKey={(v) => v?.AgentId}
                bordered
                scroll={{ x: 1100 }}
                loading={{ spinning: breakReportReducer.isLoading, indicator: <SyncOutlined spin /> }}
            />
            {/*<PlayAudio visible={visible} setVisible={setVisible} />*/}
            <BreakReportFilter form={form} data={data} setData={setData} resetFilter={resetFilter} btnLoading={breakReportReducer.isLoading} visible={showFilter} setVisible={setShowFilter} />
        </>
    )
}

export const BreakReportFilter = ({ visible, setVisible, data, setData, btnLoading, resetFilter, form }) => {

    const [date, setDate] = useState();
    const pauseReasonState = useSelector(state => state.PauseResonReducer)
    const agentState = useSelector(state => state.AgentReducer)
    const dispatch = useDispatch()

    const [queues, setQueues] = useState([])

    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data))
    }, [])
    const options = agentState?.users?.map((value, idx) => {

        return (
            {

                value: value.username,
                label: value.name
            }

        )
    })

    console.log("options check", options)

    useEffect(() => {
        dispatch(getPauseReason())
        dispatch(getAccountCodes())
        console.log("check", agentState.users)
    }, [])
    useEffect(() => dispatch(getAgent()), [])
    // useEffect(() => form.resetFields(), [resetFilter])

    function handleDate(value, param) {
        setDate(param)
    }

    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            okText="Submit"
            onOk={() => form.validateFields()
                .then(value => {
                    const data = { ...value, date: date }
                    dispatch(getBreakReport(data))
                    setVisible(false)
                })
            }
            okButtonProps={{
                loading: btnLoading,
                icon: <SaveOutlined />
            }}
            title="Break Report Filter"
        >
            <Spin spinning={agentState.isLoading || pauseReasonState.isLoading} indicator={<SyncOutlined spin />}>
                <Form
                    form={form}
                    layout="vertical"
                >
                    <Form.Item name="date" label="Date">
                        <DatePicker onChange={handleDate} style={{ width: '100%' }} />
                    </Form.Item>
                    <Form.Item name="agents" label="Agents">
                        <Select mode="multiple" placeholder="Select agents">
                            {agentState.users && agentState.users.map((value, index) => <Select.Option key={value.id} value={value.name}>{value.name}</Select.Option>)}
                        </Select>
                    </Form.Item>
                    <Form.Item name="breaks" label="Breaks">
                        <Select
                            showSearch
                            mode="multiple" placeholder="Select agents">
                            {pauseReasonState && pauseReasonState.pauseReason.map((value, index) => <Select.Option key={index} value={value.name}>{value.name}</Select.Option>)}
                        </Select>
                    </Form.Item>
                    <Form.Item colon={false} name="queue" label="Queue" >
                        <Select placeholder="Select Queue" >
                            {queues && queues.map((queue, index) => <Select.Option key={index} value={queue.name}>
                                {queue.name}
                            </Select.Option>)}
                        </Select>
                    </Form.Item>
                </Form>
            </Spin>
        </Modal>
    )
}