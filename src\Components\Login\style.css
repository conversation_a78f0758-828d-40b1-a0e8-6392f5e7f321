.top-heading {
  font-size: 32px;
  font-weight: 500;
  width: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #15347c;
  font-weight: bold;
  /* margin-bottom: 20px; */
}
/* llll  */
.container {
  position: relative;
  width: 100%;
  background-color: #fff;
  min-height: 100vh;
  overflow: hidden;
}
/* background-image: url("../../Assets/logins.jpg"); */
.forms-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 50px;
  background-image: url("../../Assets/bg-cover.jpg");
  background-size: 100% 100%;

  /* position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgb(255, 253, 253); */
}
/* .signin-signup {
position: absolute;
top: 50%;
transform: translate(-50%, -50%);
left: 130%;

width: 440px;
transition: 1s 0.7s ease-out;
display: grid;
grid-template-columns: 1fr;
background: #fff;
box-shadow: 0 12px 48px rgba(26,33,52,.11);
z-index: 5;
border-radius: 24px;
} */

.signin-signup {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  right: 130%;
  /* width: 50%; */
  width: 440px;
  transition: 1s 0.7s ease-out;
  display: grid;
  grid-template-columns: 1fr;
  background: #fff;
  box-shadow: 0 12px 48px rgba(26, 33, 52, 0.11);
  z-index: 5;
  border-radius: 24px;
}

/* .signin-signup2 {
position: absolute;
top: 50%;
transform: translate(-50%, -50%);
transition: 1s 0.7s ease-out;

left: 75% !important;

width: 440px;
display: grid;
grid-template-columns: 1fr;
background: #fff;
box-shadow: 0 12px 48px rgba(26,33,52,.11);
z-index: 5;
border-radius: 24px;
} */
.signin-signup2 {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  left: 85% !important;
  /* width: 50%; */
  width: 440px;
  /* transition: 1s ease-in; */
  transition: 1s ease-out;
  display: grid;
  grid-template-columns: 1fr;
  background: none;
  box-shadow: none;
  z-index: 5;
  border-radius: 24px;
}

#sign-in {
  display: flex;
  align-items: center;
  gap: 30px;
  justify-content: center;
  flex-direction: column;
  padding: 2rem 3rem;
  transition: all 0.2s 0.7s;
  overflow: hidden;
  grid-column: 1 / 2;
  grid-row: 1 / 2;
}
.title {
  font-size: 2.2rem;
  color: #444;
  margin-bottom: 10px;
}
.panels-container {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}
.container:before {
  content: "";
  position: absolute;
  height: 2000px;
  width: 2000px;
  top: 10%;
  right: 48%;
  transform: translateY(-45%);
  /* background-image: url("../../Assets/logins-removebg-preview.png"); */
  background-size: 59.5%;
  /* background-repeat: no-repeat; */
  transition: 1.8s ease-in-out;
  border-radius: 50%;
  z-index: 6;
}
.image {
  /* left: 2%; */
  /* position: absolute; */
  /* width: 100%; */
  max-width: 100%;
  max-height: 100%;
  /* transition: transform 1.1s ease-in-out; */
  /* transform: translate(-50%, -50%); */
  transition: 1s 0.9s ease-out;
  transition-delay: 0.4s;
}
.image2 {
  /* width: 100%; */
  left: 50%;
  position: absolute;
  width: 600px;
  /* transition: transform 1.1s ease-in-out; */
  /* transform: translate(-50%, -50%); */
  transition: 1s 0.9s ease-out;
  transition-delay: 0.4s;
}
.panel {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-around;
  text-align: center;
  z-index: 6;
  width: 100%;
  margin-left: -700px;
  transition: 1s 0.7s ease-out;
}

.panel2 {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-around;
  text-align: center;
  z-index: 6;
  width: 100%;
  margin-left: -40px;
  /* transition: 1s ease-in; */
  transition: 1s ease-out;
}

.left-panel {
  pointer-events: all;
  padding: 3rem 17% 2rem 12%;
}
.right-panel {
  pointer-events: none;
  padding: 3rem 12% 2rem 17%;
}
.panel .content {
  color: #fff;
  transition: transform 0.9s ease-in-out;
  transition-delay: 0.6s;
}
.panel h3 {
  font-weight: 600;
  line-height: 1;
  font-size: 1.5rem;
}
.panel p {
  font-size: 0.95rem;
  padding: 0.7rem 0;
}
.btn.transparent {
  margin: 0;
  background: none;
  border: 2px solid #fff;
  width: 130px;
  height: 41px;
  font-weight: 600;
  font-size: 0.8rem;
}
.right-panel .image,
.right-panel .content {
  transform: translateX(800px);
}
/* ANIMATION */
.container.sign-up-mode:before {
  transform: translate(100%, -50%);
  right: 52%;
}
.container.sign-up-mode .left-panel .image,
.container.sign-up-mode .left-panel .content {
  transform: translateX(-800px);
}
.container.sign-up-mode .signin-signup {
  left: 25%;
}
.container.sign-up-mode form.sign-in-form {
  opacity: 0;
  z-index: 1;
}
.container.sign-up-mode .right-panel .image,
.container.sign-up-mode .right-panel .content {
  transform: translateX(0%);
}
.container.sign-up-mode .left-panel {
  pointer-events: none;
}
.container.sign-up-mode .right-panel {
  pointer-events: all;
}
@media (max-width: 870px) {
  .container {
    min-height: 800px;
    height: 100vh;
  }
  .signin-signup {
    width: 100%;
    top: 95%;
    transform: translate(-50%, -100%);
    transition: 1s 0.8s ease-in-out;
  }
  .signin-signup,
  .container.sign-up-mode .signin-signup {
    left: 50%;
  }
  .panels-container {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 2fr 1fr;
  }
  .panel {
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    padding: 2.5rem 8%;
    grid-column: 1 / 2;
  }
  .right-panel {
    grid-row: 3 / 4;
  }
  .left-panel {
    grid-row: 1 / 2;
  }
  .image {
    width: 200px;
    transition: transform 0.9s ease-in-out;
    transition-delay: 0.6s;
  }
  .panel .content {
    padding-right: 15%;
    transition: transform 0.9s ease-in-out;
    transition-delay: 0.8s;
  }
  .panel h3 {
    font-size: 1.2rem;
  }
  .panel p {
    font-size: 0.7rem;
    padding: 0.5rem 0;
  }
  .btn.transparent {
    width: 110px;
    height: 35px;
    font-size: 0.7rem;
  }
  .container:before {
    width: 1500px;
    height: 1500px;
    transform: translateX(-50%);
    left: 30%;
    bottom: 68%;
    right: initial;
    top: initial;
    transition: 2s ease-in-out;
  }
  .container.sign-up-mode:before {
    transform: translate(-50%, 100%);
    bottom: 32%;
    right: initial;
  }
  .container.sign-up-mode .left-panel .image,
  .container.sign-up-mode .left-panel .content {
    transform: translateY(-300px);
  }
  .container.sign-up-mode .right-panel .image,
  .container.sign-up-mode .right-panel .content {
    transform: translateY(0px);
  }
  .right-panel .image,
  .right-panel .content {
    transform: translateY(300px);
  }
  .container.sign-up-mode .signin-signup {
    top: 5%;
    transform: translate(-50%, 0);
  }
}
@media (max-width: 570px) {
  form {
    padding: 0 1.5rem;
  }
  .image {
    display: none;
  }
  .panel .content {
    padding: 0.5rem 1rem;
  }
  .container {
    padding: 1.5rem;
  }
  .container:before {
    bottom: 72%;
    left: 50%;
  }
  .container.sign-up-mode:before {
    bottom: 28%;
    left: 50%;
  }
  .signin-signup2 {
    position: static;
    /* top: 50%; */
    transform: none;
    width: 100%;
    margin-top: 180px;
    /* width: 50%; */
    /* width: 440px; */
  }
}

.login3-container {
  height: 100vh;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  /* background-image: url("../../Assets/logins.jpg"); */
  background-size: 50% 100%;
  background-repeat: no-repeat;
  background: "#6a7987";
  color: "#fff";
}

.login-form {
  position: absolute;
  animation: loginform 5s;
  right: 0;
}

@keyframes loginform {
  from {
    right: -10%;
  }
  to {
    right: 0;
  }
}
