import * as ActionTypes from "../Constants/UserConstants"
import apiClient from "../Shared/apiClient";
import { getUser, login, logout } from "../Endpoints/UserRoutes";
import { handleError } from "../Shared/handleError";

export const loginUser = (username, password) => dispatch => {
    dispatch(loading())
    apiClient.post(login, {
        username,
        password
    }).then(response => {
        sessionStorage.setItem('auth_token', response?.data?.token)
        apiClient.get(getUser).then(response => {
            
            const userData = response?.data;
            const roleName = userData?.roles?.[0]?.name || null;
            sessionStorage.setItem('role', roleName);

            dispatch(userSuccess(response.data))
        }).catch(error => {
            if (error.response)
                dispatch(userFailed(error.response))
            else
                dispatch(userFailed(error.message))
        })
    }).catch(error => {
        if (error.response)
            dispatch(userFailed(error.response))
        else
            dispatch(userFailed(error.message))
    })

}

export const logoutUser = () => dispatch => {
    dispatch(loading())
    apiClient.post(logout)
        .then(r => dispatch(logoutSuccess()))
        .then(() => sessionStorage.clear())
        .then(() => modifyElementsOnLogout())
        .catch(e => {
            if (e.response) {
                if (e.response.status === 401) {
                    sessionStorage.clear()
                    window.location.reload()
                }
            }
        })
}

const modifyElementsOnLogout = () => {
    setTimeout(() => {
        const old = document.getElementById("perl");
        const panel = document.getElementById("pp");

        panel.classList.remove('panel');
        old.classList.remove('signin-signup');
        old.classList.add('signin-signup2');
        panel.classList.add('panel2');
    }, 500)
};

const loading = () => ({
    type: ActionTypes.LOGIN_LOADING
})

export const userSuccess = (user) => ({
    type: ActionTypes.LOGIN_SUCCESS,
    payload: user
})

export const userFailed = error => ({
    type: ActionTypes.LOGIN_FAILED,
    payload: error
})

export const logoutSuccess = () => ({
    type: ActionTypes.LOGOUT_SUCCESS
})

export const logoutFailed = error => ({
    type: ActionTypes.LOGOUT_FAILED,
    payload: error
})