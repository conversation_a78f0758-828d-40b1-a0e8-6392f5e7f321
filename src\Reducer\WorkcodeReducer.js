import * as ActionTypes from "../Constants/WorkcodeConstant"

const initalElements = {
    workcode: [],
    errMess: null,
    isLoading: false,
    message: null
}

export const WorkcodeReducer = (state = initalElements, action) =>
{
    switch (action.type){
        default:
            return state
        case ActionTypes.WORKCODE_FAILED:
            return { ...state, isLoading: false, message: null, errMess: action.payload}
        case ActionTypes.WORKCODE_SUCCESS:
            return {...state, isLoading: false, workcode: action.payload, message: null ,errMess: null}
        case ActionTypes.WORKCODE_CREATE_SUCCESS:
            return {...state, isLoading: false, message: action.payload, errMess: null}
        case ActionTypes.WORKCODE_UPDATE_SUCCESS:
            return {...state, isLoading: false, message: action.payload, errMess: null}
        case ActionTypes.WORKCODE_DELETE_SUCCESS:
            return {...state, isLoading: false, message: action.payload, errMess: null}
        case ActionTypes.WORKCODE_LOADING:
            return {...state, isLoading: true}

    }
}