import * as ActionTypes from "../Constants/AgentReportsContant"

const initialState = {
    agentReport: [],
    errMess: null,
    isLoading: false,
    message: '',
    pagination: {}
}

export const AgentReportReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.AGENT_REPORTS_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.AGENT_REPORTS_SUCCESS:
            // return {...state, isLoading: false, agentReport: action.payload}
            return { ...state, isLoading: false, agentReport: action.payload, pagination: { total: action.payload.total } }
        case ActionTypes.AGENT_REPORTS_FAILED:
            return { ...state, isLoading: false, errMess: action.payload }
        case ActionTypes.AGENT_NO_ANSWER_SUMMARY:
            return { ...state, isLoading: false, agentReport: action.payload }
        case ActionTypes.AGENT_REPORT_FILTER:
            // return {...state, isLoading: false, agentReport: action.payload}
            return { ...state, isLoading: false, agentReport: action.payload, pagination: { total: action.payload.total } }
        case ActionTypes.AGENT_PAUSE_REASON_REPORT:
            return { ...state, isLoading: false, agentReport: action.payload }
        case ActionTypes.AGENT_PAUSE_REASON_REPORT_FILTER:
            return { ...state, isLoading: false, agentReport: action.payload }
        case ActionTypes.AGENT_CALL_PER_AGENT:
            return { ...state, isLoading: false, agentReport: action.payload }
        case ActionTypes.AGENT_CALLER_HANGUP:
            return { ...state, isLoading: false, agentReport: action.payload }
        case ActionTypes.AGENT_CALLER_HANGUP_FILTER:
            return { ...state, isLoading: false, agentReport: action.payload }
        case ActionTypes.AGENT_RING_NO_ANSWER:
            return { ...state, isLoading: false, agentReport: action.payload.data, pagination: { current: action.payload.current_page, pegesize: action.payload.per_page, total: action.payload.total } }
        case ActionTypes.AGENT_RING_NO_ANSWER_RESET:
            return { ...state, isLoading: false, agentReport: [], pagination: { current: 1, pegesize: 1, total: 0 } }
        case ActionTypes.AGENT_RING_NO_ANSWER_FILTER:
            return { ...state, isLoading: false, agentReport: action.payload.data, pagination: { current: action.payload.current_page, pegesize: action.payload.per_page, total: action.payload.total } }
        case ActionTypes.AGENT_EXIT_EMPTY:
            return { ...state, isLoading: false, agentReport: action.payload }
        case ActionTypes.AGENT_EXIT_EMPTY_FILTER:
            return { ...state, isLoading: false, agentReport: action.payload }
        case ActionTypes.AGENT_EXIT_WITH_TIMEOUT:
            return { ...state, isLoading: false, agentReport: action.payload }
        case ActionTypes.AGENT_EXIT_WITH_TIMEOUT_FILTER:
            return { ...state, isLoading: false, agentReport: action.payload }
        case ActionTypes.AGENT_CONFIGURED_LOAD:
            return { ...state, isLoading: false, agentReport: action.payload }
        case ActionTypes.AGENT_CONFIGURED_LAOD_FILTER:
            return { ...state, isLoading: false, agentReport: action.payload }
        case ActionTypes.AGENT_COMPLETE_CALLER:
            return { ...state, isLoading: false, agentReport: action.payload }
        case ActionTypes.AGENT_COMPLETE_CALLER_FILTER:
            return { ...state, isLoading: false, agentReport: action.payload }
        case ActionTypes.AGENT_COMPLETE_AGENT:
            return { ...state, isLoading: false, agentReport: action.payload }
        case ActionTypes.AGENT_COMPLETE_AGENT_FILTER:
            return { ...state, isLoading: false, agentReport: action.payload }
    }
}