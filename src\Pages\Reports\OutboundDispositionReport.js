import { Button, DatePicker, Form, Input, Modal, Select, Space, Spin, Table, Typography } from "antd";
import { useDispatch, useSelector } from "react-redux";
import React, { useEffect, useRef, useState } from "react";
import { getFilteredOutboundDisposition, getOutboundDisposition, outboundDispositionReset } from "../../Actions/OutboundDispositionActions";
import { DownloadOutlined, FilterOutlined, ReloadOutlined, SearchOutlined, SettingOutlined, SyncOutlined } from "@ant-design/icons";
import Highlighter from "react-highlight-words";
import { CSVLink } from "react-csv";
import { getCallStatuses } from "../../Actions/CallStatusActions";
import { getAgent } from "../../Actions/AgentActions";
import { getAgentCallReport } from "../../Actions/AgentCallReportActions";
import apiClient from "../../Shared/apiClient";

export const OutboundDispositionReport = () => {

    const [filterVisible, setFilterVisible] = useState(false)
    const [resetFilter, setResetFilter] = useState(false)
    const [filterValues, setFilterValues] = useState(null)
    const outboundDispositionState = useSelector(state => state.OutboundDispositionReducer)
    const dispatch = useDispatch()

    const resetFormFilter = () => {
        dispatch(outboundDispositionReset())
        setResetFilter(true)
    }

    // useEffect(() => dispatch(getOutboundDisposition()), [])

    const [searchText, setSearchText] = useState('')

    const [searchedColumn, setSearchedColumn] = useState('')
    const searchInput = useRef()

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }

    const columns = [
        {
            title: 'Unique Id',
            dataIndex: 'uniqueid',
            key: 'uniqueid'
        },
        // {
        //     title: 'Source',
        //     dataIndex: 'channel',
        //     key: 'channel',
        //     ...getColumnSearchProps('channel')
        // },
        {
            title: 'Source',
            dataIndex: 'Agent',
            key: 'Agent',
            ...getColumnSearchProps('Agent')
        },
        {
            title: 'Destination',
            dataIndex: 'dst',
            key: 'dst',
            ...getColumnSearchProps('dst')
        },
        {
            title: 'Date Time',
            dataIndex: 'start',
            key: 'start'
        },
        {
            title: 'Call Status',
            dataIndex: 'disposition',
            key: 'disposition',
            ...getColumnSearchProps('disposition')
        },
        {
            title: 'Workcode',
            dataIndex: 'workcode',
            key: 'workcode',
            ...getColumnSearchProps('workcode')
        }
    ]

    // const exportCsv = async () => {
    //     // .then((res) => console.log("response", res))
    //     const response = await apiClient.post('/api/report/export-outbound-disposition', { "range": filterValues })
    //     console.log("response", response)

    // }



    // const exportCsv = () => {

    //     // setLoading(true)
    //     apiClient.post('/api/report/export-outbound-disposition', { "range": filterValues })
    //         .then((response) => {
    //             const type = response.headers['content-type']
    //             const blob = new Blob([response.data], { type: type, encoding: 'UTF-8' })
    //             const link = document.createElement('a')
    //             link.href = window.URL.createObjectURL(blob)
    //             link.download = 'CLI-Recording-Report.csv'
    //             link.click()
    //             // setLoading(false)
    //         }).catch((e) => {
    //             // setLoading(false)
    //             console.log("Export error", e)
    //         })

    // }

    const exportCsv = () => {
        // setLoading(true);
        apiClient.post('/api/report/export-outbound-disposition',
            { "range": filterValues },
            { responseType: 'blob' }).then((response) => {
                const type = response.headers['content-type']; const blob = new Blob([response.data], { type: type }); const link = document.createElement('a'); link.href = window.URL.createObjectURL(blob); link.download = 'outbound-disposition.csv'; link.click();
            }).catch((e) => {
                // setLoading(false); 
                console.log("Export error", e);
            });
    }


    return (
        <>
            <Table
                title={data => {
                    return (

                        <span style={{ display: 'flex', justifyContent: 'space-between' }}>
                            Outbound Disposition Report
                            <Space>
                                <Button onClick={() => resetFormFilter()} danger type="primary" icon={<ReloadOutlined />}>Reset Filter</Button>
                                <Button onClick={() => setFilterVisible(true)} icon={<FilterOutlined />}>Filter</Button>

                                {/* <CSVLink filename="OutboundDispositionReport.csv"> */}
                                <Button onClick={() => { exportCsv() }} disabled={outboundDispositionState.data.length == 0} type={"primary"} target="_blank" icon={<DownloadOutlined />}>
                                    Download
                                </Button>

                                {/* </CSVLink> */}

                            </Space>
                        </span>
                    )
                }}
                loading={{ spinning: outboundDispositionState.isLoading, indicator: <Spin /> }}
                bordered
                dataSource={outboundDispositionState.data}
                columns={columns}
                summary={data => {

                    return (
                        <>
                            <Table.Summary.Row>
                                <Table.Summary.Cell colSpan={5}>Total</Table.Summary.Cell>
                                <Table.Summary.Cell>
                                    <Typography.Text strong>{data.length}</Typography.Text>
                                </Table.Summary.Cell>
                            </Table.Summary.Row>
                        </>
                    )
                }}
            />
            < OutboundDispositionFilter setFilterValues={setFilterValues} buttonLoading={outboundDispositionState.isLoading} visible={filterVisible} setVisible={setFilterVisible} resetField={resetFilter} />
        </>
    )
}

const OutboundDispositionFilter = ({ visible, setVisible, setFilterValues, buttonLoading, resetField }) => {
    const [form] = Form.useForm()
    const agentState = useSelector(state => state.AgentReducer)
    const callState = useSelector(state => state.CallStatusReducer)
    const dispatch = useDispatch()

    const [queues, setQueues] = useState([])

    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data))
    }, [])

    useEffect(() => {
        dispatch(getCallStatuses())
        dispatch(getAgent())
    }, [])

    useEffect(() => form.resetFields(), [resetField])


    return (
        // <Spin spinning={agentState.isLoading || callState.isLoading} indicator={<SettingOutlined />}>
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            title="Outbound disposition filter"
            size="small"
            onOk={() => form.validateFields()
                .then(data => {
                    // console.log("range", data?.range)
                    setFilterValues(data?.range)
                    dispatch(getFilteredOutboundDisposition(data))
                    setVisible(false)
                    form.resetFields()
                })
                .catch(e => console.log(e))
            }
            okText="Submit"
            oKButtonProps={{
                loading: buttonLoading
            }}
        >
            <Form
                layout="vertical"
                form={form}
            >
                <Form.Item name="range" label="Date Range">
                    <DatePicker.RangePicker style={{ width: '100%' }} />
                </Form.Item>
                <Form.Item name="agent" label="Agent">
                    <Select
                        mode="multiple"
                        filterOption={(input, option) =>
                            option.children.toLowerCase().includes(input.toLowerCase())
                        }
                    >
                        {agentState.users && agentState.users.map((value, index) => <Select.Option value={value.auth_username} key={value.username}>
                            {value.name}
                        </Select.Option>)}
                    </Select>
                </Form.Item>
                <Form.Item name="dst" label="Destination">
                    <Input />
                </Form.Item>
                <Form.Item name="call_status" label="Call Status">
                    <Select>
                        {callState.statuses && callState.statuses.map((value, index) => <Select.Option key={index} value={value.disposition}>{value.disposition}</Select.Option>)}
                    </Select>
                </Form.Item>
                {/* <Form.Item colon={false} name="queue" label="Queue" >
                    <Select placeholder="Select Queue" >
                        {queues && queues.map((queue, index) => <Select.Option key={index} value={queue.name}>
                            {queue.name}
                        </Select.Option>)}
                    </Select>
                </Form.Item> */}
            </Form>
        </Modal>
        // </Spin>
    )
}