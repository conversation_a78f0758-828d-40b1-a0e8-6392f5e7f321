import { useDispatch, useSelector } from "react-redux";
import { DatePicker, Form, Input, Modal, Select, Spin } from "antd";
import React, { useEffect, useState } from "react"
import { getQueues } from "../../Actions/QueueActions";
import { getAgent } from "../../Actions/AgentActions";

export const RingNoAnswerFilter = ({ visible, setVisible, onCreate, isLoading, setFilterOption }) => {
    const [form] = Form.useForm()
    const agentState = useSelector(state => state.AgentReducer)
    const QueueState = useSelector(state => state.QueueReducer)
    const [showQueue, setShowQueue] = useState(false)
    const dispatch = useDispatch()

    useEffect(() => {
        if (visible) {
            dispatch(getQueues())
            dispatch(getAgent())
            setShowQueue(true)
        }
    }, [visible])

    return (
        <Spin spinning={QueueState.isLoading || isLoading}>
            <Modal
                visible={visible}
                onCancel={() => {
                    form.resetFields()
                    setVisible(false)
                }}
                onOk={() => {
                    form
                        .validateFields()
                        .then((values) => {
                            setFilterOption({ ...values })
                            onCreate(values);
                            form.resetFields();
                        })
                        .catch((info) => {
                            console.log('Validate Failed:', info);
                        })
                }}
            >
                <Form
                    form={form}
                    layout="vertical"
                    name="form_in_modal"
                >
                    <Form.Item
                        name="date"
                        label="Date Time"
                    >
                        <DatePicker.RangePicker showTime />
                    </Form.Item>

                    <Form.Item name="agent" label="Agent">
                        <Select mode="multiple">
                            {agentState.users && agentState.users.map((value, index) => <Select.Option value={value.name} key={value.id}>
                                {value.name}
                            </Select.Option>)}
                        </Select>
                    </Form.Item>

                    <Form.Item
                        name="partya"
                        label="Party A"
                        rules={[
                            {
                                message: 'Please input the Party A',
                            },
                        ]}
                    >
                        <Input />
                    </Form.Item>

                    {showQueue && <Form.Item
                        name="queue"
                        label="Queue"
                        rules={[
                            {
                                message: 'Please select the queue',
                            },
                        ]}
                    >
                        <Select>
                            {QueueState.queues.map((value) => (
                                <Select.Option value={value.name} key={value.name}>{value.name}</Select.Option>
                            ))}
                        </Select>
                    </Form.Item>}

                </Form>

            </Modal>
        </Spin>
    );
}

export default RingNoAnswerFilter