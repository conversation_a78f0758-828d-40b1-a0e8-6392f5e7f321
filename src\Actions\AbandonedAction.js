import * as ActionTypes from "../Constants/OutboundActivityConstants";
import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";


export const getAbandonedCall = (value, pagination) => dispatch => {
    dispatch(loading())
    console.log(pagination['current'])
    apiClient.post(`/api/report/abandon-call?page=${pagination['current']}&pageSize=${pagination['pageSize']}`, value).then(r => dispatch(abandonedSuccess(r.data))).catch(e => dispatch(abandonedFailed(handleError(e))))
}

export const getAbandonedCallHour = (data = null) => dispatch => {
    dispatch(loading())
    apiClient.post(`/api/report/abandon-call-hours`, data).then(r => dispatch(abandonedperHourSuccess(r.data))).catch(e => dispatch(abandonedFailed(handleError(e))))
}

export const abandonCallReset = () => dispatch => {

    dispatch(abandonedReset());

}
export const abandonCallHourReset = () => dispatch => {

    dispatch(abandonedCallReset());

}

const loading = () => ({
    type: "ABANDONED_LOADING"
})
const abandonedperHourSuccess = data => ({
    type: "ABANDONED_PER_HOUR_SUCCESS",
    payload: data
})
const abandonedSuccess = data => ({
    type: "ABANDONED_SUCCESS",
    payload: data
})
const abandonedFailed = err => ({
    type: "ABANDONED_FAILED",
    payload: err
})
const abandonedReset = () => ({
    type: "ABANDONED_RESET"
})
const abandonedCallReset = () => ({
    type: "ABANDONEDCALL_RESET"
})