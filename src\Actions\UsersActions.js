import * as ActionTypes from "../Constants/UsersConstants"
import apiClient from "../Shared/apiClient";
import { GET_ALL_USERS, GetUsers } from "../Endpoints/UsersRoutes";
import { logoutUser } from "./UserActions";
import { settingFailed } from "./SettingActions";
import { openNotificationWithIcon } from "../Shared/notification";

export const getUsers = () => dispatch => {
    dispatch(usersLoading())
    apiClient.get(GetUsers).then(response => dispatch(usersSuccess(response.data)))
        .catch(error => {
            if (error.response) {
                if (error.response.status === 401)
                    dispatch(logoutUser())
                dispatch(usersFailed(error.response))
            }
            else
                dispatch(usersFailed(error.message))
        })
}

export const getAllUsers = () => dispatch => {
    dispatch(usersLoading())
    apiClient.post(GET_ALL_USERS).then(response => dispatch(getAll(response.data)))
        .catch(error => {
            if (error.response) {
                if (error.response.status === 401)
                    dispatch(logoutUser())
                dispatch(usersFailed(error.response))
            }
            else
                dispatch(usersFailed(error.message))
        })
}

export const createUser = data => dispatch => {
    dispatch(usersLoading())
    apiClient.post(GetUsers, data).then(response => dispatch(userSuccess(response.data)))
        .catch(error => {
            // openNotificationWithIcon('error', error?.response?.data?.message)

            if (error.response) {
                console.log("user error", error?.response?.data?.message)
                if (error.response.status === 401)
                    dispatch(logoutUser())
                dispatch(usersFailed(error?.response?.data?.message))

            }
            else {
                // openNotificationWithIcon('error', error?.response?.data?.message)
                dispatch(usersFailed(error?.response?.data?.message))
            }
        })
}

export const updateUser = (id, data) => dispatch => {
    dispatch(usersLoading())
    apiClient.put(`${GetUsers}/${id}`, data).then(response => dispatch(userSuccess(response.data)))
        .catch(error => {
            // openNotificationWithIcon()
            if (error.response) {
                if (error.response.status === 401)
                    dispatch(logoutUser())
                dispatch(usersFailed(error?.response?.data?.message))
            }
            else
                dispatch(usersFailed(error?.response?.data?.message))
        })
}

export const deleteUser = id => dispatch => {
    dispatch(usersLoading())
    apiClient.delete(`${GetUsers}/${id}`)
        .then(response => dispatch(userSuccess(response.data)))
        .catch(error => {
            if (error.response) {
                if (error.response.status === 401)
                    dispatch(logoutUser())
                dispatch(usersFailed(error.response))
            }
            else
                dispatch(usersFailed(error.message))
            console.log(error)
        })

}

const usersLoading = () => ({
    type: ActionTypes.USERS_LOADING
})

const usersSuccess = users => ({
    type: ActionTypes.USERS_SUCCESS,
    payload: users
})

const getAll = users => ({
    type: ActionTypes.GET_ALL_USERS,
    payload: users
})

const usersFailed = error => ({
    type: ActionTypes.USERS_FAILED,
    payload: error
})

const userSuccess = message => ({
    type: ActionTypes.USER_SUCCESS,
    payload: message
})


