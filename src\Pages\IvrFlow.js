import { DeleteOutlined, EditOutlined, SettingOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, Checkbox, Col, Divider, Form, Input, InputNumber, message, Row, Select, Space, Switch, Table, Tag, Upload } from 'antd'
import React, { useEffect, useState } from 'react'
import apiClient from '../Shared/apiClient';
import { options } from './Reports/ChannelOccupancy';
import { useDispatch, useSelector } from 'react-redux';
import { getIvrFlow } from '../Actions/IvrFlowActions';
import { useHistory } from "react-router";
import { openNotificationWithIcon } from '../Shared/notification';
import ContactFlowSettings from '../Components/ContactFlowSettings';


export default function IvrFlow() {


    const style = {
        background: '#FFF',
        padding: '8px',
        height: '500px',
        borderRadius: '10px'

    };
    const [form] = Form.useForm();
    const [form2] = Form.useForm();
    const [fileList, setFileList] = useState([])
    const [optionsConfig, setOptionsConfig] = useState({
        menu_option: [],
        // dtmf: [],
        target_menu: []
    })

    const [menuId, setMenuId] = useState(null)
    const [workingId, setWorkingId] = useState(null)
    const [status, setStatus] = useState(null)
    const [workingStatus, setWorkingStatus] = useState(null)
    const [isloading, setisLoading] = useState(false)
    const [editData, setEditData] = useState([])
    const [editVisible, setEditVisible] = useState(false)
    const dispatch = useDispatch()
    let history = useHistory();

    const onFinish = async (values) => {
        // console.log('Success:', values);
        const formData = new FormData();
        // formData.append('file', values.media.fileList);

        // console.log("check", formData)
        // const payload = {
        //     name: values.name,
        //     prompt_text: values.prompt_text,
        //     media: values.media.fileList[0],
        //     priority: values.priority || false,
        //     off_working: values.off_working || false,

        // }
        // console.log("payload", payload)
        console.log("hhh", values.media)

        if (values.media && values.media.fileList.length > 0) {
            formData.append('media', values.media.fileList[0].originFileObj);
        }
        formData.append('name', values.name);
        formData.append('prompt_text', values.prompt_text || '');
        formData.append('priority', values.priority ? 1 : 0);
        formData.append('off_working', values.off_working ? 1 : 0);


        // apiClient.post('api/menus', formData).then((res) => {
        //     console.log("menu respose ", res);
        //     if (res?.status == 201 || res?.status == 200) {

        //         openNotificationWithIcon('success', "Menu Added Successfully")
        //     }
        //     dispatch(getIvrFlow())
        //     fetchAllOptions()
        //     form.resetFields()
        // }).catch((err) => {
        //     console.log("Error", err?.response?.data?.message)
        //     openNotificationWithIcon('error', err?.response?.data?.message)
        // })

        try {
            const res = await apiClient.post('api/menus', formData)
            if (res?.status == 201 || res?.status == 200) {

                openNotificationWithIcon('success', "Menu Added Successfully")
            }
            dispatch(getIvrFlow())
            fetchAllOptions()
            form.resetFields()
            setFileList([]);

        } catch (error) {
            console.log("Error", error.response);
            // Handling error when the request fails
            const errorMessage = error.response?.data?.error

            openNotificationWithIcon('error', errorMessage);
        }

    };


    // const onFinishTwo = async (values) => {

    //     // apiClient.post('api/menu-options', values).then((res) => {
    //     //     console.log("Menu option save", res.data)
    //     //     if (res?.status == 201 || res?.status == 200) {

    //     //         openNotificationWithIcon('success', "Menu options saved successfully")
    //     //     } else {
    //     //         openNotificationWithIcon('error', res.error)
    //     //     }
    //     //     dispatch(getIvrFlow())
    //     //     form2.resetFields();
    //     // }).catch((err) => {
    //     //     console.log("Error", err)
    //     // })

    //     const response = await apiClient.post('api/menu-options', values);
    //     console.log("Responsee", response.data);
    //     if (response?.status == 201 || response?.status == 200) {

    //         openNotificationWithIcon('success', "Menu options saved successfully")
    //     } else {
    //         openNotificationWithIcon('error', response.error)
    //     }

    // };




    const onFinishTwo = async (values) => {
        try {
            const response = await apiClient.post('api/menu-options', values);


            if (response?.status === 201 || response?.status === 200) {
                openNotificationWithIcon('success', "Menu options saved successfully");
            }
            dispatch(getIvrFlow());
            form2.resetFields();
        } catch (error) {
            console.log("Error", error.response);
            // Handling error when the request fails
            const errorMessage = error.response?.data?.error

            openNotificationWithIcon('error', errorMessage);
        }
    };


    const onFinishFailed = (errorInfo) => {
        console.log('Failed:', errorInfo);
    };
    const onFinishFailedTwo = (errorInfo) => {
        console.log('Failed:', errorInfo);
    };

    const ivrReportState = useSelector(state => state.IvrFlowReducer)

    const fetchAllOptions = () => {
        apiClient.get('api/get-all-menus').then((r) => {

            const options = r.data.map((elm, idx) => ({
                value: elm.id,
                label: elm.name
            }))
            setOptionsConfig({ ...optionsConfig, menu_option: options, target_menu: options })

        })
    }

    useEffect(() => {
        fetchAllOptions()
        dispatch(getIvrFlow())
    }, [])

    console.log("Data", ivrReportState.data)


    const handleChange = (info) => {
        console.log("Error", info)
        let newFileList = [...info.fileList];

        // 1. Limit the number of uploaded files

        newFileList = newFileList.slice(-1);

        // 2. Read from response and show file link
        newFileList = newFileList.map((file) => {
            if (file.response) {
                // Component will show file.url as link
                file.url = file.response.url;
            }
            return file;
        });
        setFileList(newFileList);
        // console.log("after file", fileList)

    };

    const props = {
        fileList,
        onChange: handleChange,
        beforeUpload: () => false
    }



    useEffect(() => {
        if (menuId && status !== null) {
            handleScript(status);
        }
    }, [menuId, status]);

    useEffect(() => {
        if (workingId && workingStatus !== null) {
            handleOffWorking(workingStatus);
        }
    }, [workingId, workingStatus]);

    const handleScript = (status) => {

        setisLoading(true)
        apiClient.put(`api/menus/${menuId}/priority`, { "priority": status }).then((res) => {
            setisLoading(false)
            dispatch(getIvrFlow())
        }).catch((e) => {
            // console.log("Error", e)
            openNotificationWithIcon('error', e?.response?.data?.error)
            setisLoading(false)

        })
        // setisLoading(false)

    }
    const handleOffWorking = (status) => {

        setisLoading(true)
        apiClient.put(`api/menus/${workingId}/off_working`, { "off_working": status }).then((res) => {

            setisLoading(false)
            dispatch(getIvrFlow())
        }).catch((e) => {
            // console.log("Error", e?.response?.data?.error)
            openNotificationWithIcon('error', e?.response?.data?.error)
            setisLoading(false)

        })
        // setisLoading(false)
    }

    const handleActive = (check) => {
        setStatus(check);
    };
    const handleActiveOffWorking = (check) => {
        setWorkingStatus(check);
    };

    const handleEdit = (item) => {
        console.log("edit", item)
        setEditData(item)
        history.push(`/edit-ivrflow/${item.id}`)
    }
    const handleDelete = (item) => {
        console.log("delete", item)
        apiClient.delete(`/api/menus/${item.id}`).then((r) => {
            console.log("delete menu", r.data)
            openNotificationWithIcon('success', r?.data?.message);
            dispatch(getIvrFlow());
            

        }).catch((e) => console.log("Err", e))
    }

    const handleSettings = () => {
        setEditVisible(!editVisible)
    }

    const columns = [
        {
            title: 'Id',
            dataIndex: 'id',
            key: 'id',

        },
        {
            title: 'Name',
            dataIndex: 'name',
            key: 'name',
            width: '200px',
            // render: (text) => <a>{text}</a>,
        },
        {
            title: 'Media',
            dataIndex: 'media',
            key: 'media',
            width: '200px'
        },
        // {
        //     title: 'Address',
        //     dataIndex: 'address',
        //     key: 'address',
        // },
        {
            title: 'Options',
            key: 'options',
            dataIndex: 'options',
            width: '200px',
            render: (_, { options }) => (
                <>
                    {options.map((option) => {
                        // let color = option.length > 5 ? 'geekblue' : 'green';
                        // if (option === 'loser') {
                        //     color = 'volcano';
                        // }
                        return (
                            <Tag color={'geekblue'} key={option}>
                                {"Option " + option.option_number + ': ' + option.option_text}
                            </Tag>
                        );
                    })}
                </>
            ),
        },
        {
            title: 'Greetings',
            key: 'priority',
            render: (text, item, index) => (
                <Space size="middle">

                    <Switch
                        checked={item.priority == 1 ? true : false}
                        onChange={(e) => {
                            // setScriptId(item?.id)
                            setMenuId(item?.id)
                            handleActive(e)
                        }}

                    />
                    {/* <Switch

                        checked={item.priority == 1 ? true : false}
                        onChange={(e) => {
                            // setScriptId(item?.id)
                            setWorkingId(item?.id)
                            handleActive(e)
                        }
                        }

                    /> */}
                </Space>
            ),
        },
        {
            title: 'Off Working Menu',
            key: 'off_working',
            render: (text, item, index) => (
                <Space size="middle">
                    {console.log("ss", item)}

                    <Switch

                        checked={item.off_working == 1 ? true : false}
                        onChange={(e) => {
                            // setScriptId(item?.id)
                            setWorkingId(item?.id)
                            handleActiveOffWorking(e)
                        }
                        }

                    />
                </Space>
            ),
        },
        {
            title: 'Action',
            key: 'action',
            render: (_, record) => (
                <Space size="middle">
                    <Button onClick={() => handleEdit(record)} style={{ fontSize: '14px' }} icon={<EditOutlined color='#000' />}>Edit</Button>
                    <Button onClick={() => handleDelete(record)} style={{ fontSize: '14px' }} icon={<DeleteOutlined color='#FF0000' />}></Button>
                </Space>
            ),
        },
    ];

    return (

        <>
            <Row


                gutter={[16, 16]} align="middle" style={{ backgroundColor: "#f0f0f0", padding: "10px" }}
            >
                <Col className="gutter-row" span={12} >
                    <div style={style}>

                        <Divider orientation="left">Add New Menu</Divider>
                        {/* <span style={{ display: 'flex', justifyContent: 'flex-end', padding: '10px' }}> */}

                        <SettingOutlined
                            style={{
                                fontSize: '20px',
                                display: 'flex', justifyContent: 'flex-end'
                            }}
                            onClick={handleSettings}
                        />
                        {/* </span> */}
                        <Form
                            form={form}
                            name="basic"
                            // label={{
                            //     span
                            // }}
                            layout='vertical'
                            onFinish={onFinish}
                            onFinishFailed={onFinishFailed}
                            autoComplete="off"
                        >
                            <Form.Item
                                label="Menu Name"
                                name="name"
                                style={{ width: '60%' }}
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please input menu name!',
                                    },
                                ]}
                            >
                                <Input />
                            </Form.Item>
                            <Form.Item
                                label="Prompt Text"
                                name="prompt_text"
                                style={{ width: '60%' }}
                            // rules={[
                            //     {
                            //         required: true,
                            //         message: 'Please input prompt text!',
                            //     },
                            // ]}
                            >
                                <Input />
                            </Form.Item>


                            <Form.Item
                                label="Audio File:"
                                name="media"
                            >
                                <Upload
                                    {...props}
                                    progress={true}
                                // fileList={fileList}
                                >
                                    <Button icon={<UploadOutlined />}>Upload file</Button>
                                </Upload>
                            </Form.Item>

                            <Form.Item
                                name="priority"
                                valuePropName="checked"
                            >
                                <Checkbox >Greetings</Checkbox>
                            </Form.Item>
                            <Form.Item
                                name="off_working"
                                valuePropName="checked"
                            >
                                <Checkbox>Off Working Menu</Checkbox>
                            </Form.Item>

                            <Button type="primary" htmlType="submit">
                                Save
                            </Button>

                        </Form>

                    </div>
                </Col>

                <Col className="gutter-row" span={12}>
                    <div style={style}>

                        <Divider orientation="left">Add Menu Option</Divider>

                        <Form
                            form={form2}
                            name="basic"
                            // label={{
                            //     span
                            // }}
                            layout='vertical'
                            onFinish={onFinishTwo}
                            onFinishFailed={onFinishFailedTwo}
                            autoComplete="off"
                        >
                            <Form.Item
                                label="Select menu"
                                name="menu_id"
                                style={{ width: '60%' }}
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please select menu option!',

                                    },
                                ]}
                            >
                                <Select
                                    showSearch
                                    placeholder="Select menu option"
                                    filterOption={(input, option) =>
                                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                    }
                                    options={optionsConfig.menu_option}
                                />

                            </Form.Item>
                            <Form.Item
                                label="Option Number (DTMF Key)"
                                name="option_number"
                                style={{ width: '60%' }}
                            // rules={[
                            //     {
                            //         required: true,
                            //         message: 'Please Select DTMF Key!',

                            //     },
                            // ]}
                            >
                                <InputNumber min={0} max={9} />
                                {/* <Input /> */}
                            </Form.Item>

                            <Form.Item
                                label="Option Text"
                                name="option_text"
                                style={{ width: '60%' }}
                                rules={[
                                    {
                                        required: true,
                                        message: 'This Field cannot be empty!',

                                    },
                                ]}
                            >
                                <Input />
                            </Form.Item>

                            <Form.Item
                                label="Target Menu"
                                name="target_menu_id"
                                style={{ width: '60%' }}
                            // rules={[
                            //     {
                            //         required: true,
                            //         message: 'Please Select Target Menu!',

                            //     },
                            // ]}
                            >
                                <Select
                                    showSearch
                                    placeholder="Select Target menu"
                                    filterOption={(input, option) =>
                                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                    }
                                    options={optionsConfig.target_menu}
                                />
                            </Form.Item>

                            <Form.Item
                                label="Queue"
                                name="queue"
                                style={{ width: '60%' }}
                            // rules={[
                            //     {
                            //         required: true,
                            //         message: 'This Field cannot be empty!',

                            //     },
                            // ]}
                            >
                                <Input />
                            </Form.Item>

                            <Button type="primary" htmlType="submit">
                                Save
                            </Button>

                        </Form>

                    </div>
                </Col>
                <ContactFlowSettings visible={editVisible} setVisible={setEditVisible} />
            </Row>

            <Table loading={ivrReportState.isLoading || isloading} columns={columns} dataSource={ivrReportState.data} />
        </>
    )
}
