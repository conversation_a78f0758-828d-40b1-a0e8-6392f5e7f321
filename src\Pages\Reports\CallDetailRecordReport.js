import {
    Button,
    DatePicker,
    Form,
    Input,
    Modal,
    Pagination,
    Col,
    Row,
    Select,
    Space,
    Spin,
    Table,
    Tooltip
} from "antd";
import {
    CloudDownloadOutlined,
    DownloadOutlined,
    FilterOutlined,
    SearchOutlined,
    ReloadOutlined,
    SaveOutlined,
    SyncOutlined,
} from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import React, { useEffect, useRef, useState } from "react";
import {
    getCallDetailRecordReport,
    getCallDetailRecordReportFiltered,
} from "../../Actions/CallDetailRecordReportActions";
import {
    getAccountCodes,
    getCallStatuses,
} from "../../Actions/CallStatusActions";
import { getAgent } from "../../Actions/AgentActions";
import { openNotificationWithIcon } from "../../Shared/notification";
import "react-h5-audio-player/lib/styles.css";
import ReactAudioPlayer from "react-audio-player";
import apiClient from "../../Shared/apiClient";
import Highlighter from "react-highlight-words";

let route = `${process.env.REACT_APP_baseURL}/api/cdrExport`;

export const CallDetailRecordReport = () => {
    const callDetailReportState = useSelector(
        (state) => state.CallDetailRecordReportReducer
    );
    const [showFilter, setShowFilter] = useState(false);
    const [filterCheck, setFilterCheck] = useState(true);
    const [filterButton, setFilterButton] = useState(false);
    const [rangeArray, setRangeArray] = useState([]);
    const [rangeString, setRangeString] = useState("");
    const [agentArray, setAgentArray] = useState([]);
    const [agentString, setAgentString] = useState("");
    const [data, setData] = useState([]);
    const [exportLoading, setExportLoading] = useState(false);
    const [paginate, setPaginate] = useState({
        current: 1,
        pageSize: 15,
        total: 15,
    });

    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const agentState = useSelector((state) => state.AgentReducer);

    const { current, pageSize } = paginate;

    useEffect(() => {
        if (callDetailReportState.data) {
            setData(callDetailReportState.data.data);
            setPaginate({ ...paginate, total: callDetailReportState.data.total });
        }
    }, [callDetailReportState, data, pageSize]);

    useEffect(() => {
        dispatch(getAgent());
        if (filterCheck) {
            dispatch(getCallDetailRecordReport(paginate));
        } else {
            dispatch(getCallDetailRecordReportFiltered(paginate));
        }
    }, [current, pageSize, filterCheck]);

    useEffect(() => {
        if (callDetailReportState.errMess !== "")
            openNotificationWithIcon("error", callDetailReportState.errMess);
    }, [callDetailReportState.errMess]);

    const onPaginationChange = (page, pageSize) => {
        setPaginate({ ...paginate, pageSize: pageSize, current: page });
    };

    //   useEffect(() => {
    //     form.resetFields();
    //   }, [form, resetFilter]);

    const resetFormFilter = () => {
        setPaginate({ current: 1, pageSize: 20, total: 20 });
        setRangeArray([]);
        setAgentArray([]);
        setFilterCheck(true);
        setFilterButton(false);
        form.resetFields();
        route = `${process.env.REACT_APP_baseURL}/api/cdrExport`;
    };

    const checkUanNum = (num = "") => {
        let result = ""
        const dst = num.startsWith("0") ? num : `0${num}`

        if (dst.startsWith("021")) result = "From Karachi-UAN";
        else if (dst.startsWith("042")) result = "From Lahore-UAN";
        else if (dst.startsWith("051")) result = "From Islamabad-UAN";
        else if (dst.startsWith("061")) result = "From Multan-UAN";
        else if (dst.startsWith("03")) result = "From Mobile";
        else result = "From Other-UAN"

        return result;
    }

    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const searchInput = useRef();



    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }

    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalContent, setModalContent] = useState("");

    const columns = [
        {
            title: "",
            dataIndex: "recordingfile",
            key: "recordingfile",
            render: (text, record) =>
                record.recordingfile !== "" ? (
                    <Button
                        href={
                            process.env.REACT_APP_baseURL +
                            "/api/download/" +
                            record.recordingfile
                        }
                        target="_blank"
                        type="primary"
                        block
                        icon={<DownloadOutlined />}
                    />
                ) : (
                    ""
                ),
        },
        {
            title: "Transcription",
            dataIndex: "transcription",
            key: "transcription",
            ...getColumnSearchProps('transcription'),
            render: (text) => (
                <Tooltip title="Click to view full transcription">
                    <span
                        onClick={() => {
                            setModalContent(text);
                            setIsModalOpen(true);
                        }}
                        style={{ color: "#1890ff", cursor: "pointer" }}
                    >
                        {text?.length > 100 ? text.substring(0, 100) + "..." : text}
                    </span>
                </Tooltip>
            )
        },
        {
            title: "Call-type",
            dataIndex: "accountcode",
            key: "accountcode",
            ...getColumnSearchProps('accountcode')
        },
        {
            title: "Source",
            dataIndex: "src",
            key: "src",
            ...getColumnSearchProps('src')
        },
        {
            title: "Destination",
            dataIndex: "dst",
            key: "dst",
            ...getColumnSearchProps('dst')
        },
        // {
        //     title: "Incoming From",
        //     dataIndex: "uan",
        //     key: "uan",
        //     align: 'center',
        //     ...getColumnSearchProps('uan'),
        //     render: (_, { src }) => checkUanNum(src)
        // },
        {
            title: "Src-channel",
            dataIndex: "channel",
            key: "channel",
            ...getColumnSearchProps('channel'),
        },
        {
            title: "Dst-channel",
            dataIndex: "dstchannel",
            key: "dstchannel",
            ...getColumnSearchProps('dstchannel'),
        },
        {
            title: "Disposition(Workcode)",
            dataIndex: "workcode",
            key: "workcode",
            ...getColumnSearchProps('workcode'),
        },
        {
            title: "Status",
            dataIndex: "disposition",
            key: "disposition",
            ...getColumnSearchProps('disposition'),
        },
        {
            title: "Agent ID",
            dataIndex: "channel",
            key: "agent",
            ...getColumnSearchProps('channel'),

        },
        {
            title: "Agent Name",
            key: "agent_name",
            dataIndex: "name",
            ...getColumnSearchProps('name'),
        },
        {
            title: "Duration",
            dataIndex: "duration",
            key: "duration",
            ...getColumnSearchProps('duration'),
            sorter: {
                compare: (a, b) => a.duration - b.duration,
                multiple: 3,
            },
        },
        {
            title: "Start",
            dataIndex: "start",
            key: "start",

        },
        {
            title: "End",
            dataIndex: "end",
            key: "end",

        },
    ];

    //   console.log(
    //     "paginate route",
    //     `${process.env.REACT_APP_baseURL}/api/cdrExport?source=${paginate.data.source}&accountcode=${paginate.data.accountcode}
    //     &call_status=${paginate.data.call_status}&destination=${paginate.data.destination}&range=${rangeString}`
    //   );

    useEffect(() => {
        if (paginate.data) {
            setAgentString(
                () =>
                    agentArray && agentArray.map((v, i) => `agents[${i}]=${v}`).join("&")
            );
            setRangeString(
                () =>
                    paginate.data.range &&
                    paginate.data.range
                        .map((v, i) => `range[${i}]=${new Date(v).toISOString()}`)
                        .join("&")
            );
            route = `${process.env.REACT_APP_baseURL}/api/cdrExportFilter?`;
            if (rangeString) {
                route = `${route}${rangeString}&`;
            }
            if (paginate.data.source) {
                route = `${route}source=${paginate.data.source}&`;
            }
            if (paginate.data.destination) {
                route = `${route}destination=${paginate.data.destination}&`;
            }
            if (paginate.data.agents) {
                route = `${route}${agentString}&`;
            }
            if (paginate.data.call_status) {
                route = `${route}&call_status=${paginate.data.call_status}&`;
            }
            if (paginate.data.accountcode) {
                route = `${route}accountcode=${paginate.data.accountcode}&`;
            }
        }
    }, [paginate.data, rangeString, agentString, rangeArray]);


    return (
        <>
            <Table
                title={(d) => (
                    <Row>
                        <Col xs={12}>
                            <div style={{ padding: "0.5rem 0 0 1rem" }}>
                                <span>Call Detail Report</span>
                            </div>
                        </Col>
                        <Col xs={12}>
                            <div style={{ float: "right" }}>
                                <Space>
                                    <Button
                                        onClick={() => resetFormFilter()}
                                        type="danger"
                                        icon={<ReloadOutlined />}
                                    >
                                        Reset Filter
                                    </Button>
                                    <Button
                                        onClick={() => setShowFilter(true)}
                                        type="primary"
                                        icon={<FilterOutlined />}
                                    // disabled={filterButton}
                                    >
                                        Filter
                                    </Button>
                                    {/* <CSVLink data={data || []} filename="CallDetailReport.csv">
                            <Button icon={<CloudDownloadOutlined />}>Download</Button>
                        </CSVLink> */}
                                    <Button
                                        icon={
                                            <CloudDownloadOutlined style={{ marginTop: "5px" }} />
                                        }
                                        type="primary"
                                        style={{
                                            justifyContent: "center",
                                            display: "flex",
                                            alignItems: "center",
                                        }}
                                        disabled={callDetailReportState?.data?.data?.length == 0}
                                        href={route}
                                        loading={exportLoading}
                                        // onClick={getExcelData}
                                        target="_blank"
                                    >
                                        Download
                                    </Button>
                                </Space>
                            </div>
                        </Col>
                    </Row>
                )}
                dataSource={data}
                columns={columns}
                scroll={{ x: 1100 }}
                rowKey={(_, index) => index}
                bordered
                pagination={false}
                loading={{
                    spinning: callDetailReportState.isLoading,
                    indicator: <SyncOutlined spin />,
                }}
                expandable={{
                    // expandedRowRender: record => <AudioPlayer
                    //     src={process.env.REACT_APP_baseURL + '/api/file/' + record.recordingfile}
                    //     onPlay={e => console.log("onPlay")}
                    //     // other props here
                    // />,
                    expandedRowRender: (record) => (
                        <ReactAudioPlayer
                            src={
                                process.env.REACT_APP_baseURL +
                                "/api/monitor-file/" +
                                record.recordingfile
                            }
                            autoPlay={false}
                            key={record.recordingfile}
                            controls
                        />
                    ),

                    rowExpandable: (record) => record.recordingfile !== "",
                }}
            />
            {data && (
                <Pagination
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total, range) => {
                        return `${range[0]}-${range[1]} of ${total} items`;
                    }}
                    onChange={onPaginationChange}
                    defaultCurrent={paginate.current}
                    pageSize={paginate.pageSize}
                    total={paginate.total}
                    style={{
                        display: "flex",
                        justifyContent: "end",
                        alignItems: "end",
                        marginTop: "1rem",
                    }}
                />
            )}
            {/*<PlayAudio visible={visible} setVisible={setVisible} />*/}
            <CallDetailRecordReportFilter
                form={form}
                setFilterButton={setFilterButton}
                setFilterCheck={setFilterCheck}
                btnLoading={callDetailReportState.isLoading}
                visible={showFilter}
                setVisible={setShowFilter}
                paginate={paginate}
                setPaginate={setPaginate}
                setAgentArray={setAgentArray}
                setRangeArray={setRangeArray}
            />

            <Modal
                title="Full Transcription"
                open={isModalOpen}
                onCancel={() => setIsModalOpen(false)}
                footer={null}
                width={800}
                bodyStyle={{ maxHeight: '600px', overflowX: 'auto' }}
            >
                <p style={{ whiteSpace: 'pre-wrap' }}>{modalContent}</p>
            </Modal>
        </>
    );
};

export const CallDetailRecordReportFilter = ({
    form,
    visible,
    setVisible,
    btnLoading,
    setFilterButton,
    setFilterCheck,
    setAgentArray,
    setRangeArray,
    paginate,
    setPaginate,
}) => {


    const callStatusState = useSelector((state) => state.CallStatusReducer);
    const agentState = useSelector((state) => state.AgentReducer);
    const [queueSelect, setQueueSelect] = useState(false)
    const dispatch = useDispatch();
    const [queues, setQueues] = useState([])

    useEffect(() => {
        dispatch(getCallStatuses());
        dispatch(getAccountCodes());
        dispatch(getAgent());
        apiClient.get('api/queue').then((res) => {
            setQueues(res.data);
            console.log("ff", res.data)
        })

    }, [visible]);

    // useEffect(() => dispatch(getAgent()), []);

    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            okText="Submit"
            onOk={() =>
                form.validateFields().then((value) => {
                    setPaginate({ ...paginate, data: value });
                    setVisible(false);
                    setFilterCheck(false);
                    setFilterButton(true);
                })}

            okButtonProps={{
                loading: btnLoading,
                icon: <SaveOutlined />,
            }}
            title="Call Detail Report Filter"
        >
            <Spin
                spinning={agentState.isLoading || callStatusState.isLoading}
                indicator={<SyncOutlined spin />}
            >
                <Form form={form} layout="vertical">
                    <Form.Item name="range" label="Date range">
                        <DatePicker.RangePicker
                            showTime={{ format: "HH:mm" }}
                            style={{ width: "100%" }}
                            onChange={(date, range) => {
                                setRangeArray(range);
                            }}
                        />
                    </Form.Item>
                    <Form.Item name="source" label="Source">
                        <Input />
                    </Form.Item>
                    <Form.Item name="destination" label="Destination">
                        <Input />
                    </Form.Item>
                    <Form.Item name="agents" label="Agents">
                        <Select

                            mode="multiple"
                            filterOption={(input, option) =>
                                option.children.toLowerCase().includes(input.toLowerCase())
                            }
                            placeholder="Select agents"
                            onChange={(agents) => {
                                // console.log("paginate agents", agents);
                                setAgentArray(agents);
                            }}
                        >
                            {agentState.users &&
                                agentState.users.map((value, index) => (
                                    <Select.Option key={value.id} value={value.auth_username}>
                                        {value.name}
                                    </Select.Option>
                                ))}
                        </Select>
                    </Form.Item>
                    <Form.Item name="call_status" label="Call Status">
                        <Select>
                            {callStatusState &&
                                callStatusState.statuses.map((value, index) => (
                                    <Select.Option key={index} value={value.disposition}>
                                        {value.disposition}
                                    </Select.Option>
                                ))}
                        </Select>
                    </Form.Item>
                    <Form.Item name="accountcode" label="Call Type">
                        <Select onChange={(val) => {
                            if (val === 'Queue') setQueueSelect(true)
                            else setQueueSelect(false)
                        }}>
                            {callStatusState &&
                                callStatusState.accountCodes.map((value, index) => (
                                    <React.Fragment key={index}>
                                        <Select.Option value={value}>
                                            {value}
                                        </Select.Option>
                                    </React.Fragment>
                                ))}
                        </Select>
                    </Form.Item>
                    {
                        queueSelect &&

                        <Form.Item name={'queue'} label="Queue Value">
                            <Select>
                                {queueSelect && queues.map((queue, index) => (

                                    <Select.Option value={queue.name}>{queue.name}</Select.Option>
                                ))
                                }
                            </Select>
                        </Form.Item>
                    }
                </Form>
            </Spin>
        </Modal>
    );
};
