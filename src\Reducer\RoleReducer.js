import * as ActionTypes from '../Constants/RoleContants'
import { ASSIGN_PERMSSION } from "../Endpoints/RoleRoutes";
const initElement =
    {
        isLoading: false,
        errMess: null,
        role:[],
        message: null,
        rolePermission:[],
        userAssignedRole: [],
        modules: []
    }

export const RoleReducer = (state = initElement, action) =>
{
    switch (action.type)
    {
        default:
            return { ...state }
        case ActionTypes.ROLE_FAILED:
            return { ...state, isLoading: false, errMess: action.payload }
        case ActionTypes.ROLE_SUCCESS:
            return { ...state, isLoading: false, role: action.payload, message: "" }
        case ActionTypes.ROLE_CREATE_SUCCESS:
            return { ...state, isLoading: false, message: action.payload, errMess: null }
        case ActionTypes.ROLE_UPDATE:
            return { ...state, isLoading: false, message: action.payload, errMess: null }
        case ActionTypes.ROLE_DELETE:
            return { ...state, isLoading: false, message: action.payload, errMess: null} 
        case ActionTypes.GET_PERMISSION_BY_ROLE:
            return { ...state, isLoading: false, rolePermission: action.payload, errMess: null }
        case ActionTypes.ASSIGN_ROLE_TO_USER:
            return { ...state, message: action.payload, isLoading: false, errMess: null }
        case ActionTypes.PERMISSION_ASSIGN:
            return { ...state, message: action.payload, isLoading: false, errMess: null }
        case ActionTypes.GET_ROLE_FROM_USERS:
            return { ...state, userAssignedRole: action.payload, isLoading: false, errMess: null }
        case ActionTypes.ROLE_LOADING:
            return { ...state, isLoading: true, message: "" }
    }
}