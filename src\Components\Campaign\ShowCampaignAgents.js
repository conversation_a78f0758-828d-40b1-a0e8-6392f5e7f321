import { useEffect, useState } from "react"
import { Button, Form, Input, Modal, Select, Table } from "antd"
import { DeleteOutlined, PlusSquareOutlined, UsergroupAddOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux"
import { getAgent } from "../../Actions/AgentActions"
import {
    addUserInCampaign,
    addUsersInCampaign,
    deleteUserInCampaign,
    getUsersInCampaign
} from "../../Actions/CampaignUserActions";
import { openNotificationWithIcon } from "../../Shared/notification";

const ShowCampaignAgents = props => {

    const [form] = Form.useForm()
    const Agents = useSelector(state => state.AgentReducer)
    const Users = useSelector(state => state.CampaignUserReducer)
    const dispatch = useDispatch()

    useEffect(() => {
        Users.message && openNotificationWithIcon('success', Users.message)
    }, [Users.message])

    useEffect(() => {
        Users.errMess && openNotificationWithIcon('error', Users.errMess)
    }, [Users.errMess])

    useEffect(() => {
        if (props.item) {
            dispatch(getAgent())
            dispatch(getUsersInCampaign(props.item.id))
        }
    }, [props.item])

    const handleOnFinish = values => {
        dispatch(addUsersInCampaign(props.item.id, values))
    }

    const columns = [
        {
            title: 'Name',
            dataIndex: 'name',
            key: 'name'
        },
        {
            title: 'Interface',
            dataIndex: 'auth_username'
        },
        {
            title: 'Actions',
            render: (text, record) => <Button onClick={() => dispatch(deleteUserInCampaign(props.item.id, record.id))} icon={<DeleteOutlined />} />
        }
    ]

    return (
        <Modal
            destroyOnClose={true}
            visible={props.showAddUser}
            title={<><UsergroupAddOutlined /> Add Agents</>}
            okText="OK"
            cancelText="Cancel"
            onOk={props.onAddUserOk}
            onCancel={props.onAddUserCancel}
        >
            <Form onFinish={handleOnFinish}>
                <Form.Item name="users">
                    <Select mode="multiple"
                        filterOption={(input, option) =>
                            option.children.toLowerCase().includes(input.toLowerCase())
                        }
                    >
                        {Agents.users && Agents.users.map(((value, index) => <Select.Option key={value.name} value={value.id}>{value.name}</Select.Option>))}
                    </Select>
                </Form.Item>
                <Form.Item>
                    <Button icon={<PlusSquareOutlined />} type="primary" htmlType="submit">
                        Add
                    </Button>
                </Form.Item>
            </Form>
            <Table dataSource={Users.campaignUsers} loading={Users.isLoading} columns={columns} scroll={{ x: 1100 }} />
        </Modal>
    )
}

export default ShowCampaignAgents