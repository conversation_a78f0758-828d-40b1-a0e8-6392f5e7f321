import React, { useEffect, useState } from 'react'
import { EditOutlined, MinusCircleOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, Checkbox, Col, Divider, Form, Input, InputNumber, Row, Select, Space, Switch, Table, Tag, Upload } from 'antd'
import apiClient from '../Shared/apiClient';
import { useParams } from "react-router";
import { openNotificationWithIcon } from '../Shared/notification';

export default function EditIvrFlow(props) {


    const style = {
        background: '#FFF',
        padding: '8px',
    };
    const [fileList, setFileList] = useState([])
    const [form] = Form.useForm();
    const { id } = useParams()
    const [optionsConfig, setOptionsConfig] = useState({
        menu_option: [],
        // dtmf: [],
        target_menu: []
    })
    const [options, setOptions] = useState()

    // const [optionsConfigs, setOptionsConfigs] = useState({
    //     menu_option: [],
    //     // dtmf: [],
    //     target_menu: []
    // })



    const fetchMenuById = () => {
        apiClient.get(`api/menus/${id}`).then((res) => {
            const { media, ...formData } = res.data;

            setOptions(res.data.options)
            // Populate form fields
            form.setFieldsValue(formData);

            // Initialize fileList
            if (media) {
                const initialFile = [
                    {
                        uid: '-1',
                        name: media.split('/').pop(), // Extract file name from path
                        status: 'done',
                        originFileObj: null, // No actual file object for existing media
                    },
                ];
                setFileList(initialFile);
            }
        });
    };

    console.log(" fetch options", options)
    const testObj = {
        "name": "Test menu 1",
        "prompt_text": "tsad",
        "options": {
            "1": {
                "menu_id": 2,
                "option_number": 2,
                "option_text": "change text",
                "target_menu_id": 3
            },
            "new": []
        }
    }

    const handleChange = (info) => {
        let newFileList = [...info.fileList];


        newFileList = newFileList.slice(-1);

        // Map files to add URL if available
        newFileList = newFileList.map((file) => {
            if (file.response) {
                file.url = file.response.url; // Add file URL if the file is uploaded
            }
            return file;
        });

        // Update fileList state
        setFileList(newFileList);
        // console.log("fileList", fileList);
        // console.log("newFileList", newFileList)
        const formData = new FormData();

        formData.append('media_file', newFileList[0]?.originFileObj)

        handleUpload(formData)
        // console.log("check", newFileList[0])
        // Update form field value for "media_file"
        if (newFileList.length > 0) {
            form.setFieldsValue({ media_file: newFileList[0] });
            handleUpload(formData)
        } else {
            form.setFieldsValue({ media_file: null });
            const fileRemovePayload = new FormData();
            fileRemovePayload.append('remove_media', 1)
            handleUpload(fileRemovePayload)
        }

    };
    const handleUpload = (formData) => {
        apiClient.post(`api/menu-media-upload/${id}`, formData).then((r) => {
            console.log("upload response", r.data)
        }).catch((e) => console.log("Error", e))

    }




    const [disabledFields, setDisabledFields] = useState({});

    // const handleValuesChange = (changedValues, allValues) => {
    //     const updatedDisabledFields = { ...disabledFields };

    //     allValues.options.forEach((option, idx) => {
    //         if (option?.target_menu_id) {
    //             updatedDisabledFields[idx] = { queue: true };
    //         } else if (option?.queue) {
    //             updatedDisabledFields[idx] = { target_menu_id: true };
    //         } else {
    //             updatedDisabledFields[idx] = {};
    //         }
    //     });

    //     setDisabledFields(updatedDisabledFields);
    // };
    // const handleValidation = (_, value, callback) => {
    //     const options = form.getFieldValue("options") || [];
    //     const isValid = options.some(
    //         (opt) => opt?.target_menu_id || opt?.queue
    //     );
    //     if (!isValid) {
    //         return Promise.reject("At least one field (Target Menu or Queue) must be filled!");
    //     }
    //     return Promise.resolve();
    // };

    const onFinish = async (values) => {
        console.log("Form Data", values);

        const transformedOptions = {};
        values.options && values.options.forEach((opt, idx) => {
            if (opt) {
                transformedOptions[options[idx].id] = {
                    menu_id: opt.menu_id,
                    option_number: opt.option_number,
                    option_text: opt.option_text,
                    target_menu_id: opt.target_menu_id,
                    queue: opt.queue
                };
            }
        });

        let payload;
        payload = {
            name: values.name,
            prompt_text: values.prompt_text,
            options: {
                ...transformedOptions,
                new: values.new || [],
            },
        };



        // console.log("Payload:", payload);


        try {
            const res = await apiClient.post(`api/menu-options/${id}`, payload);
            if (res?.status === 201 || res?.status === 200) {
                openNotificationWithIcon('success', "Menu Updated Successfully");
            }
            fetchMenuById();
            form.resetFields();

        } catch (error) {
            // console.log("Error", error?.response?.data?.error)
            openNotificationWithIcon('error', error?.response?.data?.error)
            fetchMenuById();
        }

    };


    useEffect(() => {

        fetchMenuById();

    }, [id, form])


    const onFinishFailed = (errorInfo) => {
        console.log('Failed:', errorInfo);
    };
    const propss = {
        fileList,
        onChange: handleChange,
        beforeUpload: () => false
    }


    const fetchAllOptions = () => {
        apiClient.get('api/get-all-menus').then((r) => {
            console.log("get all menu", r.data)
            const options = r.data.map((elm, idx) => ({
                value: elm.id,
                label: elm.name
            }))
            setOptionsConfig({ ...optionsConfig, menu_option: options, target_menu: options })
        })

    }
    useEffect(() => {
        fetchAllOptions();
    }, [])



    console.log("File list", fileList)

    const formItemLayout = {
        labelCol: {
            xs: {
                span: 24,
            },
            sm: {
                span: 4,
            },
        },
        wrapperCol: {
            xs: {
                span: 24,
            },
            sm: {
                span: 20,
            },
        },
    };
    const formItemLayoutWithOutLabel = {
        wrapperCol: {
            xs: {
                span: 24,
                offset: 0,
            },
            sm: {
                span: 20,
                offset: 4,
            },
        },
    };
    console.log("options config", optionsConfig.menu_option)

    const handleRemoveOption = (index) => {

        const optionId = options[index].id;
        apiClient.delete(`api/menu-options/${optionId}`)
            .then((r) => {
                console.log("delete response", r.data)
                fetchAllOptions();
            })
            .catch((e) => console.log("Err", e))


        const newOptions = options.filter((_, idx) => idx !== index);
        setOptions(newOptions);
        form.setFieldsValue({ options: newOptions });
    };


    return (

        <Row

            gutter={[16, 16]} align="middle" style={{ backgroundColor: "#f0f0f0", padding: "10px" }}
        >
            <Col className="gutter-row" span={24} >
                <div style={style}>

                    <Divider orientation="left">Edit Menu</Divider>

                    <Form
                        form={form}
                        name="basic"
                        // onValuesChange={handleValuesChange}
                        // label={{
                        //     span
                        // }}
                        layout='vertical'

                        onFinish={onFinish}
                        onFinishFailed={onFinishFailed}
                        autoComplete="off"
                        initialValues={{
                            options: options, // Populate form with options array
                        }}
                    >
                        <Form.Item
                            label="Menu Name"
                            name="name"
                            style={{ width: '60%' }}
                            rules={[
                                {
                                    required: true,
                                    message: 'Please input menu name!',
                                },
                            ]} >
                            <Input />
                        </Form.Item>
                        <Form.Item
                            label="Prompt Text"
                            name="prompt_text"
                            style={{ width: '60%' }}
                        // rules={[
                        //     {
                        //         required: true,
                        //         message: 'Please input prompt text!',
                        //     },
                        // ]}
                        >
                            <Input />
                        </Form.Item>

                        <Form.Item
                            label="Audio File:"
                            name="media_file"
                        >
                            <Upload

                                {...propss}
                                progress={true}
                            // fileList={fileList}
                            >
                                <Button icon={<UploadOutlined />}>Upload file</Button>
                            </Upload>
                        </Form.Item>

                        {options && options.map((elm, idx) => (

                            <div
                                style={{ display: 'flex', gap: '5px', }}
                                key={idx}
                            >
                                <Form.Item
                                    label="Select menu"
                                    // name="menu_id"
                                    name={['options', idx, 'menu_id']}

                                    style={{ width: '60%' }}
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select menu option!',

                                        },
                                    ]}
                                >
                                    <Select
                                        showSearch
                                        placeholder="Select menu option"
                                        filterOption={(input, option) =>
                                            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                        }
                                        // defaultValue={elm.menu_id}
                                        // value={optionsConfigs.menu_option[idx]}
                                        options={optionsConfig.menu_option}
                                    />

                                </Form.Item>
                                <Form.Item
                                    label="Option Number (DTMF Key)"
                                    // name="option_number"
                                    name={['options', idx, 'option_number']}
                                    style={{ width: '60%' }}
                                // rules={[
                                //     {
                                //         required: true,
                                //         message: 'Please Select DTMF Key!',

                                //     },
                                // ]}
                                >
                                    <InputNumber
                                        style={{
                                            width: '100%',
                                        }}
                                        min={0}
                                        max={9}
                                    // defaultValue={elm.option_number}
                                    />

                                </Form.Item>

                                <Form.Item
                                    label="Option Text"
                                    // name="option_text"
                                    name={['options', idx, 'option_text']}
                                    style={{ width: '60%' }}
                                    rules={[
                                        {
                                            required: true,
                                            message: 'This Field cannot be empty!',

                                        },
                                    ]}
                                >
                                    <Input
                                    // defaultValue={elm.option_text} 
                                    />
                                </Form.Item>

                                <Form.Item
                                    label="Target Menu"
                                    // name="target_menu_id"
                                    disabled={disabledFields[idx]?.target_menu_id}
                                    name={['options', idx, 'target_menu_id']}
                                    style={{ width: '60%' }}
                                // rules={[
                                //     {
                                //         required: true,
                                //         message: 'Please Select Target Menu!',

                                //     },
                                // ]}
                                >
                                    <Select
                                        allowClear
                                        showSearch
                                        placeholder="Select Target menu"
                                        filterOption={(input, option) =>
                                            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                        }
                                        // defaultValue={elm.target_menu_id}
                                        options={optionsConfig.target_menu}
                                    />
                                </Form.Item>
                                <Form.Item
                                    label="Queue"
                                    name={['options', idx, 'queue']}
                                    // name={[field.name, 'queue']}
                                    disabled={disabledFields[idx]?.queue}
                                    style={{ width: '60%' }}
                                // rules={[
                                //     {
                                //         required: true,
                                //         message: 'This Field cannot be empty!',

                                //     },
                                // ]}
                                >
                                    <Input />
                                </Form.Item>

                                <MinusCircleOutlined
                                    style={{ fontSize: '18px', cursor: 'pointer' }}
                                    onClick={() => handleRemoveOption(idx)}
                                />
                            </div>
                        ))}

                        {/* options form list */}

                        {/*                  
                        <Form.List name="options">
                            {(fields, { add, remove }) => (
                                <>
                                    {fields.map(({ key, name, fieldKey }) => (
                                        <div key={key} style={{ display: "flex", gap: "5px" }}>
                                            <Form.Item
                                                label="Select Menu"
                                                name={[name, "menu_id"]}
                                                fieldKey={[fieldKey, "menu_id"]}
                                                rules={[{ required: true, message: "Please select a menu option!" }]}
                                                style={{ width: "20%" }}
                                            >
                                                <Select
                                                    showSearch
                                                    placeholder="Select menu option"
                                                    options={optionsConfig.menu_option}
                                                />
                                            </Form.Item>

                                            <Form.Item
                                                label="Option Number (DTMF Key)"
                                                name={[name, "option_number"]}
                                                fieldKey={[fieldKey, "option_number"]}
                                                rules={[{ required: true, message: "Please select a DTMF key!" }]}
                                                style={{ width: "20%" }}
                                            >
                                                <InputNumber style={{ width: "100%" }} min={1} max={9} />
                                            </Form.Item>

                                            <Form.Item
                                                label="Option Text"
                                                name={[name, "option_text"]}
                                                fieldKey={[fieldKey, "option_text"]}
                                                rules={[{ required: true, message: "This field cannot be empty!" }]}
                                                style={{ width: "20%" }}
                                            >
                                                <Input />
                                            </Form.Item>

                                            <Form.Item
                                                label="Target Menu"
                                                name={[name, "target_menu_id"]}
                                                fieldKey={[fieldKey, "target_menu_id"]}
                                                style={{ width: "20%" }}
                                                rules={[{ validator: handleValidation }]}
                                            >
                                                <Select
                                                    allowClear
                                                    showSearch
                                                    placeholder="Select target menu"
                                                    options={optionsConfig.target_menu}
                                                />
                                            </Form.Item>

                                            <Form.Item
                                                label="Queue"
                                                name={[name, "queue"]}
                                                fieldKey={[fieldKey, "queue"]}
                                                style={{ width: "20%" }}
                                                rules={[{ validator: handleValidation }]}
                                            >
                                                <Input allowClear />
                                            </Form.Item>

                                            <MinusCircleOutlined
                                                onClick={() => remove(name)}
                                                style={{ fontSize: "18px", cursor: "pointer" }}
                                            />
                                        </div>
                                    ))}
                                    <Button
                                        type="dashed"
                                        onClick={() => add()}
                                        style={{ width: "100%", marginTop: "10px" }}
                                    >
                                        <PlusOutlined /> Add Option
                                    </Button>
                                </>
                            )}
                        </Form.List>
 */}

                        {/* options form list */}


                        {/* menu options code */}
                        <Form.List name="new">
                            {(fields, { add, remove }, { errors }) => (
                                <>
                                    {fields.map((field, index) => (
                                        <Form.Item
                                            {...formItemLayout}
                                            required={false}
                                            key={field.key}
                                        >
                                            <div style={{ display: 'flex', gap: '10px' }}>
                                                {/* First Select Field */}
                                                <Form.Item
                                                    label="Select Menu"
                                                    validateTrigger={['onChange', 'onBlur']}
                                                    name={[field.name, 'menu_id']}
                                                    fieldKey={[field.fieldKey, 'menu_id']}
                                                >
                                                    <Select
                                                        showSearch
                                                        placeholder="Select menu option"
                                                        filterOption={(input, option) =>
                                                            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                                        }
                                                        options={optionsConfig.menu_option}
                                                    />
                                                </Form.Item>

                                                {/* Option Number */}
                                                <Form.Item
                                                    label="Option Number"
                                                    name={[field.name, 'option_number']}
                                                    style={{ width: '60%' }}
                                                    fieldKey={[field.fieldKey, 'option_number']}
                                                // rules={[
                                                //     {
                                                //         required: true,
                                                //         message: 'Please Select DTMF Key!',
                                                //     },
                                                // ]}
                                                >
                                                    <InputNumber
                                                        style={{ width: '100%' }}
                                                        placeholder="DTMF Key"
                                                        min={0}
                                                        max={9}
                                                    />
                                                </Form.Item>

                                                {/* Option Text */}
                                                <Form.Item
                                                    label="Option Text"
                                                    name={[field.name, 'option_text']}
                                                    fieldKey={[field.fieldKey, 'option_text']}
                                                    style={{ width: '60%' }}
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: 'This Field cannot be empty!',
                                                        },
                                                    ]}
                                                >
                                                    <Input />
                                                </Form.Item>

                                                {/* Target Menu */}
                                                <Form.Item
                                                    label="Target Menu"
                                                    validateTrigger={['onChange', 'onBlur']}
                                                    name={[field.name, 'target_menu_id']}
                                                    fieldKey={[field.fieldKey, 'target_menu_id']}
                                                >
                                                    {/* <Select
                                                        showSearch
                                                        allowClear
                                                        placeholder="Select target option"
                                                        disabled={!!form.getFieldValue(['new', index, 'queue'])}
                                                        filterOption={(input, option) =>
                                                            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                                        }
                                                        options={optionsConfig.target_menu}
                                                        onChange={(value) => {
                                                            if (value) {
                                                                form.setFieldsValue({
                                                                    new: form.getFieldValue('new').map((item, idx) =>
                                                                        idx === index ? { ...item, queue: undefined } : item
                                                                    ),
                                                                });
                                                            }
                                                        }}
                                                    /> */}
                                                    <Select
                                                        showSearch
                                                        allowClear
                                                        placeholder="Select target option"
                                                        disabled={!!form.getFieldValue(['new', index, 'queue'])}
                                                        filterOption={(input, option) =>
                                                            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                                        }
                                                        options={optionsConfig.target_menu}
                                                        onChange={(value) => {
                                                            // Update value and enable queue field if cleared
                                                            form.setFieldsValue({
                                                                new: form.getFieldValue('new').map((item, idx) =>
                                                                    idx === index ? { ...item, queue: value ? undefined : item.queue } : item
                                                                ),
                                                            });
                                                        }}
                                                    />

                                                </Form.Item>

                                                {/* Queue */}
                                                <Form.Item
                                                    label="Queue"
                                                    name={[field.name, 'queue']}
                                                    fieldKey={[field.fieldKey, 'queue']}
                                                    style={{ width: '60%' }}
                                                >
                                                    {/* <Input
                                                        placeholder="Queue"
                                                        disabled={!!form.getFieldValue(['new', index, 'target_menu_id'])}
                                                        onChange={(e) => {
                                                            if (e.target.value) {
                                                                form.setFieldsValue({
                                                                    new: form.getFieldValue('new').map((item, idx) =>
                                                                        idx === index ? { ...item, target_menu_id: undefined } : item
                                                                    ),
                                                                });
                                                            }
                                                        }}
                                                    /> */}
                                                    <Input
                                                        placeholder="Queue"
                                                        disabled={!!form.getFieldValue(['new', index, 'target_menu_id'])}
                                                        onChange={(e) => {
                                                            const value = e.target.value;
                                                            form.setFieldsValue({
                                                                new: form.getFieldValue('new').map((item, idx) =>
                                                                    idx === index ? { ...item, target_menu_id: value ? undefined : item.target_menu_id } : item
                                                                ),
                                                            });
                                                        }}
                                                    />

                                                </Form.Item>
                                            </div>
                                            {fields.length >= 1 ? (
                                                <MinusCircleOutlined
                                                    className="dynamic-delete-button"
                                                    onClick={() => remove(field.name)}
                                                />
                                            ) : null}
                                        </Form.Item>
                                    ))}
                                    <Form.Item>
                                        <Button
                                            type="dashed"
                                            onClick={() => add()}
                                            style={{ width: '60%' }}
                                            icon={<PlusOutlined />}
                                        >
                                            Add field
                                        </Button>
                                        <Form.ErrorList errors={errors} />
                                    </Form.Item>
                                </>
                            )}
                        </Form.List>

                        {/* menu options */}

                        <Button type="primary" htmlType="submit">
                            Update
                        </Button>

                    </Form>

                </div>
            </Col>
        </Row>
    )
}
