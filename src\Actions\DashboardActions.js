import apiClient from "../Shared/apiClient";
import { Stats } from "../Endpoints/DashboardRoutes";
import * as ActionTypes from '../Constants/Stats'
import { logoutUser } from "./UserActions";
import { handleError } from "../Shared/handleError";

export const GetStats = () => dispatch => {
    dispatch(statsLoading())
    apiClient.post(Stats)
        .then(response => dispatch(statsSuccess(response.data)))
        .catch(e => dispatch(statsFailed(handleError(e))))
}

export const statsLoading = () => ({
    type: ActionTypes.STATS_LOADING
})

export const statsSuccess = stats => ({
    type: ActionTypes.STATS_SUCCESS,
    payload: stats
})

export const statsFailed = error => ({
    type: ActionTypes.STATS_FAILED,
    payload: error
})
