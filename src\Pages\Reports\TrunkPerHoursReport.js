import { useDispatch, useSelector } from "react-redux";
import { useEffect, useRef, useState } from "react";
import { getAgent } from "../../Actions/AgentActions";

import { openNotificationWithIcon } from "../../Shared/notification";
import { Button, Col, DatePicker, Form, Input, Modal, Row, Select, Space, Spin, Table, Typography } from "antd";
import {
    CloudDownloadOutlined,
    DownloadOutlined,
    FilterOutlined,
    ReloadOutlined,
    SaveOutlined,
    SearchOutlined,
    SyncOutlined
} from "@ant-design/icons";
import { CSVLink } from "react-csv";

import { getTrunkPerHourReport, trunkPerHourReset } from "../../Actions/TrunkPerHoursActions";
import apiClient from "../../Shared/apiClient";
import Highlighter from "react-highlight-words";

export const TrunkPerHourReport = () => {

    const trunkPerHourReportReducer = useSelector(state => state.TrunkPerHourReportReducer)
    const [showFilter, setShowFilter] = useState(false)
    const [resetFilter, setResetFilter] = useState(false)
    const [date, setDate] = useState('Today')
    const dispatch = useDispatch()
    const [form] = Form.useForm()
    // useEffect(() => dispatch(getTrunkPerHourReport()), [])
    // useEffect(() => console.log(date), [date])

    useEffect(() => {
        if (trunkPerHourReportReducer.errMess !== '') openNotificationWithIcon('error', trunkPerHourReportReducer.errMess)
    }, [trunkPerHourReportReducer.errMess])

    const resetFormFilter = () => {
        // dispatch(getTrunkPerHourReport())
        dispatch(trunkPerHourReset())
        // console.log(new Date().toLocaleString() + '')
        form.resetFields()
        setDate('Today')
        setResetFilter(true)
    }

    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const searchInput = useRef()

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }


    const columns = [
        {
            title: 'Time Period',
            dataIndex: 'time_period',
            key: 'time_period',
        },
        {
            title: 'Entered',
            dataIndex: 'entered',
            key: 'entered',
            ...getColumnSearchProps('entered')
        },
        {
            title: 'Answered',
            dataIndex: 'answered',
            key: 'answered',
            ...getColumnSearchProps('answered')
        },
        {
            title: 'Abandoned',
            dataIndex: 'abandoned',
            key: 'abandoned',
            ...getColumnSearchProps('abandoned')

        }
    ]

    return (
        <>
            {/* <div style={{ marginBottom: '10px' }}>
                <Space>
                    <Button onClick={() => resetFormFilter()} type="danger" icon={<ReloadOutlined />}>Reset Filter</Button>
                    <Button onClick={() => setShowFilter(true)} type="primary" icon={<FilterOutlined />}>Filter</Button>
                    <CSVLink data={trunkPerHourReportReducer.data} filename="trunkPerHour.csv">
                        <Button icon={<CloudDownloadOutlined />}>Download</Button>
                    </CSVLink>
                </Space>
            </div> */}
            <Table
                title={data =>
                    <>
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            {`Trunk Per Hour (Date: ${date})`}
                            <Space>
                                <Button onClick={() => resetFormFilter()} type="danger" icon={<ReloadOutlined />}>Reset Filter</Button>
                                <Button onClick={() => setShowFilter(true)} icon={<FilterOutlined />}>Filter</Button>
                                <CSVLink data={trunkPerHourReportReducer.data} filename="trunkPerHour.csv">
                                    <Button icon={<CloudDownloadOutlined />} type={"primary"}
                                        target="_blank" disabled={trunkPerHourReportReducer.data.length == 0}>
                                        Download</Button>
                                </CSVLink>
                            </Space>

                        </div>
                    </>

                }
                dataSource={trunkPerHourReportReducer.data}
                columns={columns}
                bordered
                loading={{ spinning: trunkPerHourReportReducer.isLoading, indicator: <SyncOutlined spin /> }}
            />
            <TrunkPerHourReportFilter date={date} form={form} setDate={setDate} resetFilter={resetFilter} btnLoading={trunkPerHourReportReducer.isLoading} visible={showFilter} setVisible={setShowFilter} />
        </>
    )
}

export const TrunkPerHourReportFilter = ({ form, visible, setVisible, btnLoading, resetFilter, setDate }) => {


    const dispatch = useDispatch()
    const [filterDate, setFilterDate] = useState()
    useEffect(() => form.resetFields(), [resetFilter])
    const [queues, setQueues] = useState([])
    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data))
    }, [])
    const handleChange = (date, dateString) => {
        setDate(dateString)
        setFilterDate(dateString)
    }
    const [queue, setQueueOption] = useState("");

    var handleQueue = (queue) => {
        console.log(queue, 'queueoption');
        setQueueOption(queue);
    };
    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            okText="Submit"
            onOk={() => form.validateFields()
                .then(value => {
                    dispatch(getTrunkPerHourReport({ date: filterDate, queue: queue }))
                    setVisible(false)
                })
            }
            okButtonProps={{
                loading: btnLoading,
                icon: <SaveOutlined />
            }}
            title="Trunk Per Hour Filter"
        >
            <Spin spinning={btnLoading} indicator={<SyncOutlined spin />}>
                <Form
                    form={form}
                    layout="vertical"
                >
                    <Form.Item name="date" label="Date">
                        <DatePicker style={{ width: '100%' }} onChange={handleChange} />
                    </Form.Item>
                    <Form.Item name="queue" label="Queue" >
                        <Select onChange={handleQueue} >
                            {queues &&
                                queues.map((value, index) => (
                                    <Select.Option key={index} value={value.name}>
                                        {value.name}
                                    </Select.Option>
                                ))}
                        </Select>
                    </Form.Item>
                </Form>
            </Spin>
        </Modal>
    )
}