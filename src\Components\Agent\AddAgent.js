import { Form, Input, Modal, Select, Spin, Switch, message } from "antd";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchAllQueues, getQueues } from "../../Actions/QueueActions";

const AddUser = ({ form, isLoading, visible, setVisible, onCreate }) => {
    const [showAgentFields, setShowAgentFields] = useState(false);
    const [showQueue, setShowQueue] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false); // Prevent multiple submissions

    const QueueState = useSelector((state) => state.QueueReducer);
    const dispatch = useDispatch();

    const onSelectChange = (option) => {
        setShowAgentFields(true);
        setShowQueue(false);
        if (option !== "Outbound") {
            dispatch(fetchAllQueues());
            setShowQueue(true);
        }
    };

    const handleFormSubmit = async () => {
        try {
            setIsSubmitting(true);
            const values = await form.validateFields();

            // Convert boolean to 1 or 0 for backend
            const userData = {
                ...values,
                auto_call_answer: values?.auto_call_answer ? 1 : 0,
                hangup_enable: values?.hangup_enable ? 1 : 0
            };

            const response = await onCreate(userData);

            if (response?.error) {
                throw new Error(response.error);
            }

            setVisible(false);
            form.resetFields();
        } catch (error) {
            console.error("Error:", error);
            message.error(error.message || "An error occurred while adding the user.");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Modal
            visible={visible}
            title={"Add Agents"}
            okText="Submit"
            cancelText="Cancel"
            closable={true}
            confirmLoading={isSubmitting}
            onCancel={() => {
                setVisible(false);
                form.resetFields();
            }}
            onOk={handleFormSubmit}
        >
            <Spin spinning={QueueState.isLoading || isLoading}>
                <Form form={form} layout="vertical" name="form_in_modal">
                    <Form.Item name="bitrix_id" label="Bitrix Id">
                        <Input />
                    </Form.Item>

                    <Form.Item
                        name="name"
                        label="Name"
                        rules={[{ required: true, message: "Please input the name" }]}
                    >
                        <Input />
                    </Form.Item>

                    <Form.Item
                        name="username"
                        label="Username"
                        rules={[{ required: true, message: "Please input the username" }]}
                    >
                        <Input />
                    </Form.Item>

                    <Form.Item
                        name="email"
                        label="E-Mail"
                        rules={[{ required: true, message: "Please input the email address" }]}
                    >
                        <Input />
                    </Form.Item>

                    <Form.Item
                        name="password"
                        label="Password"
                        rules={[{ required: true, message: "Please input the password" }]}
                    >
                        <Input.Password />
                    </Form.Item>

                    <Form.Item
                        name="password_confirmation"
                        label="Confirm Password"
                        rules={[{ required: true, message: "Please input confirm password" }]}
                    >
                        <Input.Password />
                    </Form.Item>

                    <Form.Item
                        name="type"
                        label="Type"
                        rules={[{ required: true, message: "Please select the type" }]}
                    >
                        <Select onSelect={onSelectChange}>
                            <Select.Option value="Blended">Blended</Select.Option>
                            <Select.Option value="Inbound">Inbound</Select.Option>
                            <Select.Option value="Outbound">Outbound</Select.Option>
                        </Select>
                    </Form.Item>

                    {showAgentFields && (
                        <>
                            <Form.Item
                                name="auth_username"
                                label="Auth Username"
                                rules={[{ required: true, message: "Please input the auth username" }]}
                            >
                                <Input />
                            </Form.Item>
                            <Form.Item
                                name="auth_password"
                                label="Auth Password"
                                rules={[{ required: true, message: "Please input the auth password" }]}
                            >
                                <Input />
                            </Form.Item>

                            {showQueue && (
                                <Form.Item
                                    name="queue"
                                    label="Queue"
                                    rules={[{ required: true, message: "Please select the queue" }]}
                                >
                                    <Select>
                                        {QueueState.allQueues.map((value) => (
                                            <Select.Option value={value.name} key={value.name}>
                                                {value.name}
                                            </Select.Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                            )}
                        </>
                    )}
                </Form>
            </Spin>
        </Modal>
    );
};

export default AddUser;


// import { Form, Input, Modal, Select, Spin, Switch } from "antd";
// import { useState } from "react";
// import { useDispatch, useSelector } from "react-redux";
// import { getQueues } from "../../Actions/QueueActions";

// const AddUser = ({ form, isLoading, visible, setVisible, onCreate }) => {
//     const [showAgentFields, setShowAgentFields] = useState(false);
//     const [showQueue, setShowQueue] = useState(false);

//     const QueueState = useSelector((state) => state.QueueReducer);
//     const dispatch = useDispatch();

//     const onSelectChange = (option) => {
//         setShowAgentFields(true);
//         setShowQueue(false);
//         // Dispatch queue fetch
//         if (option !== "Outbound") {
//             dispatch(getQueues());
//             setShowQueue(true);
//         }
//     };

//     const onAutoAnswerChange = (option) => {
//         console.log("systemSetting answer", option);
//     };

//     return (
//         <Modal
//             visible={visible}
//             title={"Add Agents"}
//             okText="Submit"
//             cancelText="Cancel"
//             closable={true}
//             onCancel={() => {
//                 //form.resetFields()
//                 setVisible(false);
//             }}
//             onOk={() => {
//                 form
//                     .validateFields()
//                     .then((values) => {
//                         //form.resetFields()
//                         onCreate({
//                             ...values,
//                             auto_call_answer: values?.auto_call_answer == true ? 1 : 0,
//                             hangup_enable: values?.hangup_enable == true ? 1 : 0
//                         });
//                     })
//                     .catch((info) => {
//                         console.log("Validate Failed:", info);
//                     });
//             }}
//         >
//             <Spin spinning={QueueState.isLoading || isLoading}>
//                 <Form form={form} layout="vertical" name="form_in_modal">
//                     <Form.Item
//                         name="bitrix_id"
//                         label="Bitrix Id"
//                         rules={[
//                             {
//                                 required: false,
//                                 message: 'Please input the bitrix id',
//                             },
//                         ]}
//                     >
//                         <Input />
//                     </Form.Item>

//                     <Form.Item
//                         name="name"
//                         label="Name"
//                         rules={[
//                             {
//                                 required: true,
//                                 message: "Please input the name",
//                             },
//                         ]}
//                     >
//                         <Input />
//                     </Form.Item>
//                     <Form.Item
//                         name="username"
//                         label="Username"
//                         rules={[
//                             {
//                                 required: true,
//                                 message: "Please input the username",
//                             },
//                         ]}
//                     >
//                         <Input />
//                     </Form.Item>
//                     <Form.Item
//                         name="email"
//                         label="E-Mail"
//                         rules={[
//                             {
//                                 required: true,
//                                 message: "Please input the email address",
//                             },
//                         ]}
//                     >
//                         <Input />
//                     </Form.Item>
//                     <Form.Item
//                         name="password"
//                         label="Password"
//                         rules={[
//                             {
//                                 required: true,
//                                 message: "Please input the password",
//                             },
//                         ]}
//                     >
//                         <Input.Password />
//                     </Form.Item>
//                     <Form.Item
//                         name="password_confirmation"
//                         label="Confirm Password"
//                         rules={[
//                             {
//                                 required: true,
//                                 message: "Please input confirm password",
//                             },
//                         ]}
//                     >
//                         <Input.Password />
//                     </Form.Item>
//                     <Form.Item
//                         name="type"
//                         label="Type"
//                         rules={[
//                             {
//                                 required: true,
//                                 message: "Please select the type",
//                             },
//                         ]}
//                     >
//                         <Select onSelect={onSelectChange}>
//                             <Select.Option value="Blended" key="Blended">
//                                 Blended
//                             </Select.Option>
//                             <Select.Option value="Inbound" key="Inbound">
//                                 Inbound
//                             </Select.Option>
//                             <Select.Option value="Outbound" key="Outbound">
//                                 Outbound
//                             </Select.Option>
//                         </Select>
//                     </Form.Item>
//                     {/* <Form.Item
//                         name="auto_call_answer"
//                         label="Auto Call Answer"
//                     // rules={[
//                     //     {
//                     //         required: true,
//                     //         message: 'Please select the type',
//                     //     },
//                     // ]}
//                     >
//                         <Switch />
//                     </Form.Item> */}
//                     {showAgentFields && (
//                         <>
//                             <Form.Item
//                                 name="auth_username"
//                                 label="Auth Username"
//                                 rules={[
//                                     {
//                                         required: true,
//                                         message: "Please input the auth username",
//                                     },
//                                 ]}
//                             >
//                                 <Input />
//                             </Form.Item>
//                             <Form.Item
//                                 name="auth_password"
//                                 label="Auth Password"
//                                 rules={[
//                                     {
//                                         required: true,
//                                         message: "Please input the auth password",
//                                     },
//                                 ]}
//                             >
//                                 <Input />
//                             </Form.Item>
//                             {showQueue && (
//                                 <Form.Item
//                                     name="queue"
//                                     label="Queue"
//                                     rules={[
//                                         {
//                                             required: true,
//                                             message: "Please select the queue",
//                                         },
//                                     ]}
//                                 >
//                                     <Select>
//                                         {QueueState.queues.map((value, index) => (
//                                             <Select.Option value={value.name} key={value.name}>
//                                                 {value.name}
//                                             </Select.Option>
//                                         ))}
//                                     </Select>
//                                 </Form.Item>
//                             )}
//                         </>
//                     )}

//                 </Form>
//             </Spin>
//         </Modal>
//     );
// };

// export default AddUser;
