import * as ActionTypes from "../Constants/Stats"

const initialState = {
    stats: {
        client: {},
        host: {},
        database: {},
        server: {
            software: {},
            hardware: {
                ram: {
                    total: '',
                    free: ''
                },
                swap: {
                    total: '',
                    free: ''
                },
                disk: {
                    total: '',
                    free: ''
                }
            },
            uptime: {},
        },
    },
    isLoading: false,
    errMess: null
}

export const Stats = (state = initialState, action) => {
    switch (action.type) {
        default:
            return {...state}
        case ActionTypes.STATS_LOADING:
            return {...state, isLoading: true}
        case ActionTypes.STATS_SUCCESS:
            return {...state, stats: action.payload, errMess: null, isLoading: false}
        case ActionTypes.STATS_FAILED:
            return {...state, errMess: action.payload, isLoading: false}
    }
}

 // default Stats

