import * as ActionTypes from '../Constants/IvrFlowConstants'
import apiClient from '../Shared/apiClient'





export const getIvrFlow = () => dispatch => {
    dispatch(ivrFlowLoading())
    apiClient.get('api/menus').then((r) => {
        console.log("Response", r.data)
        dispatch(ivrFlowSuccess(r.data))
    }).catch((e) => {
        console.log("Error", e)

    })
}



const ivrFlowLoading = () => ({
    type: ActionTypes.IVR_FLOW_LOADING
})

const ivrFlowSuccess = data => ({
    type: ActionTypes.IVR_FLOW_SUCCESS,
    payload: data
})

const ivrFlowFailed = err => ({
    type: ActionTypes.IVR_FLOW_FAILED,
    payload: err
})

const ivrFlowReset = () => ({
    type: ActionTypes.IVR_FLOW_RESET
})

