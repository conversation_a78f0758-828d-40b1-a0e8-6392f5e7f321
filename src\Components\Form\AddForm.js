import { Checkbox, Form as AntForm, Input, Modal, Select } from "antd";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getQueues } from "../../Actions/QueueActions";

export const AddForm = ({
    formVisible,
    setFormVisible,
    onCreate,
    onUpdate,
    item,
    setItem,
    allForms
}) => {
    const [form] = AntForm.useForm();
    const [defaultCheck, setDefaultCheck] = useState(item?.default);
    const [defaultDisabled, setDefaultDisabled] = useState(false);
    const { queues } = useSelector((state) => state.QueueReducer);
    const dispatch = useDispatch();

    useEffect(() => dispatch(getQueues()), [dispatch]);

    useEffect(() => {
        if (item) {
            form.setFieldsValue({
                name: item.name,
                queue_name: item.queue_name,
                default: item["default"] === 1,
            });
        }
    }, [item, form]);

    const onDefault = (e) => {
        setDefaultCheck(e.target.checked);
        setDefaultDisabled(false);
    };

    const validateDefault = async (_, value) => {
        if (!value) return Promise.resolve();

        if (allForms.some(f => f.queue_name === form.getFieldValue("queue_name") && f.default === 1)) {
            setDefaultDisabled(true);
            return Promise.reject(new Error("Queue already has a default"));
        }
        setDefaultDisabled(false);
        return Promise.resolve();
    };

    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            const formData = {
                ...values,
                default: defaultCheck ? 1 : 0,
            };
            
            if (item) {
                await onUpdate({ ...formData, id: item.id });
            } else {
                await onCreate(formData);
            }
            
            setDefaultDisabled(false);
            form.resetFields();
            setFormVisible(false);
        } catch (error) {
            if (error.response && error.response.data.errors) {
                const errorMessage = Object.values(error.response.data.errors).flat().join("\n");
                alert(errorMessage);
            } else {
                console.error("Validation Error:", error);
            }
        }
    };

    return (
        <Modal
            visible={formVisible}
            title={item ? `Edit ${item.name}` : "Create form"}
            okText={item ? "Update" : "Save"}
            okButtonProps={{ disabled: defaultDisabled }}
            onCancel={() => {
                setFormVisible(false);
                setDefaultDisabled(false);
                setItem(null);
                form.resetFields();
            }}
            onOk={handleSubmit}
        >
            <AntForm form={form} layout="vertical">
                <AntForm.Item name="name" label="Name" rules={[{ required: true, message: "Name is required" }]}>
                    <Input />
                </AntForm.Item>
                <AntForm.Item name="queue_name" label="Queue" rules={[{ required: true, message: "Queue name is required" }]}>
                    <Select>
                        {queues.map((value, index) => (
                            <Select.Option key={index} value={value.name}>
                                {value.name}
                            </Select.Option>
                        ))}
                    </Select>
                </AntForm.Item>
                <AntForm.Item name="default" valuePropName="checked" rules={[{ validator: validateDefault }]}> 
                    <Checkbox onChange={onDefault}>Set as Default</Checkbox>
                </AntForm.Item>
            </AntForm>
        </Modal>
    );
};
