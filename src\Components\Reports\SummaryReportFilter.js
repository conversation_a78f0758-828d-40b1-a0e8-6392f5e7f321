import React, { useEffect, useState } from 'react';
import { DatePicker, Form, Modal, Select, Spin } from 'antd';
import apiClient from '../../Shared/apiClient';

const { RangePicker } = DatePicker;

export const SummaryReportFilter = ({ visible, setVisible, onCreate, isLoading }) => {
    const [form] = Form.useForm();
    const [queues, setQueues] = useState([]);
    const [loadingQueues, setLoadingQueues] = useState(false);

    useEffect(() => {
        if (visible) {
            fetchQueues();
        }
    }, [visible]);

    const fetchQueues = async () => {
        setLoadingQueues(true);
        try {
            const response = await apiClient.get('api/queue');
            setQueues(response.data);
        } catch (error) {
            console.error('Error fetching queues:', error);
        } finally {
            setLoadingQueues(false);
        }
    };

    return (
        <Spin spinning={loadingQueues || isLoading}>
            <Modal
                title="Summary Report Filter"
                open={visible}
                onCancel={() => {
                    form.resetFields();
                    setVisible(false);
                }}
                onOk={() => {
                    form
                        .validateFields()
                        .then((values) => {
                            onCreate(values);
                            form.resetFields();
                        })
                        .catch((info) => {
                            console.log('Validate Failed:', info);
                        });
                }}
                okText="Apply Filter"
                cancelText="Cancel"
            >
                <Form
                    form={form}
                    layout="vertical"
                    name="summary_report_filter"
                >
                    <Form.Item
                        name="dateRange"
                        label="Date Range"
                        rules={[
                            {
                                required: true,
                                message: 'Please select date range!',
                            },
                        ]}
                    >
                        <RangePicker 
                            style={{ width: '100%' }}
                            format="YYYY-MM-DD"
                        />
                    </Form.Item>

                    <Form.Item 
                        name="queue" 
                        label="Queue"
                    >
                        <Select 
                            placeholder="Select Queue (Optional)"
                            allowClear
                        >
                            {queues.map((queue, index) => (
                                <Select.Option key={index} value={queue.name}>
                                    {queue.name}
                                </Select.Option>
                            ))}
                        </Select>
                    </Form.Item>
                </Form>
            </Modal>
        </Spin>
    );
};

export default SummaryReportFilter;
