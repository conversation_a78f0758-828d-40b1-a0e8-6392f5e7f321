import * as ActionTypes from '../Constants/PermissionConstant'
import apiClient from "../Shared/apiClient";
import { PERMISSION, PERMISSION_IN_ROLE, PERMISSION_MODULE } from "../Endpoints/PermissionRoutes";
import { logoutUser } from "./UserActions";

export const getPermission = () => dispatch => {
    dispatch(loading())
    apiClient.get(PERMISSION).then(response => {
        dispatch(success(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(failedPermission(error.response))
        }
        else
            dispatch(failedPermission(error.message))
    })
}

export const getPermissionsInRole = () => dispatch => {
    dispatch(loading())
    apiClient.get(PERMISSION_IN_ROLE).then(response => {
        dispatch(success(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(failedPermission(error.response))
        }
        else
            dispatch(failedPermission(error.message))
    })
}

export const createPermission = data => dispatch => {
    dispatch(loading())
    apiClient.post(PERMISSION, data).then(response => {
        dispatch(createSuccess(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(failedPermission(error.response))
        }
        else
            dispatch(failedPermission(error.message))
    })
}

export const deletePermission = id => dispatch => {
    dispatch(loading())
    apiClient.delete(`${PERMISSION}/${id}`).then(response => {
        dispatch(deleteSuccess(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(failedPermission(error.response))
        }
        else
            dispatch(failedPermission(error.message))
    })
}

export const updatePermission = (data, id) => dispatch => {
    dispatch(loading())
    apiClient.put(`${PERMISSION}/${id}`, data).then(response => {
        dispatch(updateSuccess(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(failedPermission(error.response))
        }
        else
            dispatch(failedPermission(error.message))
    })
}

export const getPermissionModule = () => dispatch => {
    dispatch(loading())
    apiClient.get(PERMISSION_MODULE).then(response => {
        dispatch(permissionModules(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(failedPermission(error.response))
        }
        else
            dispatch(failedPermission(error.message))
    })

}



export const loading = () => {
    return { type: ActionTypes.PERMISSION_LOADING }
}

export const success = data => {
    return { type: ActionTypes.PERMISSION_SUCCESS, payload: data }
}
//
// export const success = data =>
// {
//     return {type: ActionTypes.PERMISSION_SUCCESS, payload: data}
// }

export const createSuccess = data => {
    return { type: ActionTypes.PERMISSION_CREATE_SUCCESS, payload: data }
}

export const updateSuccess = data => {
    return { type: ActionTypes.PERMISSION_UPDATE, payload: data }
}

export const deleteSuccess = data => {
    return { type: ActionTypes.PERMISSION_DELTE, payload: data }
}

export const failedPermission = error => {
    return { type: ActionTypes.PERMISSION_FAILED, payload: error }
}

export const permissionModules = data => {
    return { type: ActionTypes.PERMISSION_MODULE_SUSSESS, payload: data }
}