import { useEffect, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { loginUser } from "../../Actions/UserActions";
import { useHistory } from "react-router";
import Text from "antd/es/typography/Text";
import rocket from "../../Assets/login.svg";
import logo from '../../Assets/logo-lg.png'
import "./style.css";
import { Row, Col, Form, Input, notification, Checkbox, Button, Spin, message } from "antd";
import { LockOutlined, UserOutlined } from "@ant-design/icons";
import Axios from "axios";
import { P } from "@antv/g2plot";


// 
const Login2 = (props) => {
    const dispatch = useDispatch();
    const formRef = useRef();
    const [form] = Form.useForm();
    const User = useSelector((state) => state.User);
    const [record, setRecord] = useState({
        username: sessionStorage.getItem("user"),
        password: sessionStorage.getItem("password"),
    });
    const [checked, setChecked] = useState(false);
    const [errors, setErrors] = useState(false);
    const [username, setUsername] = useState("");
    const [password, setPassword] = useState("");
    let history = useHistory();
    form.setFieldsValue(record);
    const onFinish = (values) => {
        dispatch(loginUser(values.username, values.password));
        if (checked) {
            setUsername(values.username);
            setPassword(values.password);
        } else {
            setUsername("");
            setPassword("");
        }
    };
    const onFinishFailed = (errorInfo) => {
        console.log("Failed:", errorInfo);
    };
    const openNotificationWithIcon = (type, content) => {
        notification[type]({
            message: type === "error" ? "Error" : "Success",
            description: content,
        });
    };

    // const { paramName } = props.match.params;
    // console.log("paramss", paramName)
    // console.log("hh", history)-

    const path = history.location.pathname;


    const historyy = useHistory();


    // useEffect(() => {
    //     // This code will run on page load and when the URL changes
    //     const loadEventCallback = () => {
    //         const old = document.getElementById("perl");
    //         const panel = document.getElementById("pp");

    //         panel.classList.remove('panel');
    //         old.classList.remove('signin-signup');
    //         old.classList.add('signin-signup2');
    //         panel.classList.add('panel2');
    //     };

    //     // Attach the event listener for 'load'
    //     window.addEventListener('load', loadEventCallback);

    //     // Remove the event listener when the component unmounts
    //     return () => {
    //         window.removeEventListener('load', loadEventCallback);
    //     };
    // }, [window.location.pathname]);

    // useEffect(() => {

    //     if (history.location.pathname.includes("sign")) {

    //         const old = document.getElementById("perl");
    //         const panel = document.getElementById("pp");

    //         panel.classList.remove('panel')
    //         old.classList.remove('signin-signup');
    //         old.classList.add('signin-signup2')
    //         panel.classList.add('panel2')
    //     }



    // }, [])

    // useEffect(() => {
    //     const onRouteEnter = () => {
    //         // This code will run every time the route is navigated to
    //         const old = document.getElementById("perl");
    //         const panel = document.getElementById("pp");

    //         panel.classList.remove('panel');
    //         old.classList.remove('signin-signup');
    //         old.classList.add('signin-signup2');
    //         panel.classList.add('panel2');
    //     };

    //     const unlisten = historyy.listen(() => {
    //         onRouteEnter();
    //     });

    //     // Run it initially
    //     onRouteEnter();

    //     // Cleanup the listener when the component unmounts
    //     return () => {
    //         unlisten();
    //     };
    // }, [historyy]);

    useEffect(() => {
        if (typeof User.errMess === "object" && User.errMess !== null) {
            if (User.errMess?.data?.errors) {
                let msg = Object.keys(User.errMess.data.errors).map(
                    (v) => User.errMess.data.errors[v]
                );
                openNotificationWithIcon(
                    "error",
                    msg.map((v) => (
                        <Text>
                            {v}
                            {"\n"}
                        </Text>
                    ))
                );
            } else if (User.errMess.data?.message) {
                openNotificationWithIcon("error", User.errMess.data.message);
            }
        } else if (User.errMess?.message) {
            openNotificationWithIcon("error", User.errMess?.message);
        } else if (User.errMess) {
            openNotificationWithIcon("error", User.errMess);
        }
        if (User.errMess?.errors) {
            setErrors(true);
            formRef.current.setFields([
                {
                    name: "username",
                    errors: User.errMess.errors.username,
                },
            ]);
        }
    }, [User.errMess]);

    useEffect(() => {
        if (User.loggedIn) {
            openNotificationWithIcon("success", "Login success, redirecting...");
            console.log("RRRRRRRRRRR");
            history.push("/");

            setTimeout(() => {
                const authToken = sessionStorage.getItem("auth_token");

                Axios.get(`${process.env.REACT_APP_baseURL}api/check-available-minutes`, {
                    headers: {
                        Authorization: `Bearer ${authToken}`,
                    },
                })
                .then((response) => {
                    if (response.data.status === "warning") {
                        message.warn(response?.data?.message);
                    }
                })
                .catch((error) => {
                    console.error("API Error:", error.response ? error.response.data : error);
                });

            }, 5000);
        }
    }, [User.loggedIn]);

    function onChange(e) {
        if (e.target.checked) setChecked(true);
        else setChecked(false);
    }
    const style = {
        height: '40px',
        width: '350px',
        // borderRadius: '20px',
    }
    return (
        <div className="forms-container">
            <img src={logo} alt="" height={80} width={250} style={{ position: 'absolute', top: 0, right: '5%' }} />
            <div className="login-forms">
                <Spin spinning={User.isLoading} >
                    <Form
                        ref={formRef}
                        onFinish={onFinish}
                        onFinishFailed={onFinishFailed}
                        initialValues={record}
                        size="large"
                        action="#"
                        id="sign-in"
                    >
                        <span className="top-heading">
                            {/* <img src={logo} alt="" height={90} width={250} /> */}
                            Admin Login
                        </span>

                        <div style={{ marginBottom: 0 }}>
                            <Form.Item
                                name="username"
                                rules={[
                                    {
                                        required: true,
                                        message: "Please input your username!",
                                    },
                                ]}

                            >
                                <Input
                                    prefix={<UserOutlined className="site-form-item-icon outlined-input" />}
                                    placeholder="Username"
                                    style={style}
                                />
                            </Form.Item>

                            <Form.Item
                                name="password"
                                rules={[
                                    {
                                        required: true,
                                        message: "Please input your password!",
                                    },
                                ]}
                            >
                                <Input.Password
                                    style={style}
                                    prefix={<LockOutlined className="site-form-item-icon" />}
                                    placeholder="Password"
                                />
                            </Form.Item>
                        </div>

                        <div >
                            {/* <Form.Item name="remember" valuePropName="checked">
                                <Checkbox onChange={onChange}><b style={{ color: '#15347c' }}>Remember me</b></Checkbox>
                            </Form.Item> */}
                            <Form.Item>
                                <Button
                                    size="large"
                                    type="primary"
                                    style={{ height: '40px', width: '350px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                                    block
                                    htmlType="submit"
                                >
                                    Login
                                </Button>
                            </Form.Item>
                        </div>
                    </Form>
                </Spin>
            </div>
        </div>
        // <Row className="forms-container">
        //     <Col span={24}>
        //         <div className="container forms-container">
        //             <div className="forms-container">
        //                 <img src={logo} alt="" height={90} width={250} style={{ position: 'absolute', top: 0, right: '5%' }} />
        //                 <div id="perl" className="signin-signup" >
        //                     <Spin spinning={User.isLoading} >
        //                         <Form
        //                             ref={formRef}
        //                             onFinish={onFinish}
        //                             onFinishFailed={onFinishFailed}
        //                             initialValues={record}
        //                             size="large"
        //                             action="#"
        //                             id="sign-in"
        //                         >
        //                             <span className="top-heading" style={{ color: '' }}>
        //                                 {/* <img src={logo} alt="" height={90} width={250} /> */}
        //                                 Admin Login
        //                             </span>

        //                             <div style={{ marginBottom: 0 }}>
        //                                 <Form.Item
        //                                     name="username"
        //                                     rules={[
        //                                         {
        //                                             required: true,
        //                                             message: "Please input your username!",
        //                                         },
        //                                     ]}

        //                                 >
        //                                     <Input
        //                                         prefix={<UserOutlined className="site-form-item-icon outlined-input" />}
        //                                         placeholder="Username"
        //                                         style={style}
        //                                     />
        //                                 </Form.Item>

        //                                 <Form.Item
        //                                     name="password"
        //                                     rules={[
        //                                         {
        //                                             required: true,
        //                                             message: "Please input your password!",
        //                                         },
        //                                     ]}
        //                                 >
        //                                     <Input.Password
        //                                         style={style}
        //                                         prefix={<LockOutlined className="site-form-item-icon" />}
        //                                         placeholder="Password"
        //                                     />
        //                                 </Form.Item>
        //                             </div>

        //                             <div >
        //                                 <Form.Item name="remember" valuePropName="checked">
        //                                     <Checkbox onChange={onChange}><b style={{ color: '#15347c' }}>Remember me</b></Checkbox>
        //                                 </Form.Item>
        //                                 <Form.Item>
        //                                     <Button
        //                                         size="large"
        //                                         type="primary"
        //                                         style={{ height: '40px', width: '350px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        //                                         block
        //                                         htmlType="submit"
        //                                     >
        //                                         Login
        //                                     </Button>
        //                                 </Form.Item>
        //                             </div>
        //                         </Form>
        //                     </Spin>
        //                 </div>
        //             </div>

        //             <div className="panels-container" style={{

        //             }}>
        //                 <div id="pp" className="panel left-panel">

        //                     {/* <div class="content">
        //                     </div> */}
        //                     {/* <img src={rocket} className="image" alt="" /> */}
        //                 </div>
        //                 <div className="panel right-panel">
        //                     {/* <div className="content">
        //                         <button className="btn transparent" id="sign-in-btn">
        //                             Sign in
        //                         </button>
        //                     </div> */}
        //                 </div>
        //             </div>
        //         </div>
        //     </Col>
        // </Row>
    );
};
export default Login2;