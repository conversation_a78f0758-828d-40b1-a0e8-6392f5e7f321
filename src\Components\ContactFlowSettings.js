import { Button, DatePicker, Form, Input, Modal, Select } from 'antd'
import React, { useEffect, useState } from 'react'
import apiClient from '../Shared/apiClient';
import moment from 'moment';
import { openNotificationWithIcon } from '../Shared/notification';

export default function ContactFlowSettings({ visible, setVisible }) {


    const [confirmLoading, setConfirmLoading] = useState(false);

    const handleOk = () => {

        const values = form.getFieldsValue();
        console.log("ok check", values)
        setConfirmLoading(true);
        onFinishTwo(values);
        setConfirmLoading(false)
    };
    const handleCancel = () => {
        console.log('Clicked cancel button');
        setVisible(false);
    };
    const [form] = Form.useForm();

    const onFinishTwo = (values) => {

        apiClient.patch('api/update-ivr-menu-settings', values).then((r) => {
            setVisible(!visible)
            console.log("response", r.data)
            getTime();
            openNotificationWithIcon('success', r.data.message)
        }).catch((e) => console.log("error", e));
        // console.log("values", values)
    };



    const getTime = () => {
        apiClient.get('api/get-ivr-menu-settings').then((r) => {
            console.log("response", r.data)
            form.setFieldsValue({
                start: moment(r?.data[0]?.start, "HH:mm:ss"),
                end: moment(r?.data[0]?.end, "HH:mm:ss"),
                queue: r.data[0].queue,
                weeks: r.data[0].weeks
            })

        }).catch((e) => console.log("error", e));
    }
    useEffect(() => {
        getTime();
    }, [visible])

    return (

        <div>

            <Modal
                title="Set Time"
                open={visible}
                onOk={handleOk}
                confirmLoading={confirmLoading}
                onCancel={handleCancel}
            >
                {/* <p>{modalText}</p> */}
                <Form onFinish={onFinishTwo} form={form} layout="vertical" name="SettingsForm">
                    <Form.Item
                        name="start"
                        label="Start Time"
                        rules={[
                            {
                                required: true,
                                message: "This field is required.",
                            },
                        ]}
                    >
                        <DatePicker.TimePicker format="HH:mm:ss" />
                    </Form.Item>
                    <Form.Item
                        name="end"
                        label="End Time"
                        rules={[
                            {
                                required: true,
                                message: "This field is required.",
                            },
                        ]}
                    >
                        <DatePicker.TimePicker format="HH:mm:ss" />
                    </Form.Item>
                    <Form.Item
                        name="weeks"
                        label="Weekdays"
                        rules={[
                            {
                                required: true,
                                message: "This field is required.",
                            },
                        ]}
                    >
                        <Select
                            mode="multiple"
                            allowClear
                            placeholder="Please Select Weekdays"
                        >
                            <Select.Option value="Monday">Monday</Select.Option>
                            <Select.Option value="Tuesday">Tuesday</Select.Option>
                            <Select.Option value="Wednesday">Wednesday</Select.Option>
                            <Select.Option value="Thursday">Thursday</Select.Option>
                            <Select.Option value="Friday">Friday</Select.Option>
                            <Select.Option value="Saturday">Saturday</Select.Option>
                            <Select.Option value="Sunday">Sunday</Select.Option>
                        </Select>
                    </Form.Item>
                    <Form.Item
                        name="queue"
                        label="Default Queue"
                        style={{ width: '200px' }}
                    >
                        <Input type='text' />
                    </Form.Item>

                    <Button type="primary" htmlType="submit">
                        Save
                    </Button>

                </Form>
            </Modal>
        </div>
    )
}
