import { useDispatch, useSelector } from "react-redux";
import { Button, Form, Input, Modal, Row, Space, Spin, Table } from "antd";
import { DeleteOutlined, EditOutlined, PlusOutlined } from "@ant-design/icons";
import { useState, useEffect } from "react";
import { CreateWorkCode, DeleteWorkCode, getWorkCode, UpdateWorkCode } from "../Actions/WorkcodeActions";
import { openNotificationWithIcon } from "../Shared/notification";
import Text from "antd/es/typography/Text";


const WorkCode = () => {

    const Workcode = useSelector(state => state.WorkcodeReducer)
    // const [showDetails, setShowDetails] = useState(false)
    const [showEdit, setShowEdit] = useState(false)
    const [form] = Form.useForm()
    const [record, setRecord] = useState(null)
    const [showCreate, setShowCreate] = useState(false)
    const dispatch = useDispatch()

    useEffect(() => {
        dispatch(getWorkCode())
    }, [])

    useEffect(() => {
        if (Workcode.errMess)
            openNotificationWithIcon('error', Workcode?.errMess?.data?.message)

    }, [Workcode.errMess])

    useEffect(() => {
        if (Workcode.message != null) {
            openNotificationWithIcon('success', Workcode.message)
            dispatch(getWorkCode())
        }
    }, [Workcode.message])

    const handleSubmit = values => {
        setShowEdit(false)
        dispatch(UpdateWorkCode(values, record.id))
    }

    const handleCreate = values => {
        dispatch(CreateWorkCode(values))
        form.setFieldsValue({ 'name': '' })
        setShowCreate(false)
    }

    const handleShowCreate = () => {
        setRecord(Workcode.workcode)
        setShowCreate(true)
    }

    return (
        <Spin spinning={Workcode.isLoading}>
            <Modal
                centered
                title="Edit Work Code"
                visible={showEdit}
                destroyOnClose={true}
                closable={true}
                okText="Update"
                onOk={() => {
                    form.validateFields()
                        .then(values => handleSubmit(values))
                        .catch(error => console.log(error))
                }}
                onCancel={() => {
                    setShowEdit(false)
                }}
            >
                <Form
                    form={form}
                    layout="inline"
                    name="form_in_modal"
                    initialValues={record}
                >
                    <Form.Item
                        name="name"
                        kay="1"
                        label="WorkCode"
                    >
                        <Input />
                    </Form.Item>

                </Form>
            </Modal>
            <Modal
                centered
                title="Add Work Code"
                visible={showCreate}
                destroyOnClose={true}
                closable={true}
                okText="Submit"
                onOk={() => {
                    form.validateFields()
                        .then(values => handleCreate(values))
                        .catch(error => console.log(error))
                }}
                onCancel={() => {
                    setShowCreate(false)
                }}
            >
                <Form
                    form={form}
                    layout="inline"
                    name="form_in_modal"
                >
                    <Row gutter={[16, 24]}>
                        <Form.Item
                            name="name"
                            kay="2"
                            label="WorkCode"
                        >
                            <Input />
                        </Form.Item>

                    </Row>
                </Form>
            </Modal>
            <Space style={{ marginBottom: 16 }}>
                <Button onClick={handleShowCreate} icon={<PlusOutlined />} type="primary">Add New</Button>
            </Space>
            <Table scroll={{ x: 800 }} size="small" bordered dataSource={Workcode.workcode}>
                <Table.Column dataIndex="name" key="name" title="Name" />
                <Table.Column dataIndex="created_at" key="created_at" title="CreatedAt" />
                <Table.Column dataIndex="updated_at" key="updated_at" title="UpdateAt" />
                <Table.Column align="center" title="Actions" render={(text, record) => (
                    <Space>
                        <Button onClick={() => {
                            form.setFieldsValue(record)
                            setRecord(record)
                            setShowEdit(true)
                        }} icon={<EditOutlined />} type="primary">Edit</Button>
                        <Button onClick={() => {
                            dispatch(DeleteWorkCode(record.id))
                        }} icon={<DeleteOutlined />} type="danger">Delete</Button>
                    </Space>
                )} />
            </Table>
        </Spin>
    )
}

export default WorkCode