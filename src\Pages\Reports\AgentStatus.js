import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, DatePicker, Input, Form, Select, Space, Table, TimePicker, Modal, Spin } from "antd";
import apiClient from "../../Shared/apiClient";
import moment from "moment";
import { CSVLink } from "react-csv";

import Highlighter from "react-highlight-words";
import {

    FilterOutlined,
    ReloadOutlined,
    SaveOutlined,
    SearchOutlined,
    SyncOutlined,

} from "@ant-design/icons";
import { useDispatch } from "react-redux";

const timeFormat = 'HH:mm';


const exportHeaders = [
    { label: 'Agent Name', key: 'agentName' },
    { label: 'Time In', key: 'Time-in' },
    { label: 'Time Out', key: 'Time-out' },
    { label: 'Status', key: 'Status' },
    { label: 'Time-Difference', key: 'Time-Difference' }
]

const AgentStatus = () => {
    const [form] = Form.useForm();
    const [data, setData] = useState([]);
    const [date, setDate] = useState();
    const [to, setTo] = useState();
    const [from, setFrom] = useState();
    const [loading, setLoading] = useState(false);
    const [fetchReportCheck, setFetchReportCheck] = useState(true);

    // queue selection
    const [queues, setQueues] = useState([])
    const [selectedQueue, setSelectedQueue] = useState("")
    const [agent, setAgent] = useState([]);
    const [selectedAgent, setSelectedAgent] = useState();
    const [allIds, setAllIds] = useState([]);
    const [selectedAgentString, setSelectedAgentString] = useState();
    const [showFilter, setShowFilter] = useState(false)
    const [resetFilter, setResetFilter] = useState(false)

    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const searchInput = useRef()

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }



    const columns = [
        {
            title: "Agent Name",
            dataIndex: "agentName",
            key: "agentName",
            ...getColumnSearchProps('agentName'),
            width: 150,
        },
        {
            title: "Time In",
            dataIndex: "Time-in",
            key: "Time-in",
            width: 150,
        },
        {
            title: "Time Out",
            dataIndex: "Time-out",
            key: "Time-out",
            width: 150,
        },
        {
            title: "Time-Difference",
            dataIndex: "Time-Difference",
            key: "Time-Difference",
            width: 150,
        },
        {
            title: "Status",
            dataIndex: "Status",
            key: "status",
            ...getColumnSearchProps('Status'),
            width: 150,
        },
    ];

    const resetFormFilter = () => {
        // dispatch(getTrunkPerHourReport())
        // dispatch(trunkPerHourReset())
        // console.log(new Date().toLocaleString() + '')
        form.resetFields()
        setResetFilter(true)
    }
    const onDateChange = (date, dateString) => {
        setDate(moment(dateString));
    };

    const onTimeChange = (time, timeString) => {
        setFrom(timeString[0]);
        setTo(timeString[1]);
        setFetchReportCheck(false);
    };



    useEffect(() => {
        apiClient.get('/api/queue').then((res) => {
            setQueues(res.data)
        }).catch(err => console.log(err.response))
    }, []);

    useEffect(() => {
        if (selectedQueue) {
            apiClient
                .get(`/api/getAgents?queue=${selectedQueue}`)
                .then((resp) => {
                    setAgent(resp.data)
                    // setAgent(resp.data.map(({ name }) => ({title: name, value: name})))
                    setAllIds(resp.data.map(({ name }) => (name)))
                })
                .catch((err) => {
                    console.log(err.message);
                });
        }
    }, [selectedQueue])

    useEffect(() => {
        setSelectedAgentString(
            () =>
                selectedAgent &&
                selectedAgent.map((v, i) => `agent[${i}]=${v}`).join("&")
        );
    }, [selectedAgentString, selectedAgent]);


    const onChange = (value) => {
        if (value.includes('all')) {
            setSelectedAgent(allIds);
        } else {
            setSelectedAgent(value);
        }
        // setSelectedAgent(value);
        setFetchReportCheck(false);
    };

    const onSearch = (value) => {
        console.log("search:", value);
    };

    const fetchReport = () => {
        setLoading(true);
        apiClient
            .post(`/api/report/getAgentStatus`, {

                date: date?._i,
                from: from,
                to: to,
                queue: selectedQueue,
                agents: selectedAgent,
            })
            .then((resp) => {
                setData(resp.data);
                setLoading(false);
                // form.resetFields();
            })
            .catch((err) => {
                setLoading(false);
                console.log(err.message);
            });
    };

    return (
        <>
            {/* <Card

                style={{ background: "#fff", borderRadius: "2px", padding: 10 }}

            > */}
            <Table
                title={data =>
                    <>
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            {`Agent Status Report`}
                            <Space>
                                <Button onClick={() => resetFormFilter()} type="danger" icon={<ReloadOutlined />}>Reset Filter</Button>
                                <Button onClick={() => setShowFilter(true)} icon={<FilterOutlined />}>Filter</Button>
                                <CSVLink filename="Agent Status Report.csv" data={data} headers={exportHeaders}>
                                    <Button type="primary" disabled={data.length === 0}>Download</Button>
                                </CSVLink>
                            </Space>

                        </div>
                    </>
                }

                columns={columns}
                dataSource={data}
                pagination={false}
                loading={loading}
                size="default"
                rowKey={"Agent"}
                bordered
            />

            <AgentStatusFilter
                form={form}
                resetFilter={resetFilter}
                visible={showFilter}
                date={date}
                fetchReport={fetchReport}
                onTimeChange={onTimeChange}
                onDateChange={onDateChange}
                onSearch={onSearch}
                selectedAgent={selectedAgent}
                setSelectedQueue={setSelectedQueue}
                onChange={onChange}
                agent={agent}
                selectedQueue={selectedQueue}
                setVisible={setShowFilter} />
            {/* </Card> */}
        </>
    );
};

export default AgentStatus;

export const AgentStatusFilter = ({ form, agent, selectedAgent, fetchReport, onChange, visible, setVisible, selectedQueue, setSelectedQueue, date, onDateChange, onSearch, onTimeChange, resetFilter, setDate }) => {


    const dispatch = useDispatch()
    const [filterDate, setFilterDate] = useState()
    // useEffect(() => form.resetFields(), [resetFilter])
    const [queues, setQueues] = useState([])

    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data))
    }, [])

    const handleQueue = (value) => {
        console.log("selected value", value);
        setSelectedQueue(value)

    }
    // useEffect(() => {
    //     console.log("selected queue ", selectedQueue)
    // }, [selectedQueue])

    // console.log("selected queue ", selectedQueue)

    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            okText="Submit"
            onOk={() => form.validateFields()
                .then(value => {
                    // dispatch(getTrunkPerHourReport({ date: filterDate, queue: queue }))
                    fetchReport()
                    setVisible(false)
                })
            }
            // okButtonProps={{
            //     loading: btnLoading,
            //     icon: <SaveOutlined />
            // }}
            title="Agent Status Filter"
        >
            {/* <Spin spinning={btnLoading} indicator={<SyncOutlined spin />}> */}
            <Form
                form={form}
                layout="vertical"
            >
                <Form.Item name={"picker"}>
                    <DatePicker
                        onChange={onDateChange}
                        value={date}
                    />
                </Form.Item>
                <Form.Item name={"timePicker"}>
                    <TimePicker.RangePicker
                        format={timeFormat}
                        onChange={onTimeChange}
                    />
                </Form.Item>
                <Form.Item name={"queues"}>
                    <Select
                        showSearch
                        placeholder="Select Queue"
                        optionFilterProp="children"
                        value={selectedQueue}
                        onChange={handleQueue}
                        style={{ marginRight: "10px", minWidth: "150px" }}
                    >
                        {queues.length > 0 &&
                            queues.map((value, index) => (
                                <Select.Option key={index} value={value.name}>
                                    {value.name}
                                </Select.Option>
                            ))}
                    </Select>
                </Form.Item>
                <Form.Item name={"agent"}>
                    <Select
                        showSearch
                        placeholder="Select an agent"
                        optionFilterProp="children"
                        mode="multiple"
                        value={selectedAgent}
                        onChange={onChange}
                        disabled={selectedQueue.length == 0 || selectedQueue == 'undefined'}
                        onSearch={onSearch}
                        style={{ marginRight: "10px", minWidth: "150px" }}
                    >
                        {agent.length > 0 && <Select.Option value="all">Select All</Select.Option>}

                        {agent &&
                            agent.map((value, index) => (
                                <Select.Option key={index} value={value.name}>
                                    {value.name}
                                </Select.Option>
                            ))}

                    </Select>
                </Form.Item>
            </Form>
            {/* </Spin> */}
        </Modal>
    )
}