import React from "react";
import { Modal, Descriptions, Spin, message } from "antd";

const ViewReportSummary = ({ isOpen, onClose, data = {}, loading }) => {
  
  const getTotalNumber = (obj = {}) => {
    if (Object.keys(obj).length > 0) {
      return Object.values(obj).reduce((a, b) => a + b, 0);
    }
    return 0;
  };

  return (
    <Modal
      title="Campaign Summary"
      open={isOpen}
      onCancel={onClose}
      onOk={onClose}
      footer={null} 
    >
      {loading ? (
        <Spin /> 
      ) : (
        <Descriptions title="Campaign Summary" bordered>
          {/* Display total calls */}
          <Descriptions.Item label="Total" span={24}>
            {getTotalNumber(data)}
          </Descriptions.Item>

          {/* Display individual call dispositions */}
          {Object.keys(data).length > 0 &&
            Object.keys(data).map((key) => (
              <Descriptions.Item key={key} label={key} span={24}>
                {data[key] || 0}
              </Descriptions.Item>
            ))}
        </Descriptions>
      )}
    </Modal>
  );
};

export default ViewReportSummary;