{"name": "visual-ivr", "version": "0.1.0", "private": true, "homepage": "./", "dependencies": {"@ant-design/icons": "^4.3.0", "@antv/g2plot": "^2.4.8", "@craco/craco": "^6.1.1", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "antd": "^4.9.1", "axios": "^0.21.0", "chart.js": "^3.7.1", "craco-less": "^1.17.1", "file-saver": "^2.0.5", "jspdf": "^2.3.1", "jspdf-autotable": "^3.5.14", "laravel-echo": "^2.0.2", "lodash": "^4.17.20", "moment": "^2.29.1", "moment-timezone": "^0.5.34", "pusher-js": "^8.4.0", "react": "^17.0.1", "react-audio-player": "^0.17.0", "react-chartjs-2": "^4.0.1", "react-csv": "^2.0.3", "react-dom": "^17.0.1", "react-dotenv": "^0.1.2", "react-downloader-file": "^1.0.0", "react-export-excel": "^0.5.3", "react-google-charts": "^4.0.0", "react-h5-audio-player": "^3.8.0", "react-highlight-words": "^0.17.0", "react-icons": "^4.3.1", "react-quill": "^2.0.0-beta.4", "react-redux": "^7.2.2", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-scripts": "4.0.1", "recharts": "^2.1.6", "redux": "^4.0.5", "redux-logger": "^3.0.6", "redux-thunk": "^2.3.0", "web-vitals": "^0.2.4"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "craco eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "react-dotenv": {"whitelist": ["API_URL"]}}