import {
    But<PERSON>,
    Card,
    Modal,
    Space,
    Upload,
    Form,
    Switch,
    Select,
    Table,
    Input,
    DatePicker,
    Row,
    Col,
} from "antd";
import {
    DownloadOutlined,
    ExportOutlined,
    FrownTwoTone,
    SettingFilled,
    SmileTwoTone,
    UploadOutlined,
} from "@ant-design/icons";
import { useEffect, useState } from "react";
import apiClient from "../Shared/apiClient";
import { openNotificationWithIcon } from "../Shared/notification";

export const ServiceRating = () => {
    const [uploadVisible, setUploadVisible] = useState(false);
    const [users, setUsers] = useState([]);
    const [settingsVisible, setSettingsVisible] = useState(false);
    const [filters, setFilters] = useState({});
    const [data, setData] = useState({});
    const [loading, setLoading] = useState(false);
    const [settings, setSettings] = useState({});

    const getSettings = () => apiClient.get("/api/getServiceRatingSettings");
    const getAgents = () => apiClient.get("/api/userAgent");
    const getRatings = (filters = {}) =>
        apiClient.get("/api/getServiceRatings", {
            params: {
                ...filters,
            },
        });
    const downloadRatings = (filters = {}) =>
        apiClient.get("/api/getServiceRatings", {
            responseType: "blob",
            params: {
                ...filters,
            },
        });

    useEffect(() => {
        setLoading(true);
        getAgents()
            .then((r) => setUsers(r.data))
            .then((_) => setLoading(false))
            .catch((_) => setLoading(false));
        getRatings()
            .then((r) => setData(r.data))
            .then((_) => setLoading(false))
            .catch((_) => setLoading(false));
        getSettings()
            .then((r) => setSettings(r.data))
            .catch((_) => setLoading(false));
    }, []);

    const columns = [
        {
            title: "id",
            dataIndex: "id",
            key: "id",
        },
        {
            title: "uniqueid",
            dataIndex: "uniqueid",
            key: "uniqueid",
        },
        {
            title: "rating",
            dataIndex: "rating",
            key: "rating",
            render: (text, _) =>
                parseInt(text) === settings.worst ? (
                    <FrownTwoTone style={{ fontSize: 20 }} />
                ) : (
                    <SmileTwoTone style={{ fontSize: 20 }} twoToneColor="#52c41a" />
                ),
        },
        {
            title: "number",
            dataIndex: "number",
            key: "number",
        },
        {
            title: "agent",
            dataIndex: "agentId",
            key: "agent",
        },
        // {
        //     title: "name",
        //     dataIndex: "name",
        //     key: "name",
        // },
        {
            title: "Recording",
            dataIndex: "recordingfile",
            key: "recordingfile",
            render: (text, record) =>
            // console.log("record", record?.recordingfile)
            (
                record?.recordingfile !== "" &&
                <Button
                    href={
                        process.env.REACT_APP_baseURL +
                        "/api/download/" +
                        record.recordingfile
                    }
                    target="_blank"
                    type="primary"
                    block
                    icon={<DownloadOutlined />}
                />
            )
        },
        {
            title: "created_at",
            dataIndex: "created_at",
            key: "created_at",
        },
    ];

    const handleFormSubmit = (values) => {
        setLoading(true);
        if (values.startDate)
            values.startDate = values.startDate.format("YYYY-MM-DD");
        if (values.endDate) values.endDate = values.endDate.format("YYYY-MM-DD");
        setFilters(values);
        getRatings(values)
            .then((r) => setData(r.data))
            .then((_) => setLoading(false))
            .catch((e) => console.log(e));
    };

    const handleDownloadClick = () => {
        setLoading(true);
        downloadRatings({ ...filters, export: true })
            .then((r) => {
                const url = window.URL.createObjectURL(new Blob([r.data]));
                const link = document.createElement("a");
                link.href = url;
                link.setAttribute("download", "service_ratings.xlsx");
                document.body.appendChild(link);
                link.click();
            })
            .then((_) => setLoading(false))
            .catch((e) => console.log(e));
    };

    const handlePageChange = ({ current, pageSize }) => {
        setLoading(true);
        getRatings({ ...filters, page: current, perPage: pageSize })
            .then((r) => setData(r.data))
            .then((_) => setLoading(false))
            .catch((e) => console.log(e));
    };

    const onUploadSubmit = () => {
        setLoading(true);
        getRatings()
            .then((r) => setData(r.data))
            .then((_) => setLoading(false))
            .catch((e) => console.log(e));
    };

    return (
        <Card
            size="small"
            title="Service Rating"
            bordered={false}
            extra={
                <Space>
                    <Button
                        onClick={(_) => setUploadVisible(true)}
                        icon={<UploadOutlined />}
                    />
                    <Button
                        onClick={(_) => setSettingsVisible(true)}
                        icon={<SettingFilled />}
                    />
                    <Button onClick={handleDownloadClick} icon={<ExportOutlined />} />
                </Space>
            }
        >
            <p>
                This module enables agent to transfer customer calls to rate their
                service. Only supports binary rating (ex. 1 or 2).
            </p>
            <Form size="large" onFinish={handleFormSubmit}>
                <Row gutter={10}>
                    <Col>
                        <Form.Item
                            labelCol={{
                                span: 6,
                            }}
                            label="From"
                            name="startDate"
                        >
                            <DatePicker format="YYYY-MM-DD" />
                        </Form.Item>
                        <Form.Item
                            labelCol={{
                                span: 6,
                            }}
                            label="To"
                            name="endDate"
                        >
                            <DatePicker format="YYYY-MM-DD" />
                        </Form.Item>
                        <Form.Item
                            labelCol={{
                                span: 6,
                            }}
                            label="Agent"
                            name="agent"
                        >
                            <Select mode="multiple" placeholder="Select agents">
                                {users.map((u) => (
                                    <Select.Option key={u.id} value={u.auth_username}>
                                        {u.auth_username}
                                    </Select.Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col>
                        <Form.Item
                            labelCol={{
                                span: 6,
                            }}
                            label="Uniqueid"
                            name="uniqueid"
                        >
                            <Input />
                        </Form.Item>
                        <Form.Item
                            labelCol={{
                                span: 6,
                            }}
                            label="Rating"
                            name="rating"
                        >
                            <Select mode="multiple">
                                <Select.Option value={settings.best}>Best</Select.Option>
                                <Select.Option value={settings.worst}>Worst</Select.Option>
                            </Select>
                        </Form.Item>
                        <Form.Item
                            wrapperCol={{
                                offset: 6,
                            }}
                        >
                            <Button loading={loading} htmlType="submit" type="primary">
                                Submit
                            </Button>
                        </Form.Item>
                    </Col>
                </Row>
            </Form>
            <br />
            <Table
                scroll={{ x: true }}
                loading={loading}
                rowKey={(r) => r.id}
                dataSource={data.data}
                columns={columns}
                pagination={{
                    current: data.current_page,
                    pageSize: data.per_page,
                    total: data.total,
                }}
                onChange={handlePageChange}
            />
            <UploadModal
                visible={uploadVisible}
                setVisible={setUploadVisible}
                onUploadSubmit={onUploadSubmit}
            />
            <SettingsModal
                settings={settings}
                visible={settingsVisible}
                setVisible={setSettingsVisible}
            />
        </Card>
    );
};

const SettingsModal = ({ visible, setVisible, settings }) => {
    const [loading, setLoading] = useState(false);
    const [files, setFiles] = useState([]);
    const [form] = Form.useForm();
    const getFiles = () => apiClient.get("/api/getServiceRatingFiles");
    const updateSettings = (values) =>
        apiClient.patch("/api/updateServiceRatingSettings", values);

    useEffect(() => {
        form.setFieldsValue({
            file: settings.service_rating_file_id,
            status: settings.status,
            best: settings.best,
            worst: settings.worst,
        });
    }, [form, settings]);

    useEffect(() => {
        setLoading(true);
        getFiles()
            .then((r) => setFiles(r.data))
            .then((_) => setLoading(false))
            .catch((e) => console.log(e));
    }, [visible]);

    const handleSubmitForm = (values) => {
        setLoading(true);
        updateSettings(values)
            .then((r) => openNotificationWithIcon("success", r.data))
            .then((_) => setLoading(false))
            .catch((e) => console.log(e));
    };

    return (
        <Modal
            visible={visible}
            okText="Save"
            title="Settings"
            onCancel={(_) => setVisible(false)}
            okButtonProps={{
                loading: loading,
            }}
            onOk={(_) =>
                form
                    .validateFields()
                    .then((r) => handleSubmitForm(r))
                    .then((r) => setVisible(false))
                    .catch((e) => console.log(e))
            }
        >
            <Form
                form={form}
                labelCol={{ span: 10 }}
                wrapperCol={{ span: 14 }}
                layout="horizontal"
                size="large"
            >
                <Form.Item name="status" label="Enable" valuePropName="checked">
                    <Switch />
                </Form.Item>
                <Form.Item
                    name="file"
                    label="Select File"
                    extra={
                        <p style={{ marginTop: 10 }}>
                            This file will be played to customer before submitting keypress
                            value of rating.
                        </p>
                    }
                    rules={[
                        {
                            required: true,
                            message: "This field is required.",
                        },
                    ]}
                >
                    <Select>
                        {files.map((f) => (
                            <Select.Option value={f.id} key={f.id}>
                                {f.name}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
                <Form.Item
                    name="best"
                    label="Best score DTMF key"
                    rules={[
                        {
                            required: true,
                            message: "This field is required.",
                        },
                    ]}
                >
                    <Select>
                        <Select.Option value={0}>0</Select.Option>
                        <Select.Option value={1}>1</Select.Option>
                        <Select.Option value={2}>2</Select.Option>
                        <Select.Option value={3}>3</Select.Option>
                        <Select.Option value={4}>4</Select.Option>
                        <Select.Option value={5}>5</Select.Option>
                        <Select.Option value={6}>6</Select.Option>
                        <Select.Option value={7}>7</Select.Option>
                        <Select.Option value={8}>8</Select.Option>
                        <Select.Option value={9}>9</Select.Option>
                    </Select>
                </Form.Item>
                <Form.Item
                    name="worst"
                    label="Worst score DTMF key"
                    rules={[
                        {
                            required: true,
                            message: "This field is required.",
                        },
                    ]}
                >
                    <Select>
                        <Select.Option value={0}>0</Select.Option>
                        <Select.Option value={1}>1</Select.Option>
                        <Select.Option value={2}>2</Select.Option>
                        <Select.Option value={3}>3</Select.Option>
                        <Select.Option value={4}>4</Select.Option>
                        <Select.Option value={5}>5</Select.Option>
                        <Select.Option value={6}>6</Select.Option>
                        <Select.Option value={7}>7</Select.Option>
                        <Select.Option value={8}>8</Select.Option>
                        <Select.Option value={9}>9</Select.Option>
                    </Select>
                </Form.Item>
            </Form>
        </Modal>
    );
};

const UploadModal = ({ visible, setVisible, onUploadSubmit }) => {
    const handleFileUpload = (file) => {
        setTimeout(() => {
            openNotificationWithIcon(
                "success",
                `${file.name} uploaded successfully!`
            );
        }, 500);
    };

    const customRequest = ({ file, onSuccess, onError }) => {
        const formData = new FormData();
        formData.append("file", file);

        apiClient
            .post("/api/uploadServiceRatingFile", formData)
            .then((data) => {
                handleFileUpload(file); // Call the handleFileUpload function upon successful upload
                onSuccess(data, file);
            })
            .catch((error) => {
                console.error("Error uploading file:", error);
                onError(error);
            });
    };

    return (
        <Modal
            visible={visible}
            title="File Upload"
            onOk={() => {
                onUploadSubmit();
                setVisible(false);
            }}
            onCancel={(_) => setVisible(false)}
        >
            <Upload
                name="file"
                customRequest={customRequest}
            // headers={{ Authorization: `Bearer ${getToken()}` }}
            // action={uploadURL}
            >
                <Button icon={<UploadOutlined />}>Click to upload</Button>
            </Upload>
        </Modal>
    );
};
