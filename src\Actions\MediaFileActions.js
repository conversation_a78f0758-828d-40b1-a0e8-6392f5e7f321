import * as ActionTypes from '../Constants/MediaFileConstant'
import apiClient from "../Shared/apiClient";
import { MEDIAFILE } from "../Endpoints/MediaFilesRoutes";
import { logoutUser } from "./UserActions";

export const getMediaFile = () => dispatch => {
    dispatch(loading())
    apiClient.get(MEDIAFILE).then((response) => {
        dispatch(success(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(fileFailed(error.response))
        }
        else
            dispatch(fileFailed(error.message))
    })
}

export const deleteMediaFile = (id) => dispatch => {
    console.log("delete file")
    dispatch(loading())
    apiClient.delete(`${MEDIAFILE}/${id}`).then((response) => {
        dispatch(fileDelete(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(fileFailed(error.response))
        }
        else
            dispatch(fileFailed(error.message))
    })
}

export const loading = () => {
    return { type: ActionTypes.FILE_LOADING }
}

export const success = data => {
    return { type: ActionTypes.FILE_SUCCESS, payload: data }
}

export const fileFailed = error => {
    return { type: ActionTypes.FILE_FAILED, payload: error }
}

export const fileDelete = data => {
    return { type: ActionTypes.FILE_DELETE, payload: data }
}