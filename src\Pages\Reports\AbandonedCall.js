import { useDispatch, useSelector } from "react-redux";
import { openNotificationWithIcon } from "../../Shared/notification";
import { Button, Card, DatePicker, Form, Input, Modal, Select, Space, Spin, Table } from "antd";
import { DownloadOutlined, FilterOutlined, ReloadOutlined, SaveOutlined, SearchOutlined, SyncOutlined } from "@ant-design/icons";
import { CSVLink } from "react-csv";
import { useEffect, useRef, useState } from "react";
import { getAbandonedCall } from "../../Actions/AbandonedAction";
import { abandonCallReset } from "../../Actions/AbandonedAction";
import apiClient from "../../Shared/apiClient";
import Highlighter from "react-highlight-words";



export const AbandonedCall = () => {
    const [form] = Form.useForm()
    const abandonedReducer = useSelector(state => state.AbandonedReducer)
    const [pagination, setPagination] = useState({ current: 1, pageSize: 50 })
    const [value, setValue] = useState()
    const [range, setRange] = useState([])
    const [dst, setDst] = useState(null)
    const [filterVisible, setFilterVisible] = useState(false)
    const dispatch = useDispatch()
    // const [filterVisible, setFilterVisible] = useState(false);
    const [resetFilter, setResetFilter] = useState(false)
    const [queues, setQueues] = useState([]);
    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const [filterValues, setFilterValues] = useState({})
    const [loading, setLoading] = useState(false)

    const searchInput = useRef()

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}>
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }




    const columns = [
        {
            title: 'Date',
            dataIndex: 'date',
            key: 'date',

        },
        {
            title: 'Time',
            dataIndex: 'time',
            key: 'time'
        },
        {
            title: 'Number',
            dataIndex: 'src',
            key: 'src',
            ...getColumnSearchProps('src')

        },
        {
            title: 'Position',
            dataIndex: 'position',
            key: 'position',
            ...getColumnSearchProps('position')
        },
        {
            title: 'Orig Position',
            dataIndex: 'origposition',
            key: 'origposition',
            ...getColumnSearchProps('origposition')
        },
        {
            title: 'Wait Time',
            dataIndex: 'waittime',
            key: 'waittime',
            ...getColumnSearchProps('waittime')
        }
    ]


    useEffect(() => {

        apiClient.get('api/queue').then((res) => {
            setQueues(res.data);
        })

    }, [filterVisible])

    useEffect(() => {
        if (abandonedReducer.errMess !== '') openNotificationWithIcon('error', abandonedReducer.errMess)
    }, [abandonedReducer.errMess])

    const onFinish = (values) => {
        setRange(values.range)
        setDst(values.dst)
        dispatch(getAbandonedCall(values, pagination))
        setValue(values)
    }


    const resetFormFilter = () => {
        setResetFilter(true);
        // console.log('gggggggg')
        form.resetFields()
        setFilterValues({})
        dispatch(abandonCallReset())

    }
    const handleTableChange = (pagination, filters, sorter) => {
        let data = {
            pagination,
            filters
        }
        dispatch(getAbandonedCall(value, pagination))
    };

    let exportRoute = `${process.env.REACT_APP_baseURL}/api/abandonCallExport?range=${range ? JSON.stringify(range) : ''}&dst=${dst || ''}`



    const exportCsv = () => {
        setLoading(true);
        apiClient.post('/api/report/export-abandon-call',
            filterValues,
            { responseType: 'blob' }).then((response) => {

                const type = response.headers['content-type'];
                const blob = new Blob([response.data], { type: type });
                const link = document.createElement('a');
                link.href = window.URL.createObjectURL(blob);
                link.download = 'abandon-call.csv';
                link.click();
                setLoading(false)
            }).catch((e) => {
                setLoading(false);
                console.log("Export error", e);
            });
    }


    return (
        <>
            {/* <span>Abandon Call</span> */}
            <Table
                title={data => {
                    return (
                        <>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                                Abandon Call
                                <Space style={{ textAlign: 'right' }}>
                                    <Button
                                        danger
                                        type="primary"
                                        icon={<ReloadOutlined />}
                                        onClick={() => {
                                            resetFormFilter();
                                        }}
                                    >Reset Filter</Button>
                                    <Button
                                        icon={<FilterOutlined />}
                                        onClick={() => {
                                            setFilterVisible(!filterVisible);
                                        }} >Filter</Button>

                                    {/* <CSVLink data={abandonedReducer.data?.data || []} filename="abandoned.csv" > */}
                                    <Button
                                        disabled={!abandonedReducer?.data?.data}
                                        onClick={exportCsv}
                                        type={"primary"}
                                        icon={<DownloadOutlined />}
                                    >
                                        Download
                                    </Button>
                                    {/* </CSVLink> */}
                                </Space>
                            </div></>
                    )
                }}
                dataSource={abandonedReducer.data?.data || []}
                columns={columns}
                bordered
                scroll={{ x: 1100 }}
                loading={{ spinning: abandonedReducer.isLoading || loading, indicator: <SyncOutlined spin /> }}
                pagination={{
                    current: abandonedReducer.data?.current_page,
                    pageSize: abandonedReducer.data?.per_page,
                    total: abandonedReducer.data?.total
                }}
                rowKey={(data, index) => index}
                onChange={handleTableChange}
            // title={d => "Abandon Call"}
            />

            <AbandonFilterCall setFilterValues={setFilterValues} form={form} setRange={setRange} setValue={setValue} resetFilter={resetFilter} queues={queues} setQueues={setQueues} visible={filterVisible} setVisible={setFilterVisible} />
        </>
    )
}

const AbandonFilterCall = ({ visible, setVisible, setRange, queues, setQueues, resetFilter, form, buttonLoading, setValue, setFilterValues }) => {

    const dispatch = useDispatch()
    // const abandonedReducer = useSelector(state => state.AbandonedReducer)
    const [pagination, setPagination] = useState({ current: 1, pageSize: 50 })

    useEffect(() => {
        if (resetFilter) {
            form.resetFields()
        }
    }, [resetFilter])

    const onFinish = (values) => {
        setRange(values?.range)
        setFilterValues(values)
        dispatch(getAbandonedCall(values, pagination))
        setValue(values)
        setVisible(false)
    }


    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            title="Abandon Filter"
            size="small"
            okText="Submit"
            onOk={() => form.validateFields()
                .then(data => onFinish(data))
                .catch(e => console.log(e))
            }
        // okText="Submit"
        // oKButtonProps={{
        //     loading: buttonLoading
        // }}
        >
            <Form
                form={form}
                name="bulk_recording_download"
                layout={"vertical"}
            >
                <Form.Item name="range" label="Date Range">
                    <DatePicker.RangePicker format="YYYY-MM-DD" />
                </Form.Item>
                <Form.Item name="dst" label="Number">
                    <Input />
                </Form.Item>
                <Form.Item
                    label="Queue"
                    name="queue"
                >
                    <Select placeholder="Queue Option">
                        {/* <Select.Option>Select Queue</Select.Option> */}

                        {queues.map((elm, index) => {
                            return (
                                <Select.Option value={elm.name}>
                                    {elm.name}
                                </Select.Option>)

                        })}

                    </Select>
                </Form.Item>

            </Form>
        </Modal>
    )
}
