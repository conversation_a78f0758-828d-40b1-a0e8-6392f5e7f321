import { useDispatch, useSelector } from "react-redux";
import {
    agentRingNoAnswerReset,
    getRingNoAnswer, getRingNoAsnwerFilter, loading
} from "../../Actions/AgentReportActions";
import { Button, Descriptions, Input, Modal, Space, Spin, Table } from "antd";
import { FilterOutlined, PlusCircleOutlined, RedoOutlined, SearchOutlined } from "@ant-design/icons";
import { useState, useEffect, useRef } from 'react'
import RingNoAnswerFilter from "../../Components/Reports/RingNoAnswerFilter";
import { CSVLink } from "react-csv";
import Highlighter from "react-highlight-words";
import apiClient from "../../Shared/apiClient";

let exportRoute = `${process.env.REACT_APP_baseURL}/api/ring-no-answer/export`

const RingNoAnswer = () => {
    const report = useSelector(state => state.AgentReportReducer)
    const [record, setRecord] = useState(null)
    const [showDetails, setShowDetails] = useState(false)
    const [filter, setFilter] = useState(false)
    const [filterOption, setFilterOption] = useState({})
    const [filterValues, setFilterValues] = useState(null)
    const [searchText, setSearchText] = useState("");
    const [searchedColumn, setSearchedColumn] = useState("");

    const dispatch = useDispatch()

    // useEffect(() => {
    //     dispatch(getRingNoAnswer())
    // }, [])

    function onFilter(values) {
        setFilterValues(values)
        dispatch(getRingNoAsnwerFilter(values))
        setFilter(false)
    }

    const handleReset = () => {
        setFilterValues(null)
        exportRoute = `${process.env.REACT_APP_baseURL}/api/ring-no-answer/export`
        dispatch(agentRingNoAnswerReset())

    }

    const handleTableChange = (pagination, filters, sorter) => {
        dispatch(getRingNoAnswer(pagination?.current, pagination?.pageSize, filterOption))
    };

    const searchInput = useRef();

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({
            setSelectedKeys,
            selectedKeys,
            confirm,
            clearFilters,
        }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={(e) =>
                        setSelectedKeys(e.target.value ? [e.target.value] : [])
                    }
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: "block" }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button
                        onClick={() => handleResett(clearFilters)}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0]);
                            setSearchedColumn(dataIndex);
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined style={{ color: filtered ? "#1890ff" : undefined }} />
        ),
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex]
                    .toString()
                    .toLowerCase()
                    .includes(value.toLowerCase())
                : "",
        onFilterDropdownVisibleChange: (visible) => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: (text) =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: "#ffc069", padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ""}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0]);
        setSearchedColumn(dataIndex);
    };

    const handleResett = (clearFilters) => {
        clearFilters({ confirm: true });
        setSearchText("");
    };
    // let data;
    // const downloadCsv = async () => {
    //     console.log("a")
    //     const payload = {
    //         "date": filterValues?.date,
    //         "agent": filterValues?.agent,
    //         "partya": filterValues.partya,
    //         "queue": filterValues?.queue
    //     }
    //     // console.log(payload)
    //     data = await apiClient.get('/api/ring-no-answer/export', payload)
    //     console.log("csv data", data.data)
    // }
    // href={`${exportRoute}?date=${JSON.stringify(filterValues?.date) || null}&agent=${JSON.stringify(filterValues?.agent) || null}&partya=${filterValues?.partya || null}&queue=${filterValues?.queue || null}`}


    return (
        <>

            <RingNoAnswerFilter setVisible={setFilter} isLoading={report.isLoading} onCancel={() => setFilter(false)} visible={filter} onCreate={onFilter} setFilterOption={setFilterOption} record={"ok"} />
            <Modal
                width="100%"
                visible={showDetails}
                closable={true}
                onCancel={() => setShowDetails(false)}
                onOk={() => setShowDetails(false)}
                centered
            >
                <Descriptions style={{ padding: 20 }} title="Pause Reason Info" bordered size="small">
                    {record && Object.keys(record).map((key, index) => (
                        <Descriptions.Item label={key}>
                            {(record[key]) ? (record[key]) : ""}
                        </Descriptions.Item>))}
                </Descriptions>
            </Modal>

            {/* <div style={{ float: 'right' }}>
                <div style={{ marginBottom: 16 }}>
                   
                    <Space>
                        <Button type={"primary"} onClick={(<CSVDownload data={report.agentReport} target="_blank" />)}>
                            <CSVLink data={report.agentReport} > Download CSV</CSVLink>
                        </Button>
                        
                        <ReactExport.ExcelFile element={<Button type={"default"}>Download Excel</Button>} fileExtension={"xlsx"} filename={"CDR Report"}>
                            <ReactExport.ExcelFile.ExcelSheet data={report.agentReport} >
                                {record && Object.entries(record).map((key, index) => (
                                    <ReactExport.ExcelFile.ExcelColumn label={key} value={key} />
                                ))}
                            </ReactExport.ExcelFile.ExcelSheet>
                        </ReactExport.ExcelFile>
                    </Space>
                </div>
            </div> */}

            <Table scroll={{ x: 800 }} title={d =>
                <>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        Ring No Answer
                        <Space>
                            <Button onClick={handleReset} type="danger" icon={<RedoOutlined />}>
                                Reset Filter

                            </Button>
                            <Button onClick={() => setFilter(true)} icon={<FilterOutlined />}>
                                Filter
                            </Button>

                            {/* <CSVLink
                                filename="DailyLoginReport.csv"
                                // data={report.agentReport}
                                target="_blank"
                            > */}
                            <Button
                                disabled={report.agentReport.length == 0}
                                href={`${exportRoute}?date=${JSON.stringify(filterValues?.date) || null}&agent=${JSON.stringify(filterValues?.agent) || null}&partya=${filterValues?.partya || null}&queue=${filterValues?.queue || null}`}
                                type="primary"
                            // onClick={downloadCsv}
                            >
                                Download
                            </Button>
                            {/* </CSVLink> */}
                        </Space>
                    </div>
                </>}
                bordered
                dataSource={report.agentReport}
                pagination={report.pagination}
                loading={report.loading}
                onChange={handleTableChange} >
                <Table.Column dataIndex="Sn" key="Sn" title="S#" render={(text, record, index) => (++index)} />
                <Table.Column dataIndex="queue" key="queue" title="Queue" {...getColumnSearchProps('queue')} />
                <Table.Column dataIndex="partya" key="partya" title="Party A" {...getColumnSearchProps('partya')} />
                <Table.Column dataIndex="agent" key="agent" title="Agent" {...getColumnSearchProps('agent')} />
                <Table.Column dataIndex="time" key="time" title="Time" />
            </Table>

        </>
    )
}

export default RingNoAnswer