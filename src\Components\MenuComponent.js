// import {
//     BoxPlotOutlined,
//     CodepenCircleOutlined,
//     DashboardOutlined,
//     FileTextOutlined,
//     GoldOutlined,
//     Html5Outlined,
//     LockOutlined,
//     PauseCircleOutlined,
//     SettingOutlined,
//     SnippetsOutlined,
//     UnlockOutlined,
//     UserAddOutlined,
//     UsergroupAddOutlined,
//     MailOutlined,
//     FieldTimeOutlined,
//     ClusterOutlined,
//     CustomerServiceFilled,
//     FundProjectionScreenOutlined,
//     PlayCircleOutlined,
// } from "@ant-design/icons";
// import { Link, useLocation } from "react-router-dom";
// import { Drawer, Menu } from "antd";
// import { useEffect, useState } from "react";
// import Sider from "antd/es/layout/Sider";
// import logo from "../Assets/logo-lg.png";
// import small from "../Assets/logo-icon.png";
// import IvrSettings from "../Pages/IvrSettings";
// import { MdPlaylistAdd } from "react-icons/md";


// const { SubMenu } = Menu;
// const rootSubmenuKeys = ["sub1", "sub2"];
// const reportNumberStyles = {
//     float: "left",
//     fontWeight: "600",
//     marginRight: "1em",
// };

// const MenuComponent = ({ isToggled, onClose }) => {
//     const [openKeys, setOpenKeys] = useState([]);
//     const [selectedKey, setSelectedKey] = useState("/");
//     let location = useLocation();

//     useEffect(() => {
//         setSelectedKey(location.pathname);

//         return () => setSelectedKey('/')
//     }, [location]);

//     const onOpenChange = (keys) => {
//         const latestOpenKey = keys.find((key) => openKeys.indexOf(key) === -1);
//         if (rootSubmenuKeys.indexOf(latestOpenKey) === -1) {
//             setOpenKeys(keys);
//         } else {
//             setOpenKeys(latestOpenKey ? [latestOpenKey] : []);
//         }
//     };


//     const items = (
//         <>
//             <div
//                 className="logo hideOnMobile"
//                 style={{
//                     display: "flex",
//                     justifyContent: "center",
//                     alignItems: "center",
//                 }}
//             >
//                 <Link to="/">
//                     {!isToggled ? (
//                         <img src={logo} alt="App logo" height={65} width={170} />
//                     ) : (
//                         <img src={small} alt="App logo" height={65} width={65} />
//                     )}
//                 </Link>
//             </div>

//             <Menu
//                 mode="vertical"
//                 defaultSelectedKeys={[window.location.pathname.split('/admin')[1]]}
//                 openKeys={openKeys}
//                 onOpenChange={onOpenChange}
//             >
//                 <Menu.Item key="/" icon={<DashboardOutlined />}>
//                     <Link to="/">Dashboard</Link>
//                 </Menu.Item>
//                 <Menu.Item key="/monitoringg" icon={<FundProjectionScreenOutlined />}>
//                     {/* <a href={'/monitoring'} target="_blank">Monitoring</a> */}
//                     <a href={`${process.env.REACT_APP_AgentMonitoringURL}/?auth_token=${sessionStorage.getItem('auth_token')}`} target="_blank">Monitoring</a>
//                 </Menu.Item>
//                 <SubMenu title="Users" key="sub1" icon={<UserAddOutlined />}>
//                     <Menu.Item key="/users" icon={<UserAddOutlined />}>
//                         <Link to="/users">Users</Link>
//                     </Menu.Item>
//                     <Menu.Item key="/role" icon={<UnlockOutlined />}>
//                         <Link to={"/role"}>Roles</Link>
//                     </Menu.Item>
//                     <Menu.Item key="/permission" icon={<LockOutlined />}>
//                         <Link to={"/permission"}>Permissions</Link>
//                     </Menu.Item>
//                 </SubMenu>
//                 <Menu.Item key="/agent" icon={<UsergroupAddOutlined />}>
//                     <Link to="/agent">Agents</Link>
//                 </Menu.Item>
//                 <Menu.Item key="/supervisor" icon={<UsergroupAddOutlined />}>
//                     <Link to="/supervisor">Supervisors</Link>
//                 </Menu.Item>
//                 <Menu.Item key="/queue" icon={<GoldOutlined />}>
//                     <Link to="/queue">Queues</Link>
//                 </Menu.Item>
//                 {/*<SubMenu key="campaigns" title="Campaigns" icon={<BoxPlotOutlined />}>*/}
//                 <Menu.Item key="/campaign" icon={<BoxPlotOutlined />}>
//                     <Link to="/campaign">Campaigns</Link>
//                 </Menu.Item>
//                 {/*<Menu.Item key="/monitoring" icon={<MonitorOutlined />}>*/}
//                 {/*    <Link to="/monitoring">Monitoring</Link>*/}
//                 {/*</Menu.Item>*/}
//                 {/*</SubMenu>*/}
//                 <Menu.Item key="/cid-lookup" icon={<SnippetsOutlined />}>
//                     <Link to="/cid-lookup">CID Look up</Link>
//                 </Menu.Item>
//                 <Menu.Item key="/form" icon={<SnippetsOutlined />}>
//                     <Link to="/form">Forms</Link>
//                 </Menu.Item>
//                 <Menu.Item key="/script" icon={<Html5Outlined />}>
//                     <Link to="/script">Scripts</Link>
//                 </Menu.Item>
//                 {/*<Menu.Item key="/visualIVR" icon={<StarTwoTone/>}>*/}
//                 {/*    <Link to={"/visualIVR"}>Visual IVR</Link>*/}
//                 {/*</Menu.Item>*/}
//                 <Menu.Item key="/workCode" icon={<CodepenCircleOutlined />}>
//                     <Link to={"/workCode"}>Workcodes</Link>
//                 </Menu.Item>
//                 <Menu.Item key="/break" icon={<PauseCircleOutlined />}>
//                     <Link to={"/break"}>Breaks</Link>
//                 </Menu.Item>
//                 <Menu.Item key="ivr-flow" icon={<PlayCircleOutlined />} >
//                     <Link to={"/ivr-flow"}>ContactFlow</Link>
//                 </Menu.Item>
//                 <Menu.Item key="/voiceMail" icon={<MailOutlined />}>
//                     <Link to="/voiceMail">Voicemails</Link>
//                 </Menu.Item>
//                 <Menu.Item key="/ivr-settings" icon={<MdPlaylistAdd />}>
//                     <Link to="/ivr-settings">GreetEase</Link>
//                 </Menu.Item>
//                 <Menu.Item key="/serviceRating" icon={<CustomerServiceFilled />}>
//                     <Link to="/serviceRating">Service Rating</Link>
//                 </Menu.Item>
//                 {/*<Menu.Item key="customRouting" icon={<ReloadOutlined />}>*/}
//                 {/*    <Link to="/customRouting">Custom Routing</Link>*/}
//                 {/*</Menu.Item>*/}
//                 {/*<Menu.Item key="/mediaFiles" icon={<SoundOutlined />}>*/}
//                 {/*    <Link to={"/mediaFiles"}>Media Files</Link>*/}
//                 {/*</Menu.Item>*/}
//                 {/*<Menu.Item key="/inboundRouteRouters" icon={<SwitcherTwoTone/>}>*/}
//                 {/*    <Link to={"/inboundRoutes"}>Inbound Routes</Link>*/}
//                 {/*</Menu.Item>*/}
//                 {/*<Menu.Item key="/outboundRoutes" icon={<PullRequestOutlined/>}>*/}
//                 {/*    <Link to={"/outboundRoutes"}>Outbound Routes</Link>*/}
//                 {/*</Menu.Item>*/}
//                 <Menu.Item key="/settings" icon={<SettingOutlined />}>
//                     <Link to={"/settings"}>Settings</Link>
//                 </Menu.Item>
//                 <Menu.Item key="/bitrix" icon={<ClusterOutlined />}>
//                     <Link to={"/bitrix"}>Bitrix</Link>
//                 </Menu.Item>

//                 <Menu.Item key="/ticker" icon={<FieldTimeOutlined />}>
//                     <Link to={"/ticker"}>Ticker</Link>
//                 </Menu.Item>

//                 {/* sms route */}
//                 {/* <SubMenu title="SMS" key="sub4" icon={<UserAddOutlined />}>
//                     <Menu.Item key="/sms_category" icon={<UserAddOutlined />}>
//                         <Link to="/sms_category">SMS Category</Link>
//                     </Menu.Item>
//                     <Menu.Item key="/sms_template" icon={<UnlockOutlined />}>
//                         <Link to={"/sms_template"}>SMS Template</Link>
//                     </Menu.Item>
//                 </SubMenu> */}

//                 <SubMenu title="Reports" key="/reports" icon={<FileTextOutlined />}>
//                     <SubMenu
//                         title="Agent Performance Reports"
//                         key="/agentPerformance"
//                         icon={<FileTextOutlined />}
//                     >
//                         {/* <Menu.Item key="/agentCallReport">
//                             <Link to="/agentCallReport">
//                                 <span style={reportNumberStyles}>1-a</span>Agent Call Report
//                             </Link>
//                         </Menu.Item> */}
//                         <Menu.Item key="/breakReport">
//                             <Link to="/breakReport">
//                                 <span style={reportNumberStyles}>1-a</span>Agent Break Report
//                                 (Inbound)
//                             </Link>
//                         </Menu.Item>
//                         <Menu.Item key="/callPerAgentReport">
//                             <Link to="/callPerAgentReport">
//                                 <span style={reportNumberStyles}>1-b</span>Call per Agent
//                             </Link>
//                         </Menu.Item>
//                         <Menu.Item key="/dailyLoginReport">
//                             <Link to="/dailyLoginReport">
//                                 <span style={reportNumberStyles}>1-c</span>Daily Login Report

//                             </Link>
//                         </Menu.Item>
//                         <Menu.Item key="/RingNoAnswer">
//                             <Link to={"/RingNoAnswer"}>
//                                 <span style={reportNumberStyles}>1-d</span>Ring No Answer
//                                 Report
//                             </Link>
//                         </Menu.Item>
//                         {/* <Menu.Item key="/agentCallSummary-inbound">
//                             <Link to={"/agentCallSummary-inbound"}>
//                                 <span style={reportNumberStyles}>1-f</span>Agent Inbound Unique
//                                 Call Summary
//                             </Link>
//                         </Menu.Item> */}
//                         {/* <Menu.Item key="/agentCallSummary-outbound">
//                             <Link to={"/agentCallSummary-outbound"}>
//                                 <span style={reportNumberStyles}>1-g</span>Agent Outbound Unique
//                                 Call Summary
//                             </Link>
//                         </Menu.Item> */}
//                         <Menu.Item key="/inboundAgentSummary">
//                             <Link to="/inboundAgentSummary">
//                                 <span style={reportNumberStyles}>1-e</span>Inbound Agent Summary Report
//                             </Link>
//                         </Menu.Item>
//                         <Menu.Item key="/outboundAgentSummary">
//                             <Link to="/outboundAgentSummary">
//                                 <span style={reportNumberStyles}>1-f</span>Outbound Agent Summary Report
//                             </Link>
//                         </Menu.Item>
//                         <Menu.Item key="/RingNoAnswerAgentWiseSummaryReport">
//                             <Link to={"/RingNoAnswerAgentWiseSummaryReport"}>
//                                 <span style={reportNumberStyles}>1-g</span>Ring No Answer Agent Wise Summary
//                             </Link>
//                         </Menu.Item>
//                     </SubMenu>
//                     <SubMenu
//                         title="Traffic Analytics Reports"
//                         key="/trafficAnalytics"
//                         icon={<FileTextOutlined />}
//                     >
//                         <Menu.Item key="/callDetailReport">
//                             <Link to="/callDetailReport">
//                                 <span style={reportNumberStyles}>2-a</span>Call Detail
//                                 Report
//                             </Link>
//                         </Menu.Item>
//                         <Menu.Item key="/outboundActivity">
//                             <Link to="/outboundActivity">
//                                 <span style={reportNumberStyles}>2-b</span>Outbound Activity Report
//                             </Link>
//                         </Menu.Item>
//                         {/* <Menu.Item key="/minutesOfMeeting">
//                             <Link to={"/minutesOfMeeting"}>
//                                 <span style={reportNumberStyles}>2-c</span>Unique Outbound Calls
//                                 Summary
//                             </Link>
//                         </Menu.Item> */}
//                     </SubMenu>
//                     <SubMenu
//                         title="Call Center Analytics Reports"
//                         key="/callCenterAnalytics"
//                         icon={<FileTextOutlined />}
//                     >
//                         <Menu.Item key="/abandonCall">
//                             <Link to="/abandonCall">
//                                 <span style={reportNumberStyles}>3-a</span>Abandoned
//                                 Call Report
//                             </Link>
//                         </Menu.Item>
//                         {/* <Menu.Item key="/abandonCallHour">
//                             <Link to="/abandonCallHour">
//                                 <span style={reportNumberStyles}>3-b</span>Hourly Report
//                                 Abondoned Calls
//                             </Link>
//                         </Menu.Item> */}
//                         <Menu.Item key="/AbandonCallReportDifference">
//                             <Link to="/AbandonCallReportDifference">
//                                 <span style={reportNumberStyles}>3-b</span> Abandon Call Report Difference Report
//                             </Link>
//                         </Menu.Item>
//                         <Menu.Item key="/inboundDisposition">
//                             <Link to="/inboundDisposition">
//                                 <span style={reportNumberStyles}>3-c</span>Inbound Disposition
//                                 Report
//                             </Link>
//                         </Menu.Item>
//                         <Menu.Item key="/inboundDispositionSummary">
//                             <Link to="/inboundDispositionSummary">
//                                 <span style={reportNumberStyles}>3-d</span>Inbound Disposition
//                                 Summary
//                             </Link>
//                         </Menu.Item>
//                         <Menu.Item key="/RingNoAnswerQueueSummaryReport">
//                             <Link to={"/RingNoAnswerQueueSummaryReport"}>
//                                 <span style={reportNumberStyles}>3-e</span>Ring No Answer Queue Summary Report
//                             </Link>
//                         </Menu.Item>
//                         <Menu.Item key="/holdTimeReport">
//                             <Link to="/holdTimeReport">
//                                 <span style={reportNumberStyles}>3-f</span>Queue Wait Time
//                                 Report
//                             </Link>
//                         </Menu.Item>
//                         <Menu.Item key="/outboundDisposition">
//                             <Link to="/outboundDisposition">
//                                 <span style={reportNumberStyles}>3-g</span>Outbound Disposition
//                                 Report
//                             </Link>
//                         </Menu.Item>
//                         <Menu.Item key="/outboundDispositionSummary">
//                             <Link to="/outboundDispositionSummary">
//                                 <span style={reportNumberStyles}>3-h</span>Outbound Disposition
//                                 Summary
//                             </Link>
//                         </Menu.Item>
//                         <Menu.Item key={"/ChannelOccupancy"}>
//                             <Link to={"/ChannelOccupancy"}>
//                                 <span style={reportNumberStyles}>3-i</span>Channel Occupancy
//                                 Graph
//                             </Link>
//                         </Menu.Item>
//                         {/* <Menu.Item key="/formDataReport">
//                             <Link to="/formDataReport">
//                                 <span style={reportNumberStyles}>3-k</span>Form Data Report
//                             </Link>
//                         </Menu.Item> */}
//                         <Menu.Item key="/MergeCDRFormDataReport">
//                             <Link to="/MergeCDRFormDataReport">
//                                 <span style={reportNumberStyles}>3-j</span>Merge CDR Form Data Report
//                             </Link>
//                         </Menu.Item>

//                         <Menu.Item key="/trunkPerHour">
//                             <Link to="/trunkPerHour">
//                                 <span style={reportNumberStyles}>3-k</span>Trunk Per Hour Report
//                             </Link>
//                         </Menu.Item>
//                         <Menu.Item key="/bulkRecordingDownload">
//                             <Link to="/bulkRecordingDownload">
//                                 <span style={reportNumberStyles}>3-l</span>Bulk Recording
//                                 Download
//                             </Link>
//                         </Menu.Item>
//                         <Menu.Item key="/summary-report">
//                             <Link to="/summary-report">
//                                 <span style={reportNumberStyles}>3-m</span>Inbound Summary Report
//                             </Link>
//                         </Menu.Item>

//                     </SubMenu>
//                     {/*<Menu.Item key="/agentReport"}
//                     {/*    <Link to={"/agentReport"}>Agent Reports</Link>*/}
//                     {/*</Menu.Item>*/}
//                     {/*<Menu.Item key="/cdrReport">
//                         <Link to={"/cdrReport"}>Call Detail Records</Link>
//                     </Menu.Item>*/}
//                 </SubMenu>
//             </Menu>
//         </>
//     );

//     return (
//         <>
//             <Sider
//                 className="hideOnMobile"
//                 collapsed={isToggled}
//                 style={{ overflow: "auto", background: "white" }}
//             >
//                 {items}
//             </Sider>

//             <Drawer
//                 className="hideOnDesktop"
//                 title={false}
//                 closeIcon={false}
//                 placement="left"
//                 onClose={onClose}
//                 visible={isToggled}
//             >
//                 {items}
//             </Drawer>
//         </>
//     );
// };

// export default MenuComponent;

import {
    BoxPlotOutlined,
    CodepenCircleOutlined,
    DashboardOutlined,
    FileTextOutlined,
    GoldOutlined,
    Html5Outlined,
    LockOutlined,
    PauseCircleOutlined,
    SettingOutlined,
    SnippetsOutlined,
    UnlockOutlined,
    UserAddOutlined,
    UsergroupAddOutlined,
    MailOutlined,
    FieldTimeOutlined,
    ClusterOutlined,
    CustomerServiceFilled,
    FundProjectionScreenOutlined,
    AudioOutlined,
    DatabaseOutlined,
    ApartmentOutlined,
    SoundOutlined,
    PlayCircleOutlined,
    NumberOutlined,
} from "@ant-design/icons";
import { Link, useLocation } from "react-router-dom";
import { Drawer, Menu } from "antd";
import { useEffect, useState } from "react";
import Sider from "antd/es/layout/Sider";
import logo from "../Assets/logo-lg.png";
import small from "../Assets/logo-icon.png";
import IvrSettings from "../Pages/IvrSettings";
import { MdPlaylistAdd } from "react-icons/md";
import apiClient from "../Shared/apiClient";
import { useSelector } from "react-redux";
const { SubMenu } = Menu;
const rootSubmenuKeys = ["sub1", "sub2"];
const reportNumberStyles = {
    float: "left",
    fontWeight: "600",
    marginRight: "1em",
};
// Define the icon mapping based on menu titles
const iconMapping = {
    Monitoring: <FundProjectionScreenOutlined />,
    Users: <UserAddOutlined />,
    Permissions: <LockOutlined />,
    Agents: <UsergroupAddOutlined />,
    Supervisor: <UsergroupAddOutlined />,
    Dashboard: <DashboardOutlined />,
    Roles: <UnlockOutlined />,
    Campaigns: <BoxPlotOutlined />,
    Queues: <GoldOutlined />,
    Announcement: <SoundOutlined />,
    CID_Look_up: <SnippetsOutlined />,
    Forms: <SnippetsOutlined />,
    Scripts: <Html5Outlined />,
    Workcodes: <CodepenCircleOutlined />,
    Breaks: <PauseCircleOutlined />,
    ContactFlow: <MdPlaylistAdd />,
    Voicemails: <MailOutlined />,
    GreetEase: <MdPlaylistAdd />,
    Service_Rating: <CustomerServiceFilled />,
    Settings: <SettingOutlined />,
    Bitrix: <ClusterOutlined />,
    PrepaidSetting: <SettingOutlined />,
    Ticker: <FieldTimeOutlined />,
    EmailSetting : < MailOutlined  /> ,
    SMS: <UserAddOutlined />,
    "Module_Accessibility": <LockOutlined />,
    "SMS_Category": <UserAddOutlined />,
    "SMS_Template": <UnlockOutlined />,
    Reports: <FileTextOutlined />,
    Agent_Performance_Reports: <FileTextOutlined />,
    Traffic_Analytics_Reports: <FileTextOutlined />,
    Call_Center_Analytics_Reports: <FileTextOutlined />,
    "1_a_Agent_Break_Report_Inbound": <FileTextOutlined />,
    "1_b_Call_Per_Agent": <FileTextOutlined />,
    "1_c_Daily_Login_Report": <FileTextOutlined />,
    "1_d_Ring_No_Answer_Report": <FileTextOutlined />,
    "1_e_Inbound_Agent_Summary_Report": <FileTextOutlined />,
    "1_f_Outbound_Agent_Summary_Report": <FileTextOutlined />,
    "1_g_Ring_No_Answer_Agent_Wise_Summary": <FileTextOutlined />,
    "2_a_Call_Detail_Report": <FileTextOutlined />,
    "2_b_Outbound_Activity_Report": <FileTextOutlined />,
    "3_a_Abandoned_Call_Report": <FileTextOutlined />,
    "3_b_Abandon_Call_Report_Difference_Report": <FileTextOutlined />,
    "3_c_Inbound_Disposition_Report": <FileTextOutlined />,
    "3_d_Inbound_Disposition_Summary": <FileTextOutlined />,
    "3_e_Ring_No_Answer_Queue_Summary_Report": <FileTextOutlined />,
    "3_f_Queue_Wait_Time_Report": <FileTextOutlined />,
    "3_g_Outbound_Disposition_Report": <FileTextOutlined />,
    "3_h_Outbound_Disposition_Summary": <FileTextOutlined />,
    "3_i_Channel_Occupancy_Graph": <FileTextOutlined />,
    "3_j_Trunk_Per_Hour_Report": <FileTextOutlined />,
    "3_k_Bulk_Recording_Download": <FileTextOutlined />,
    "3_l_Inbound_Summary_Report": <FileTextOutlined />,
    "Agentless_Calling_Server": <DatabaseOutlined />,
    "Agentless_Recording": <AudioOutlined />,
    "Agentless_Campaign": <ApartmentOutlined />
    // AudioOutlined
    // DatabaseOutlined
    // ApartmentOutlined
};



const MenuComponent = ({ isToggled, onClose }) => {
    const [menuData, setMenuData] = useState([]);
    const [openKeys, setOpenKeys] = useState([]);
    const [selectedKey, setSelectedKey] = useState("/");
    const location = useLocation();

    const role = useSelector(state => state.RoleReducer)

    useEffect(() => {

        const fetchMenuData = async () => {
            try {
                const response = await apiClient.get("api/allowed_modules");
                console.log("response", response.data);
                if (response.status !== 200) {
                    throw new Error(`Error: ${response.statusText}`);
                }
                setMenuData(response.data); // Update the state with menu data
                // Set initial open keys based on the current location
                const parentKeys = findParentKeys(response.data, location.pathname);
                setOpenKeys(parentKeys);
            } catch (error) {
                console.error("Error fetching menu data:", error);
            }
        };
        fetchMenuData();
        setSelectedKey(location.pathname);
        return () => setSelectedKey("/");
    }, [location, role.modules]);

    const findParentKeys = (menuItems, route, parents = []) => {
        for (const menuItem of menuItems) {
            if (menuItem.route === route) {
                return [...parents, menuItem.navbar_id.toString()];
            }
            if (menuItem.children && menuItem.children.length > 0) {
                const foundKeys = findParentKeys(menuItem.children, route, [
                    ...parents,
                    menuItem.navbar_id.toString(),
                ]);
                if (foundKeys.length > 0) {
                    return foundKeys;
                }
            }
        }
        return [];
    };
    const onOpenChange = (keys) => {
        setOpenKeys(keys); // Allow multiple submenus to remain open
    };
    // Recursive function to render menu items



    const renderMenuItems = (menuItems) => {
        return menuItems.map((menuItem) => {
            const iconKey = menuItem.menu_title
                .replace(/\s+/g, "_")
                .replace(/\(/g, "")
                .replace(/\)/g, "");
            const icon = iconMapping[iconKey] || <DashboardOutlined />;

            // Check if the menu title is "Monitoring"
            if (menuItem.menu_title === "Monitoring") {
                return (
                    <Menu.Item key="Monitoring" icon={icon}>
                        <a
                            href={`${process.env.REACT_APP_AgentMonitoringURL}/?auth_token=${sessionStorage.getItem('auth_token')}`}
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            Monitoring
                        </a>
                    </Menu.Item>
                );
            }

            if (menuItem.menu_title === "Live Monitoring") {
                return (
                    <Menu.Item key="Monitoring" icon={icon}>
                        <a
                            href={`${process.env.REACT_APP_Live_Monitoring_URL}/?auth_token=${sessionStorage.getItem('auth_token')}`}
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            Live Monitoring
                        </a>
                    </Menu.Item>
                );
            }


            if (menuItem.children && menuItem.children.length > 0) {

                return (
                    <SubMenu
                        key={menuItem.navbar_id.toString()}
                        icon={icon}
                        title={menuItem.menu_title}
                    >
                        {renderMenuItems(menuItem.children)}

                    </SubMenu>
                );
            }
            return (
                <Menu.Item key={menuItem.route || menuItem.navbar_id.toString()} icon={icon}>
                    {menuItem.route ? (
                        <Link to={menuItem.route}>{menuItem.menu_title}</Link>
                    ) : (
                        menuItem.menu_title
                    )}
                </Menu.Item>
            );
        });
    };
    return (
        <>
            <Sider
                className="hideOnMobile"
                collapsed={isToggled}
                style={{ overflow: "auto", background: "white" }}
            >
                <div
                    className="logo hideOnMobile"
                    style={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                >
                    <Link to="/">
                        <img src={logo} alt="App logo" height={65} width={170} />
                    </Link>
                </div>
                <Menu
                    // mode="inline"
                    selectedKeys={[selectedKey]}
                    openKeys={openKeys}
                    onOpenChange={onOpenChange}
                >
                    {renderMenuItems(menuData)}
                </Menu>
            </Sider>
            <Drawer
                className="hideOnDesktop"
                title={false}
                closeIcon={false}
                placement="left"
                onClose={onClose}
                visible={isToggled}
            >
                <Menu
                    mode="inline"
                    selectedKeys={[selectedKey]}
                    openKeys={openKeys}
                    onOpenChange={onOpenChange}
                >
                    {renderMenuItems(menuData)}
                </Menu>
            </Drawer>
        </>
    );
};
export default MenuComponent;