import * as ActionTypes from "../Constants/CustomNumberConstants"
const initialState = {
    numbers: [],
    isLoading: false,
    errMess: ''
}

export const CustomNumberReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.CUSTOM_NUMBER_LOADING:
            return {...state, isLoading: true}
        case ActionTypes.CUSTOM_NUMBER_SUCCESS:
            return {...state, isLoading: false, numbers: action.payload}
        case ActionTypes.CUSTOM_NUMBER_FAILED:
            return {...state, isLoading: false, errMess: action.payload}
    }
}