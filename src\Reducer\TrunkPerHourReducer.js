const initialState = {
    data: [],
    isLoading: false,
    errMess: ''
}

export const TrunkPerHourReportReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case "TRUNK_HOUR_REPORT_LOADING":
            return { ...state, isLoading: true }
        case "TRUNK_HOUR_REPORT_SUCCESS":
            return { ...state, isLoading: false, data: action.payload, errMess: '' }
        case "TRUNK_HOUR_REPORT_FAILED":
            return { ...state, isLoading: false, errMess: action.payload }
        case "TRUNK_HOUR_REPORT_RESET":
            return { ...state, data: [] }
    }
}