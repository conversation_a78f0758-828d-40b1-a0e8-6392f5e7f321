import { useDispatch, useSelector } from "react-redux";
import { Button, DatePicker, Descriptions, Form, Input, Modal, Select, Space, Spin, Table } from "antd";
import React, { useState, useEffect } from 'react'
import { getQueues } from "../../Actions/QueueActions";
import { getAgent } from "../../Actions/AgentActions";
import {
    getRingNoAnswerAgentWiseSummaryReport,
    getRingNoAnswerAgentWiseSummaryReportFiltered
} from "../../Actions/AgentReportActions";
import apiClient from "../../Shared/apiClient";

const RingNoAnswerAgentSummaryReportFilter = ({ visible, setVisible, onCreate, isLoading }) => {
    const [form] = Form.useForm()
    // const QueueState = useSelector(state => state.QueueReducer)
    const agentState = useSelector(state => state.AgentReducer)
    const [showQueue, setShowQueue] = useState(false)
    const dispatch = useDispatch()
    const [queues, setQueues] = useState([])
    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data))
    }, [])
    useEffect(() => {
        if (visible) {
            dispatch(getAgent())
            setShowQueue(true)
        }
    }, [visible])

    return (
        <Spin spinning={agentState.isLoading || isLoading}>
            <Modal
                visible={visible}
                onCancel={() => {
                    form.resetFields()
                    setVisible(false)
                }}
                onOk={() => {
                    form
                        .validateFields()
                        .then((values) => {
                            onCreate(values);
                            form.resetFields()
                        })
                        .catch((info) => {
                            console.log('Validate Failed:', info);
                        })
                }}
            >
                <Form
                    form={form}
                    layout="vertical"
                    name="form_in_modal"
                >
                    <Form.Item
                        name="date"
                        label="date"
                    >
                        <DatePicker.RangePicker />
                    </Form.Item>

                    <Form.Item name="agent" label="Agent">
                        <Select mode="multiple">
                            {agentState.users && agentState.users.map((value, index) => <Select.Option value={value.name} key={value.id}>
                                {value.name}
                            </Select.Option>)}
                        </Select>
                    </Form.Item>
                    <Form.Item colon={false} name="queue" label="Queue" >
                        <Select placeholder="Select Queue">
                            {queues && queues.map((queue, index) => <Select.Option key={index} value={queue.name}>
                                {queue.name}
                            </Select.Option>)}
                        </Select>
                    </Form.Item>
                </Form>
            </Modal>
        </Spin>
    );
}

export default RingNoAnswerAgentSummaryReportFilter