import * as ActionTypes from "../Constants/CustomNumberConstants"
import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";
import { getCampaignNumbers } from "./CampaignNumberActions";

const endpoint = 'customNumber'

export const getCustomNumbers = campaign => dispatch => {
    dispatch(customNumbersLoading())
    apiClient.get(`/api/${campaign}/${endpoint}`).then(r => dispatch(customNumbersSuccess(r.data))).catch(e => dispatch(customNumbersFailed(handleError(e))))
}

export const postCustomNumbers = (campaign, data) => dispatch => {
    dispatch(customNumbersLoading())
    apiClient.post(`/api/${campaign}/${endpoint}`, data).then(r => dispatch(customNumbersSuccess(r.data))).then(() => dispatch(getCustomNumbers(campaign))).catch(e => dispatch(customNumbersFailed(handleError(e)))).catch(e => dispatch(customNumbersFailed(handleError(e))))
}

export const patchCustomNumbers = (campaign, customNumber, data) => dispatch => {
    dispatch(customNumbersLoading())
    apiClient.patch(`/api/${campaign}/${endpoint}/${customNumber}`, data)
        .then(() => dispatch(getCustomNumbers(campaign)))
        .catch(e => dispatch(customNumbersFailed(handleError(e))))
}

export const deleteCustomNumbers = (campaign, customNumber) => dispatch => {
    dispatch(customNumbersLoading())
    apiClient.delete(`/api/${campaign}/${endpoint}/${customNumber}`).then(r => dispatch(customNumbersSuccess(r.data))).then(() => dispatch(getCampaignNumbers(campaign))).catch(e => dispatch(customNumbersFailed(handleError(e)))).catch(e => dispatch(customNumbersFailed(handleError(e))))
}

const customNumbersLoading = () => ({
    type: ActionTypes.CUSTOM_NUMBER_LOADING
})

const customNumbersSuccess = numbers => ({
    type: ActionTypes.CUSTOM_NUMBER_SUCCESS,
    payload: numbers
})

const customNumbersFailed = err => ({
    type: ActionTypes.CUSTOM_NUMBER_FAILED,
    payload: err
})


