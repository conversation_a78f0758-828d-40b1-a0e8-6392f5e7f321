import React, { useState, useEffect } from 'react';
import { Button, Card, Form, Input, Table, Modal, Popconfirm, notification, Radio } from "antd";

// Helper function to display a notification
const openNotificationWithIcon = (type, message) => {
    notification[type]({
        message: message,
    });
};

export default function Announcements() {
    const [selectedFile, setSelectedFile] = useState(null);
    const [editSelectedFile, setEditSelectedFile] = useState(null);
    const [form] = Form.useForm();
    const [editForm] = Form.useForm();
    const [announcements, setAnnouncements] = useState([]);
    const [isEditModalVisible, setIsEditModalVisible] = useState(false);
    const [isAddModalVisible, setIsAddModalVisible] = useState(false);
    const [editingRecord, setEditingRecord] = useState(null);
    const [selectedRowId, setSelectedRowId] = useState(null);

    // Get base URL from environment variable
    const API_BASE_URL = process.env.REACT_APP_baseURL;

    const handleFileChange = (event) => {
        const file = event.target.files[0];
        if (file && file.type !== 'audio/wav' && file.type !== 'audio/x-wav') {
            alert('Please upload a valid .wav file.');
            return;
        }
        setSelectedFile(file);
    };

    const handleEditFileChange = (event) => {
        const file = event.target.files[0];
        if (file && file.type !== 'audio/wav' && file.type !== 'audio/x-wav') {
            alert('Please upload a valid .wav file.');
            return;
        }
        setEditSelectedFile(file);
    };

    const onFinish = async (values) => {
        const formData = new FormData();
        formData.append('name', values.announcementName);
        formData.append('queue', values.queue);
        formData.append('time_interval', values.timeInterval);
        formData.append('media', selectedFile);
    
        try {
            const response = await fetch(
                `${API_BASE_URL}/api/announcements/submit`,
                {
                    method: 'POST',
                    body: formData,
                    headers: {
                        Authorization: `Bearer ${sessionStorage.getItem('auth_token')}`,
                    },
                    redirect: 'manual',
                }
            );
    
            const result = await response.json();
    
            if (response.ok) {
                openNotificationWithIcon('success', result.message);
                setIsAddModalVisible(false);
                form.resetFields();
                setSelectedFile(null);
                fetchAnnouncements(); // Refresh the list
            } else if (result.errors) {
                Object.keys(result.errors).forEach(key => {
                    openNotificationWithIcon('error', result.errors[key][0]);
                });
            } else {
                openNotificationWithIcon('error', result.message);
            }
        } catch (error) {
            console.error('Error uploading data:', error);
            openNotificationWithIcon('error', "Network error. Please try again.");
        }
    };

    const onFinishFailed = (errorInfo) => {
        console.log('Failed:', errorInfo);
    };

    const fetchAnnouncements = async () => {
        try {
            const response = await fetch(
                `${API_BASE_URL}/api/announcement-list`,
                {
                    headers: {
                        Authorization: `Bearer ${sessionStorage.getItem('auth_token')}`,
                    },
                }
            );
            if (response.ok) {
                const data = await response.json();
                setAnnouncements(data.data);
            }
        } catch (error) {
            console.error('Error fetching announcements:', error);
        }
    };

    useEffect(() => {
        fetchAnnouncements();
    }, []);

    const deleteAnnouncement = async (id) => {
        if (!selectedRowId) {
            openNotificationWithIcon('error', "Please select an announcement first");
            return;
        }

        try {
            const response = await fetch(
                `${API_BASE_URL}/api/announcement/${id}`,
                {
                    method: 'DELETE',
                    headers: {
                        Authorization: `Bearer ${sessionStorage.getItem('auth_token')}`,
                    },
                }
            );
            if (!response.ok) {
                throw new Error('Failed to delete announcement');
            }
            const result = await response.json();
            console.log('Delete response:', result);
            if (result.message === "Announcement has been deleted") {
                setAnnouncements(prev => prev.filter(item => item.id !== id));
                setSelectedRowId(null);
                openNotificationWithIcon('success', "Successfully Deleted Announcement");
            }
        } catch (error) {
            console.error('Error deleting announcement:', error);
        }
    };

  const handleEditFinish = async (values) => {
    const formData = new FormData();
    formData.append('id', editingRecord.id);
    formData.append('name', values.name);
    formData.append('queue', values.queue);
    formData.append('time_interval', values.time_interval);
    if (editSelectedFile) {
        formData.append('media', editSelectedFile);
    }

    try {
        const response = await fetch(
            `${API_BASE_URL}/api/announcements/submit`,
            {
                method: 'POST',
                body: formData,
                headers: {
                    'Authorization': `Bearer ${sessionStorage.getItem('auth_token')}`,
                    'Accept': 'application/json', // Ensure we expect JSON response
                },
                credentials: 'include', // Handle cookies if needed
            }
        );

        // First check if the response was successful
        if (response.ok) {
            const result = await response.json();
            console.log('Response from API:', result);
            
            if (result.data) {
                result.data.id = editingRecord.id;
                setAnnouncements(prev =>
                    prev.map(item => (item.id === editingRecord.id ? result.data : item))
                );
                openNotificationWithIcon('success', result.message || "Successfully Edited Announcement");
            } else {
                openNotificationWithIcon('success', result.message || "Announcement updated successfully");
                fetchAnnouncements();
            }
        } else {
            // Handle non-successful responses
            const errorData = await response.json().catch(() => null);
            
            if (errorData && errorData.errors) {
                // Handle validation errors
                Object.keys(errorData.errors).forEach(key => {
                    openNotificationWithIcon('error', errorData.errors[key][0]);
                });
            } else if (errorData && errorData.message) {
                // Handle other API errors
                openNotificationWithIcon('error', errorData.message);
            } else {
                openNotificationWithIcon('error', `Failed to update announcement: ${response.statusText}`);
            }
        }
    } catch (error) {
        console.error('Error updating announcement:', error);
        openNotificationWithIcon('error', "Network error. Please try again.");
    } finally {
        setIsEditModalVisible(false);
        setEditingRecord(null);
        setEditSelectedFile(null);
        setSelectedRowId(null);
    }
};

    const columns = [
        {
            title: '',
            dataIndex: 'id',
            key: 'select',
            render: (id) => (
                <Radio
                    checked={selectedRowId === id}
                    onChange={() => setSelectedRowId(id)}
                />
            ),
            width: 80,
        },
        { title: 'ID', dataIndex: 'id', key: 'id' },
        { title: 'Name', dataIndex: 'name', key: 'name' },
        { title: 'Queue', dataIndex: 'queue', key: 'queue' },
        { title: 'Time Interval (5 sec)', dataIndex: 'time_interval', key: 'time_interval' },
        { title: 'File Path', dataIndex: 'file_path', key: 'file_path' },
    ];

    const handleEditClick = () => {
        if (!selectedRowId) {
            openNotificationWithIcon('error', "Please select an announcement first");
            return;
        }

        const record = announcements.find(item => item.id === selectedRowId);
        if (record) {
            setEditingRecord(record);
            editForm.setFieldsValue({
                name: record.name,
                queue: record.queue,
                time_interval: record.time_interval,
            });
            setIsEditModalVisible(true);
        }
    };

    const handleDeleteClick = () => {
        if (!selectedRowId) {
            openNotificationWithIcon('error', "Please select an announcement first");
            return;
        }
        deleteAnnouncement(selectedRowId);
    };

    return (
        <div style={{ padding: '20px' }}>
            {/* Header with Add button */}
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '20px' }}>
                <Button
                    type="primary"
                    onClick={() => setIsAddModalVisible(true)}
                    style={{ marginBottom: '20px' }}
                >
                    Add Announcement
                </Button>

                {/* Action buttons */}
                <div style={{ marginBottom: '20px', display: 'flex', gap: '10px' }}>
                    <Button
                        type="primary"
                        onClick={handleEditClick}
                        disabled={!selectedRowId}
                    >
                        Edit
                    </Button>
                    <Button
                        type="primary"
                        danger
                        onClick={handleDeleteClick}
                        disabled={!selectedRowId}
                    >
                        Delete
                    </Button>
                </div>
            </div>

            {/* Announcements table */}
            <Table
                dataSource={announcements}
                columns={columns}
                rowKey="id"
                pagination={{ pageSize: 5 }}
                scroll={{ x: 800 }}
            />

            {/* Add Announcement Modal */}
            <Modal
                title="Add New Announcement"
                visible={isAddModalVisible}
                onCancel={() => {
                    setIsAddModalVisible(false);
                    form.resetFields();
                    setSelectedFile(null);
                }}
                footer={null}
            >
                <Form
                    form={form}
                    name="announcementsForm"
                    autoComplete="off"
                    initialValues={{ announcementName: "", queue: "", timeInterval: 0 }}
                    onFinish={onFinish}
                    onFinishFailed={onFinishFailed}
                    layout="vertical"
                >
                    <Form.Item
                        label="Announcement Name"
                        name="announcementName"
                        rules={[{ required: true, message: 'Please input your Announcement Name!' }]}
                    >
                        <Input />
                    </Form.Item>

                    <Form.Item
                        label="Queue"
                        name="queue"
                        rules={[{ required: true, message: 'Please input your Queue!' }]}
                    >
                        <Input />
                    </Form.Item>

                    <Form.Item
                        label="Time Interval (5 sec)"
                        name="timeInterval"
                        rules={[{ required: true, message: 'Please input the Time Interval!' }]}
                    >
                        <Input type="number" />
                    </Form.Item>

                    <Form.Item
                        label="Upload Media"
                        name="media"
                        rules={[{ required: true, message: 'Please upload a WAV file.' }]}
                    >
                        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'start' }}>
                            <Input type="file" accept=".wav" onChange={handleFileChange} />
                            {selectedFile && (
                                <span style={{ marginTop: '5px' }}>
                                    Selected File: {selectedFile.name}
                                </span>
                            )}
                        </div>
                    </Form.Item>

                    <Form.Item>
                        <Button type="primary" htmlType="submit">
                            Submit
                        </Button>
                    </Form.Item>
                </Form>
            </Modal>

            {/* Edit Announcement Modal */}
            <Modal
                title="Edit Announcement"
                visible={isEditModalVisible}
                onCancel={() => {
                    setIsEditModalVisible(false);
                    setEditingRecord(null);
                    setEditSelectedFile(null);
                }}
                footer={null}
            >
                <Form form={editForm} onFinish={handleEditFinish} layout="vertical">
                    <Form.Item
                        label="Name"
                        name="name"
                        rules={[{ required: true, message: 'Please input the name' }]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label="Queue"
                        name="queue"
                        rules={[{ required: true, message: 'Please input the queue' }]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label="Time Interval"
                        name="time_interval"
                        rules={[{ required: true, message: 'Please input the time interval' }]}
                    >
                        <Input type="number" />
                    </Form.Item>
                    <Form.Item
                        label="Upload Media (optional)"
                        name="media"
                    >
                        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'start' }}>
                            <Input type="file" accept=".wav" onChange={handleEditFileChange} />
                            {editSelectedFile ? (
                                <span style={{ marginTop: '5px' }}>
                                    Selected File: {editSelectedFile.name}
                                </span>
                            ) : (
                                editingRecord &&
                                editingRecord.file_path && (
                                    <span style={{ marginTop: '5px' }}>
                                        Current File: {editingRecord.file_path.split('/').pop()}
                                    </span>
                                )
                            )}
                        </div>
                    </Form.Item>
                    <Form.Item>
                        <Button type="primary" htmlType="submit">
                            Save
                        </Button>
                    </Form.Item>
                </Form>
            </Modal>
        </div>
    );
}
