import * as ActionTypes from "../Constants/CampaignNumberConstants"

const initial = {
    numbers: [],
    message: false,
    errMess: false,
    isLoading: false,
}

export const CampaignNumberReducer = (state = initial, action) => {
    switch (action.type) {
        case ActionTypes.NUMBERS_LOADING:
            return {...state, isLoading: true, message: false, errMess: false }
        case ActionTypes.ALL_NUMBERS:
            return { ...state, isLoading: false, errMess: false, message: false, numbers: action.payload }
        case ActionTypes.NUMBERS_SUCCESS:
            return { ...state, isLoading: false, errMess: false, message: action.payload }
        case ActionTypes.NUMBERS_FAILED:
            return { ...state, isLoading: false, errMess: action.payload, message: false }
        default:
            return state
    }
}

