import { <PERSON><PERSON>, Card, Checkbox, Form, Input } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { getCIDLookUp, saveCIDLookUp, updateCIDLookUp } from "../Actions/CIDLoopUpActions";
import { useEffect } from "react";
import { openNotificationWithIcon } from "../Shared/notification";

export const CIDLookUp = () => {

    const cidState = useSelector(state => state.CIDLookUpReducer)
    const [form] = Form.useForm()
    const dispatch = useDispatch()

    useEffect(() => dispatch(getCIDLookUp()), [])
    useEffect(() => form.setFieldsValue(cidState?.data[0]), [cidState])
    useEffect(() => {
        form.setFieldsValue(cidState?.data[0])
        dispatch(getCIDLookUp())
        if (cidState.message) openNotificationWithIcon('success', cidState.message)
    }, [cidState.message])
    // 

    const onFinish = value => {
        cidState?.data[0] ? dispatch(updateCIDLookUp(cidState?.data[0].id, value)) : dispatch(saveCIDLookUp(value))
    }

    const onFinishFailed = (errorInfo) => {
        console.log('Failed:', errorInfo);
    };

    return (<>
        <Card>
            <Form
                form={form}
                name="basic"
                labelCol={{ span: 2 }}
                wrapperCol={{ span: 16 }}
                initialValues={{ remember: true }}
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
                autoComplete="off"
            >
                <Form.Item
                    label="API URL"
                    name="url"
                    rules={[{ required: true, message: 'Please input your API URL!' }]}
                >
                    <Input type="url" />
                </Form.Item>

                <Form.Item
                    label="JSON Key"
                    name="key"
                    rules={[{ required: true, message: 'Please enter (JSON key)!' }]}
                >
                    <Input />
                </Form.Item>

                <Form.Item name="required" valuePropName="checked" label='Enable' wrapperCol={{ offset: 0, span: 16 }}>
                    <Checkbox />
                </Form.Item>

                <Form.Item wrapperCol={{ offset: 2, span: 16 }}>
                    <Button type="primary" htmlType="submit">
                        {cidState?.data[0] ? 'Submit' : 'Update'}
                    </Button>
                </Form.Item>
            </Form>
        </Card>
    </>)
}