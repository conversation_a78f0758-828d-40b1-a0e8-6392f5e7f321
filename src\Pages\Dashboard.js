import React, { useEffect, useState } from 'react'
import { Card, Col, Row, Space, Typography , message } from "antd";
// import Stats from "../Components/Dashboard/Stats";
import { MdSupportAgent, MdCampaign } from 'react-icons/md'
import { BiUserCircle, BiAddToQueue } from 'react-icons/bi';
import { AiOutlineCoffee } from 'react-icons/ai'
import { FaWpforms } from 'react-icons/fa'
import { FiActivity } from 'react-icons/fi'
import LineChart from "../Components/Charts/LineChart";
import apiClient from '../Shared/apiClient'
import { useDispatch } from 'react-redux';
// import { errorAlert } from '../Actions/AlertActions';
import { MdPayment, MdAccountBalanceWallet } from 'react-icons/md'
const { Text } = Typography;

const TopWidgetStyle = {
    textAlign: 'center',
    padding: '0 15px',
    boxShadow: 'rgba(149, 157, 165, 0.2) 0px 8px 24px',
}

const TopWidgetCircle = {
    height: '70px',
    width: '70px',
    borderRadius: '100%',
    backgroundColor: '#fff',
    boxShadow: 'rgba(149, 157, 165, 0.2) 0px 8px 24px',
    color: "#15347c",
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 'auto',
    fontSize: '2rem',
}

const smallWidget = {
    height: '110px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRight: '1px solid #f0f0f0',
}

const smallWidgetLast = {
    height: '110px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
}
const smallWidgetIcon = {
    height: '70px',
    width: '70px',
    fontSize: '1.5rem',
    color: "#15347c",
    borderRadius: '100%',
    boxShadow: 'rgba(149, 157, 165, 0.2) 0px 8px 24px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
}

const smallWidgetText = {
    paddingLeft: '15px',
    fontWeight: 'bold'
}



const Dashboard = () => {
    const [widgetsData, setWidgetsData] = useState()
    const [data, setData] = useState()
    const [inboundCallSummary, setInboundCallSummary] = useState()
    const [outboundCallSummary, setOutboundCallSummary] = useState()

    const role = sessionStorage.getItem('role');

    const dispatch = useDispatch()
    useEffect(() => {
        apiClient.get('api/getStats').then(res => {
            if (res.data) {

                setData(res.data)
            }
        }).catch(err => {
            if (err.response) {
                // dispatch(errorAlert(err.response?.data?.message))
            }
        })
        // Check available minutes
      
       
    }, [])


    useEffect(() => {
        if (data) {
            setWidgetsData(data.widgets_data)
            setInboundCallSummary(data?.inbound_call_summary)
            setOutboundCallSummary(data?.outbound_call_summary)
        }
    }, [data])


    return (
        <Space size={"large"} style={{ width: "100%" }} direction='vertical'>

            {(role === "superadministrator" || role === "admin") ? (
                <>
                
                    {/* <Stats /> */}
                    <Row gutter={[25, 20]}>
                        <Col xs={24} sm={12} md={12} lg={12} xl={6}>
                            <Card style={TopWidgetStyle}>
                                <div style={TopWidgetCircle}>
                                    <MdSupportAgent />
                                </div>
                                <div style={{ fontWeight: 'bolder', fontSize: '18px', marginTop: '25px' }}>
                                    <p style={{ float: 'left' }}>Total Agents</p>
                                    <p style={{ float: 'right' }}>{widgetsData?.total_agents || 0}</p>
                                </div>
                            </Card>
                        </Col>

                        <Col xs={24} sm={12} md={12} lg={12} xl={6}>
                            <Card style={TopWidgetStyle}>
                                <div style={TopWidgetCircle}>
                                    <BiUserCircle />
                                </div>
                                <div style={{ fontWeight: 'bolder', fontSize: '18px', marginTop: '25px' }}>
                                    <p style={{ float: 'left' }}>Total Users</p>
                                    <p style={{ float: 'right' }}>{widgetsData?.total_users || 0}</p>
                                </div>
                            </Card>
                        </Col>

                        <Col xs={24} sm={12} md={12} lg={12} xl={6}>
                            <Card style={TopWidgetStyle}>
                                <div style={TopWidgetCircle}>
                                    <FaWpforms />
                                </div>
                                <div style={{ fontWeight: 'bolder', fontSize: '18px', marginTop: '25px' }}>
                                    <p style={{ float: 'left' }}>Total Forms</p>
                                    <p style={{ float: 'right' }}>{widgetsData?.total_forms || 0}</p>
                                </div>
                            </Card>
                        </Col>

                        <Col xs={24} sm={12} md={12} lg={12} xl={6}>
                            <Card style={TopWidgetStyle}>
                                <div style={TopWidgetCircle}>
                                    <MdCampaign />
                                </div>
                                <div style={{ fontWeight: 'bolder', fontSize: '18px', marginTop: '25px' }}>
                                    <p style={{ float: 'left' }}>Total Campaigns</p>
                                    <p style={{ float: 'right' }}>{widgetsData?.total_campaigns || 0}</p>
                                </div>
                            </Card>
                        </Col>
                    </Row>

                    {/* <Row gutter={[25, 20]}>
                        <Col xs={24} sm={12} md={12} lg={6}>
                            <Card style={TopWidgetStyle}>
                                <div style={TopWidgetCircle}>
                                    <MdPayment />
                                </div>
                                <div style={{ fontWeight: 'bolder', fontSize: '18px', marginTop: '25px' }}>
                                    <p style={{ float: 'left' }}>Prepaid Amount</p>
                                    <p style={{ float: 'right' }}>PKR {widgetsData?.prepaid?.amount || 0}</p>
                                </div>
                            </Card>
                        </Col>

                        <Col xs={24} sm={12} md={12} lg={6}>
                            <Card style={TopWidgetStyle}>
                                <div style={TopWidgetCircle}>
                                    <MdAccountBalanceWallet />
                                </div>
                                <div style={{ fontWeight: 'bolder', fontSize: '18px', marginTop: '25px' }}>
                                    <p style={{ float: 'left' }}>Remaining Amount</p>
                                    <p style={{ float: 'right' }}>PKR {widgetsData?.prepaid?.remaining_amount || 0}</p>
                                </div>
                            </Card>
                        </Col>
                    </Row> */}

                    <Row gutter={[25, 20]}>
                        <Col span={24}>
                            <Card style={{ boxShadow: 'rgba(149, 157, 165, 0.2) 0px 8px 24px' }}>
                                <Row>
                                    <Col xs={12} sm={12} md={12} lg={8}>
                                        <div style={smallWidget}>
                                            <div style={smallWidgetIcon}>
                                                <BiAddToQueue />
                                            </div>
                                            <div style={smallWidgetText}>
                                                <Text>
                                                    {widgetsData?.total_queues || 0}<br />
                                                    Total Queues
                                                </Text>
                                            </div>
                                        </div>
                                    </Col>
                                    <Col xs={12} sm={12} md={12} lg={8}>
                                        <div style={smallWidget}>
                                            <div style={smallWidgetIcon}>
                                                <FiActivity />
                                            </div>
                                            <div style={smallWidgetText}>
                                                <Text>
                                                    {widgetsData?.outbound_activity || 0}<br />
                                                    Outbound Activity
                                                </Text>
                                            </div>
                                        </div>
                                    </Col>
                                    <Col xs={12} sm={12} md={12} lg={8}>
                                        <div style={smallWidgetLast}>
                                            <div style={smallWidgetIcon}>
                                                <AiOutlineCoffee />
                                            </div>
                                            <div style={smallWidgetText}>
                                                <Text>
                                                    {widgetsData?.total_break || 0}<br />
                                                    Break total
                                                </Text>
                                            </div>
                                        </div>
                                    </Col>
                                </Row>
                            </Card>
                        </Col>
                    </Row>

                    <Row gutter={[25, 20]}>
                        <Col xs={24} sm={24} md={24} lg={12}>
                            <Card style={TopWidgetStyle}>
                                <LineChart chartData={inboundCallSummary} title="Inbound" />
                            </Card>
                        </Col>
                        <Col xs={24} sm={24} md={24} lg={12}>
                            <Card style={TopWidgetStyle}>
                                <LineChart chartData={outboundCallSummary} title="Outbound" />
                            </Card>
                        </Col>
                    </Row>

                </>
            ) : null}
        </Space>
    )
}


export default Dashboard

// import Stats from "../Components/Dashboard/Stats";

// const Dashboard = () => {

//     return(
//         <>
//             <Stats />
//         </>
//     )
// }

// export default Dashboard