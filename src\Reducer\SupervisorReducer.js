

const initialState = {
    supervisorData: [],
    errMess: null,
    isLoading: false,
    message: ''
}

export const SupervisorReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case "supervoiserLoading":
            return {...state, isLoading: true}
        case "getDataByIDSupervisor":
            return {...state, isLoading: false, supervisorData: action.payload, errMess: null, message: ""}
        case "supervisorSuccess":
            return {...state, isLoading: false, users: action.payload, errMess: null, message: ''}
        case "supervisorFailed":
            return {...state, isLoading: false,message: '', errMess: action.payload}
        case "supervisorCreateSuccess":
            return {...state, isLoading: false, message: action.payload, errMess: null}
        case "supervisorUpdateSuccess":
            return {...state, isLoading: false, message: action.payload, errMess: null}
        // case "supervisorDeleteSuccess":
        //     return {...state, isLoading: false, message: action.payload, errMess: null}
        case "supervisorDeleteSuccess":
            return {
                ...state, 
                isLoading: false, 
                message: action.payload.message, 
                errMess: null,
                users: state.users.filter(user => user.id !== action.payload.id)
            }
    }
}