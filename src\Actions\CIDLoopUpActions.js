import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";


export const getCIDLookUp = () => dispatch => {
    dispatch(cidLoading())
    apiClient('api/cIDLoopUp').then(r => dispatch(getCID(r.data))).catch(e => dispatch(cidFailed(handleError(e)))).catch(e => dispatch(cidFailed(handleError(e))))
}

export const saveCIDLookUp = data => dispatch => {
    dispatch(cidLoading())
    apiClient.post('api/cIDLoopUp', data).then(r => dispatch(cidSuccess(r.data))).catch(e => dispatch(cidFailed(handleError(e)))).catch(e => dispatch(cidFailed(handleError(e))))
}

export const updateCIDLookUp = (id, data) => dispatch => {
    dispatch(cidLoading())
    apiClient.put(`api/cIDLoopUp/${id}`, data).then(r => dispatch(cidSuccess(r.data))).catch(e => dispatch(cidFailed(handleError(e)))).catch(e => dispatch(cidFailed(handleError(e))))
}

const cidLoading = () => ({
    type: "CID_LOADING"
})

const getCID = data => ({
    type: "GET_CID",
    payload: data
})

const cidSuccess = data => ({
    type: "CID_SUCCESS",
    payload: data
})

const cidFailed = error => ({
    type: "CID_FAILED",
    payload: error
})