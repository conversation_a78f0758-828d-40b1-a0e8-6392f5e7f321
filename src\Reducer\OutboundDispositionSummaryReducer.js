import * as ActionTypes from "../Constants/OutboundDispositionSummaryConstants"
const initialState = {
    data: [],
    isLoading: false,
    errMess: ''
}

export const OutboundDispositionSummaryReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.OUTBOUND_SUMMARY_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.OUTBOUND_SUMMARY_SUCCESS:
            return { ...state, isLoading: false, data: action.payload }
        case ActionTypes.OUTBOUND_SUMMARY_FAILED:
            return { ...state, isLoading: false, errMess: action.payload }
        case ActionTypes.OUTBOUND_SUMMARY_RESET:
            return { ...state, data: [] }
    }
}