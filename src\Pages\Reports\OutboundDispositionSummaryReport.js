import { Button, Form, Input, Modal, Select, Space, Table, Typography, DatePicker, Spin } from "antd";
import React, { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { OutboundDispositionSummaryReset, getOutboundDispositionSummary, getOutboundDispositionSummaryFilter } from "../../Actions/OutboundDispositionSummaryActions";
import { DownloadOutlined, FileAddOutlined, FilterOutlined, SearchOutlined, SettingOutlined, ReloadOutlined } from "@ant-design/icons";
import { CSVDownload, CSVLink } from "react-csv";
import Highlighter from "react-highlight-words";
import { getAgent } from "../../Actions/AgentActions";
import moment from "moment";

export const OutboundDispositionSummaryReport = () => {

    const dispatch = useDispatch()
    const outboundSummaryState = useSelector(state => state.OutboundDispositionSummaryReducer)
    const [filterVisible, setFilterVisible] = useState(false)
    const [resetFilter, setResetFilter] = useState(false)


    // useEffect(() => dispatch(getOutboundDispositionSummary()), [])

    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const searchInput = useRef()
    const [form] = Form.useForm()
    const resetFormFilter = () => {
        dispatch(OutboundDispositionSummaryReset())
        form.resetFields()
        setResetFilter(true)

    }

    const getColumnSearchProps = dataIndex => ({

        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        form.resetFields();
        setSearchText('')
    }

    const columns = [
        {
            title: 'S#',
            dataIndex: 'id',
            key: 'id'
        },
        {
            title: 'Call Status',
            dataIndex: 'call_status',
            key: 'call_status',
            ...getColumnSearchProps('call_status')
        },
        {
            title: 'Count',
            dataIndex: 'count',
            key: 'count',
            ...getColumnSearchProps('count')
        }

    ]

    return (<>
        <Table
            title={data => <>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    Outbound Disposition Summary
                    <Space>
                        <Button danger type="primary" onClick={() => resetFormFilter()} icon={<ReloadOutlined />}>Reset Filter</Button>
                        <Button onClick={() => setFilterVisible(true)} icon={<FilterOutlined />}>Filter</Button>
                        <CSVLink data={data} filename="OutboundDispositionSummaryReport.csv">
                            <Button icon={<DownloadOutlined />} type="primary" disabled={outboundSummaryState.data.length == 0}>
                                Download
                            </Button>
                        </CSVLink>
                    </Space>
                </div>
            </>}
            summary={data => {
                let total = 0
                data.forEach(({ call_status, count }) => total += count)
                return (
                    <>
                        <Table.Summary.Row>
                            <Table.Summary.Cell colSpan={2}>Total</Table.Summary.Cell>
                            <Table.Summary.Cell>
                                <Typography.Text strong>{total}</Typography.Text>
                            </Table.Summary.Cell>
                        </Table.Summary.Row>
                    </>
                )
            }} bordered loading={{ spinning: outboundSummaryState.isLoading, indicator: <Spin /> }}
            dataSource={outboundSummaryState.data}
            columns={columns}>This is outbound disposition report.
        </Table>
        <OutboundDispositionFilter form={form} buttonLoading={outboundSummaryState.isLoading} resetField={resetFilter} visible={filterVisible} setVisible={setFilterVisible} />
    </>
    )
}

const OutboundDispositionFilter = ({ form, visible, setVisible, resetField, buttonLoading }) => {
    // const [form] = Form.useForm()
    const dispatch = useDispatch()
    const agentState = useSelector(state => state.AgentReducer)

    useEffect(() => form.resetFields(), [resetField])

    useEffect(() => {

        if (visible == true) {

            dispatch(getAgent())
        }

    }, [visible])
    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            title="Outbound disposition summary filter"
            size="small"
            onOk={() =>
                form
                    .validateFields()
                    .then((data) => {
                        if (data.date && data.date.length === 2) {
                            const convertedDates = data.date.map((dateItem) =>
                                moment(dateItem).set({ hour: 19, minute: 0, second: 0, millisecond: 363 }).toISOString()
                            );
                            data.date = convertedDates;
                        }
                        dispatch(getOutboundDispositionSummaryFilter(data));
                        setVisible(false);
                        form.resetFields();
                    })
                    .catch((e) => console.log(e))
            }
            okText="Submit"
            oKButtonProps={{
                loading: buttonLoading
            }}
        >
            <Form
                layout="vertical"
                form={form}
            >
                <Form.Item name="date" label="Date Range">
                    <DatePicker.RangePicker />
                </Form.Item>
                <Form.Item name="agent" label="Agent" style={{ width: '55%' }}>
                    <Select
                    >
                        {agentState.users && agentState.users.map((value, index) => <Select.Option value={value.auth_username} key={value.id}>
                            {value.name}
                        </Select.Option>)}
                    </Select>
                </Form.Item>
            </Form>
        </Modal>)
}