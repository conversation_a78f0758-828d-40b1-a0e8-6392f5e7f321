import * as ActionTypes from "../Constants/OutboundAgentSummaryConstants"
import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";

export const getOutboundSummaryReportColumns = () => dispatch => {
    dispatch(outboundSummaryLoading())
    apiClient.post(`/api/report/outbound-agent-summary-columns`).then(r => dispatch(outboundSummaryColumns(r.data))).catch(e => dispatch(outboundSummaryFailed(handleError(e))))
}

export const getFilteredOutboundAgentSummary = (obj) => dispatch => {
    dispatch(outboundSummaryLoading())
    apiClient.post('api/report/outbound-agent-summary/filter', obj).then((r) => {
        console.log("res", r.data)
        dispatch(outboundSummarySuccess(r.data))
    }
    ).catch(e => dispatch(outboundSummaryFailed(handleError(e))))
}

export const getOutboundSummaryReport = () => dispatch => {
    dispatch(outboundSummaryLoading())
    apiClient.post(`/api/report/outbound-agent-summary`).then(r => dispatch(outboundSummarySuccess(r.data))).catch(e => dispatch(outboundSummaryFailed(handleError(e))))
}
export const outboundSummaryAgentReset = () => dispatch => {
    dispatch(outboundAgentSummaryReset())
}

const outboundSummaryLoading = () => ({
    type: ActionTypes.OUTBOUND_AGENT_SUMMARY_LOADING
})

const outboundSummaryColumns = data => ({
    type: ActionTypes.OUTBOUND_AGENT_SUMMARY_COLUMNS,
    payload: data
})

const outboundSummarySuccess = data => ({
    type: ActionTypes.OUTBOUND_AGENT_SUMMARY_SUCCESS,
    payload: data
})

const outboundSummaryFailed = err => ({
    type: ActionTypes.OUTBOUND_AGENT_SUMMARY_FAILED,
    payload: err
})
const outboundAgentSummaryReset = () => ({
    type: ActionTypes.OUTBOUND_AGENT_SUMMARY_RESET,

})