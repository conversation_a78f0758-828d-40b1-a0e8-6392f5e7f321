import {
    But<PERSON>,
    Card,
    DatePicker,
    Form,
    Input,
    Modal,
    Select,
    Space,
    Switch,
    Table,
    Upload,
} from "antd";
import {
    DownloadOutlined,
    ExportOutlined,
    SettingOutlined,
    UploadOutlined,
} from "@ant-design/icons";
import { useEffect, useState } from "react";
import apiClient from "../Shared/apiClient";
import moment from "moment";
import { openNotificationWithIcon } from "../Shared/notification";

export const VoiceMail = () => {
    const uploadURL = `${process.env.REACT_APP_baseURL}/api/uploadVoiceMailFile`;
    const streamURL = `${process.env.REACT_APP_baseURL}/api/voicemail`;
    const exportURL = `${process.env.REACT_APP_baseURL}/api/exportVoicemail`;

    const [form] = Form.useForm();
    const [modalVisible, setModalVisible] = useState(false);
    const [uploadModalVisible, setUploadModalVisible] = useState(false);
    const [voiceMailFiles, setVoiceMailFiles] = useState([]);
    const [voiceMails, setVoiceMails] = useState([]);
    const [loading, setLoading] = useState(false);
    const [query, setQuery] = useState({ date: [], number: "" });

    useEffect(() => {
        fetchSettings()
            .then((r) =>
                form.setFieldsValue({
                    voicemailFile: r.data.voicemail_file_id,
                    weeks: r.data.weeks,
                    except: r.data.except,
                    specificDates: r.data.specificDates,
                    specificDateStart: moment(r.data.specificDateStart, "HH:mm:ss"),
                    specificDateEnd: moment(r.data.specificDateEnd, "HH:mm:ss"),
                    start: moment(r.data.start, "HH:mm:ss"),
                    end: moment(r.data.end, "HH:mm:ss"),
                    status: r.data.status,
                })
            )
            .catch((e) => console.log(e));
    }, [form]);

    useEffect(() => {
        getFiles()
            .then((r) => setVoiceMailFiles(r.data))
            .catch((e) => console.log(e));
    }, [modalVisible]);

    const getVoicemails = () => {
        fetchVoicemails()
            .then((r) => setVoiceMails(r.data))
            .then((_) => setLoading(false))
            .catch((e) => console.log(e))
            .then((_) => setLoading(false));
    };

    useEffect(() => {
        setLoading(true);
        getVoicemails();
    }, []);

    const fetchVoicemails = (values = null) => {
        return apiClient.get("/api/voicemail", values);
    };

    const fetchSettings = () => {
        return apiClient.get("/api/getVoiceMailSettings");
    };

    const getFiles = () => {
        return apiClient.get("/api/getVoiceMailFiles");
    };

    const updateSettings = (values) => {
        return apiClient.patch("/api/updateVoiceMailSettings", values);
    };

    const exportVoicemail = (values) => {
        return apiClient.get("/api/exportVoicemail", values);
    };

    const getToken = () => {
        return sessionStorage.getItem("auth_token");
    };

    const columns = [
        {
            key: "id",
            dataIndex: "id",
            title: "#",
        },
        {
            key: "fileName",
            dataIndex: "fileName",
            title: "File Name",
            render: (text, record) => (
                <audio controls src={`${streamURL}/${record.id}`} />
            ),
        },
        {
            key: "number",
            dataIndex: "number",
            title: "Number",
        },
        {
            key: "uniqueid",
            dataIndex: "uniqueid",
            title: "Call ID",
        },
        {
            key: "created_at",
            dataIndex: "created_at",
            title: "Created At",
        },
        {
            key: "download",
            title: "Download",
            render: (_, record) => (
                <Button
                    type="primary"
                    icon={<DownloadOutlined />}
                    href={`${streamURL}/${record.id}`}
                />
            ),
        },
    ];

    const handleFileUpload = (file) => {
        setTimeout(() => {
            console.log("1001 file", file);
            openNotificationWithIcon(
                "success",
                `${file.name} uploaded successfully!`
            );
        }, 500);
    };

    const customRequest = ({ file, onSuccess, onError }) => {
        const formData = new FormData();
        console.log("kk", file)
        formData.append("file", file);

        // Assuming you have an API endpoint to handle the file upload
        // fetch(uploadURL, {
        //   method: "POST",
        //   headers: {
        //     Authorization: `Bearer ${getToken()}`,
        //   },
        //   body: formData,
        // })
        apiClient
            .post("/api/uploadVoiceMailFile", formData)
            .then((data) => {
                handleFileUpload(file); // Call the handleFileUpload function upon successful upload
                onSuccess(data, file);
            })
            .catch((error) => {
                console.error("Error uploading file:", error);
                onError(error);
            });
    };

    const handleExportClick = () => {
        setLoading(true);
        exportVoicemail({ params: { ...query }, responseType: "blob" })
            .then((r) => {
                const url = window.URL.createObjectURL(new Blob([r.data]));
                const link = document.createElement("a");
                link.href = url;
                link.setAttribute("download", "voicemails.xlsx");
                document.body.appendChild(link);
                link.click();
            })
            .then((_) => setLoading(false));
    };

    const handlePageChange = ({ current }) => {
        setLoading(true);
        fetchVoicemails({ params: { page: current } })
            .then((r) => setVoiceMails(r.data))
            .then((_) => setLoading(false))
            .catch((e) => console.log(e));
    };

    const handleSubmitForm = (values) => {
        setLoading(true);
        if (values.data) {
            values.date = [
                values["date"][0].format("YYYY-MM-DD"),
                values["date"][1].format("YYYY-MM-DD"),
            ];
        }
        setQuery(values);
        fetchVoicemails({ params: { ...values } })
            .then((r) => setVoiceMails(r.data))
            .then((_) => setLoading(false))
            .catch((e) => console.log(e));
    };

    return (
        <>
            <Card
                size="small"
                title="VoiceMails"
                extra={
                    <Space>
                        <Button
                            onClick={() => setModalVisible(true)}
                            icon={<SettingOutlined />}
                        />
                        <Button
                            onClick={() => setUploadModalVisible(true)}
                            icon={<UploadOutlined />}
                        />
                        <Button onClick={handleExportClick} icon={<ExportOutlined />} />
                    </Space>
                }
            >
                <Form
                    labelCol={{
                        span: 6,
                    }}
                    wrapperCol={{
                        span: 12,
                    }}
                    onFinish={handleSubmitForm}
                >
                    <Form.Item name="number" label="Number">
                        <Input />
                    </Form.Item>
                    <Form.Item name="date" label="Date Range">
                        <DatePicker.RangePicker />
                    </Form.Item>
                    <Form.Item
                        wrapperCol={{
                            offset: 6,
                            span: 12,
                        }}
                    >
                        <Button htmlType="submit" type="primary">
                            Submit
                        </Button>
                    </Form.Item>
                </Form>
                <Table
                    scroll={{ x: true }}
                    rowKey={(r) => r.id}
                    onChange={handlePageChange}
                    pagination={{
                        current: voiceMails.current_page,
                        pageSize: voiceMails.per_page,
                        total: voiceMails.total,
                    }}
                    loading={loading}
                    columns={columns}
                    dataSource={voiceMails.data}
                />
            </Card>

            <Modal
                title={
                    <>
                        <UploadOutlined /> VoiceMail File Upload
                    </>
                }
                visible={uploadModalVisible}
                onCancel={() => setUploadModalVisible(false)}
                onOk={() => {
                    getVoicemails();
                    setUploadModalVisible(false);
                }}
            >
                <Upload
                    name="file"
                    customRequest={customRequest}
                //   headers={{ Authorization: `Bearer ${getToken()}` }}
                //   action={uploadURL}
                >
                    <Button icon={<UploadOutlined />}>Click to upload</Button>
                </Upload>
            </Modal>

            <Modal
                title={
                    <>
                        <SettingOutlined /> VoiceMail Settings
                    </>
                }
                visible={modalVisible}
                okText="Save"
                onOk={() => {
                    form
                        .validateFields()
                        .then((values) => {
                            //console.log(values)
                            updateSettings(values)
                                .then((r) => {
                                    openNotificationWithIcon("success", r.data);
                                    setModalVisible(false);
                                })
                                .catch((e) =>
                                    openNotificationWithIcon("error", "Something went wrong")
                                );
                        })
                        .catch((e) => console.log(e));
                }}
                onCancel={() => setModalVisible(false)}
            >
                <Form form={form} layout="vertical" name="SettingsForm">
                    <Form.Item
                        name="voicemailFile"
                        label="Select file to play before recording voicemail"
                        rules={[
                            {
                                required: true,
                                message: "This field is required.",
                            },
                        ]}
                    >
                        <Select>
                            {voiceMailFiles &&
                                voiceMailFiles.map((r) => (
                                    <Select.Option key={r.id} value={r.id}>
                                        {r.name}
                                    </Select.Option>
                                ))}
                        </Select>
                    </Form.Item>
                    <Form.Item
                        name="weeks"
                        label="VoiceMail Weekdays"
                        rules={[
                            {
                                required: true,
                                message: "This field is required.",
                            },
                        ]}
                    >
                        <Select
                            mode="multiple"
                            allowClear
                            placeholder="Please Select Weekdays"
                        >
                            <Select.Option value="Monday">Monday</Select.Option>
                            <Select.Option value="Tuesday">Tuesday</Select.Option>
                            <Select.Option value="Wednesday">Wednesday</Select.Option>
                            <Select.Option value="Thursday">Thursday</Select.Option>
                            <Select.Option value="Friday">Friday</Select.Option>
                            <Select.Option value="Saturday">Saturday</Select.Option>
                            <Select.Option value="Sunday">Sunday</Select.Option>
                        </Select>
                    </Form.Item>
                    <Form.Item
                        name="except"
                        label="Except..."
                        extra="VoiceMail will be enabled whole day for the selected."
                    >
                        <Select allowClear mode="multiple">
                            <Select.Option value="Monday">Monday</Select.Option>
                            <Select.Option value="Tuesday">Tuesday</Select.Option>
                            <Select.Option value="Wednesday">Wednesday</Select.Option>
                            <Select.Option value="Thursday">Thursday</Select.Option>
                            <Select.Option value="Friday">Friday</Select.Option>
                            <Select.Option value="Saturday">Saturday</Select.Option>
                            <Select.Option value="Sunday">Sunday</Select.Option>
                        </Select>
                    </Form.Item>
                    <Form.Item
                        name="specificDates"
                        label="Or.. Add Specific Dates (format: yyyy-mm-dd)"
                    >
                        <Select mode="tags" allowClear multiple />
                    </Form.Item>
                    <Form.Item
                        name="specificDateStart"
                        label="Start Time for Specific Days Selected Above"
                    >
                        <DatePicker.TimePicker format="HH:mm:ss" />
                    </Form.Item>
                    <Form.Item
                        name="specificDateEnd"
                        label="End Time for Specific Days Selected Above"
                    >
                        <DatePicker.TimePicker format="HH:mm:ss" />
                    </Form.Item>
                    <Form.Item
                        name="start"
                        label="VoiceMail Start Time"
                        rules={[
                            {
                                required: true,
                                message: "This field is required.",
                            },
                        ]}
                    >
                        <DatePicker.TimePicker format="HH:mm:ss" />
                    </Form.Item>
                    <Form.Item
                        name="end"
                        label="VoiceMail End Time"
                        rules={[
                            {
                                required: true,
                                message: "This field is required.",
                            },
                        ]}
                    >
                        <DatePicker.TimePicker format="HH:mm:ss" />
                    </Form.Item>
                    <Form.Item
                        name="status"
                        label="Enable VoiceMail"
                        valuePropName="checked"
                        rules={[
                            {
                                required: true,
                                message: "This field is required.",
                            },
                        ]}
                    >
                        <Switch size="large" />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
};
