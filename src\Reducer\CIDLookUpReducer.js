
const initial = {
    data: [],
    errMess: null,
    isLoading: false,
    message: null
}

export const CIDLookUpReducer = (state = initial, action) => {
    switch (action.type) {
        case "GET_CID":
            return { ...state, isLoading: false, data: action.payload, errMess: null }
        case "CID_LOADING":
            return { ...state, isLoading: true }
        case "CID_SUCCESS":
            return { ...state, isLoading: false, message: action.payload, errMess: null }
        case "CID_FAILED":
            return { ...state, isLoading: false, errMess: action.payload, message: null }
        default:
            return state
    }
}