
const initState = {
    isLoading: false,
    errMess: null,
    message: null,
    data: []
}

export const SMSTemplateReducer = (state = initState, action) => {
    switch (action.type) {
        default:
            return { ...state }
        case "GET_TEMPLATE":
            return { ...state, isLoading: false, message: null, data: action.payload, errMess: null }
        case "MUTATE_TEMPLATE":
            return { ...state, isLoading: false, message: action.payload, errMess: null }
        case "LOADING_TEMPLATE":
            return { isLoading: true }
        case "FAILED_TEMPLATE":
            return { ...state, isLoading: false, errMess: action.payload, message: null }
    }
}