import { Button, Card, Form, Input, Typography } from 'antd'
import React, { useEffect, useState } from 'react'
import apiClient from '../Shared/apiClient'
import { openNotificationWithIcon } from '../Shared/notification'

export default function Ticker() {
    let [form] = Form.useForm()
    const [data, setData] = useState({})

    const fetchData = () => {
        apiClient.get('/api/dashboard/ticker').then((res) => {
            if (res.data) setData(res.data)
        })
    }

    useEffect(() => {
        fetchData()
    }, [])

    useEffect(() => {
        if (data) {
            form.setFieldsValue({ message: data.message })
        }
    }, [data])

    const handleSubmit = (values) => {
        let id = data.id

        apiClient.put(`/api/dashboard/ticker/${id}`, values).then((res) => {
            openNotificationWithIcon('success', res.data)
            fetchData()
        }).catch((err)=> openNotificationWithIcon('error',err.response?.data?.message))
    }


    return (
        <Card title={<Typography.Title level={4} style={{ margin: 0, color: '#fff' }}>Ticker</Typography.Title>}>
            <Form form={form} onFinish={handleSubmit}>
                <Form.Item name='message'>
                    <Input.TextArea
                        placeholder='Please Insert Your Ticker Message'
                    />
                </Form.Item>

                <Form.Item style={{ margin: '15px 0' }}>
                    <Button type='primary' htmlType='submit'>Save</Button>
                </Form.Item>
            </Form>
        </Card>
    )
}
