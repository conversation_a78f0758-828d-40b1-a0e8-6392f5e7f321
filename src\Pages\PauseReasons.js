import { Button, Form, Input, Modal, Row, Space, Spin, Table } from "antd";
import { DeleteOutlined, EditOutlined, PlusOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { useState, useEffect } from "react";
import { createPauseReason, deletePauseReason, getPauseReason, updatePauseReason } from "../Actions/PauseReasonActions";
import { openNotificationWithIcon } from "../Shared/notification";
import Text from "antd/es/typography/Text";

const PauseReasons = () => {

    const pauseReasons = useSelector(state => state.PauseResonReducer)
    const [showEdit, setShowEdit] = useState(false)
    const [form] = Form.useForm()
    const [record, setRecord] = useState(null)
    const [showCreate, setShowCreate] = useState(false)
    const dispatch = useDispatch()

    useEffect(() => {
        dispatch(getPauseReason())
    }, [])

    useEffect(() => {
        if ((typeof pauseReasons.errMess === 'object' && pauseReasons.errMess !== null)) {
            let obj = pauseReasons.errMess.data.errors
            let message = Object.keys(obj).map((v) => obj[v])
            openNotificationWithIcon('error', message.map(v => <Text>{v}{"\n"}</Text>))
        }
        else if (pauseReasons.errMess)
            openNotificationWithIcon('error', pauseReasons.errMess)

    }, [pauseReasons.errMess])

    useEffect(() => {
        if (pauseReasons.message != null) {
            openNotificationWithIcon('success', pauseReasons.message)
            dispatch(getPauseReason())
        }
    }, [pauseReasons.message])

    const handleSubmit = values => {
        setShowEdit(false)
        dispatch(updatePauseReason(values, record.id))
    }

    const handleCreate = values => {
        dispatch(createPauseReason(values))
        form.setFieldsValue({ 'name': '' })
        setShowCreate(false)
    }

    const handleShowCreate = () => {
        setRecord(pauseReasons.pauseReason)
        setShowCreate(true)
    }

    return (
        <Spin spinning={pauseReasons.isLoading}>
            <Modal
                centered
                title="Edit Pause Reason"
                visible={showEdit}
                destroyOnClose={true}
                closable={true}
                okText="Update"
                onOk={() => {
                    form.validateFields()
                        .then(values => handleSubmit(values))
                        .catch(error => console.log(error))
                }}
                onCancel={() => {
                    setShowEdit(false)
                }}
            >
                <Form
                    form={form}
                    layout="inline"
                    name="form_in_modal"
                    initialValues={record}
                >
                    <Form.Item
                        name="name"
                        kay="1"
                        label="Pause Reason"
                    >
                        <Input />
                    </Form.Item>

                </Form>
            </Modal>
            <Modal
                centered
                title="Add Pause Reason"
                visible={showCreate}
                destroyOnClose={true}
                closable={true}
                okText="Submit"
                onOk={() => {
                    form.validateFields()
                        .then(values => handleCreate(values))
                        .catch(error => console.log(error))
                }}
                onCancel={() => {
                    setShowCreate(false)
                }}
            >
                <Form
                    form={form}
                    layout="inline"
                    name="form_in_modal"
                >
                    <Row gutter={[16, 24]}>
                        <Form.Item
                            name="name"
                            key="2"
                            label="Pause Reason"
                        >
                            <Input />
                        </Form.Item>

                    </Row>
                </Form>
            </Modal>
            <Space style={{ marginBottom: 16 }}>
                <Button onClick={handleShowCreate} icon={<PlusOutlined />} type="primary">Add New</Button>
            </Space>
            <Table scroll={{ x: 800 }} size="small" bordered dataSource={pauseReasons.pauseReason}>
                <Table.Column dataIndex="name" key="name" title="Name" />
                <Table.Column dataIndex="created_at" key="created_at" title="CreatedAt" />
                <Table.Column dataIndex="updated_at" key="updated_at" title="UpdateAt" />
                <Table.Column align="center" title="Actions" render={(text, record) => (
                    <Space>
                        <Button onClick={() => {
                            form.setFieldsValue(record)
                            setRecord(record)
                            setShowEdit(true)
                        }} icon={<EditOutlined />} type="primary">Edit</Button>
                        <Button onClick={() => {
                            dispatch(deletePauseReason(record.id))
                        }} icon={<DeleteOutlined />} type="danger">Delete</Button>
                    </Space>
                )} />
            </Table>
        </Spin>
    )
}

export default PauseReasons