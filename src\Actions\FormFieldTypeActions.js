import * as ActionTypes from "../Constants/FormFieldTypeConstants"
import apiClient from "../Shared/apiClient"
import {FORM_FIELD_TYPE} from "../Endpoints/FormFieldTypeRoutes"

const handleError = error => {
    if(error.response) {
        return error.response.data
    } else {
        return error.message
    }
}

export const getFormFieldTypes = () => dispatch => {
    apiClient.get(FORM_FIELD_TYPE).then(d => dispatch(formFieldTypesSuccess(d.data))).catch(e => dispatch(formFieldTypesFailed(handleError(e))))
}

const formFieldTypesSuccess = message => ({
    type: ActionTypes.FORM_FIELD_TYPES_SUCCESS,
    payload: message
})

const formFieldTypesFailed = err => ({
    type: ActionTypes.FORM_FIELD_TYPES_FAILED,
    payload: err
})

const formFieldTypesLoading = () => ({
    type: ActionTypes.FORM_FIELD_TYPES_LOADING
})