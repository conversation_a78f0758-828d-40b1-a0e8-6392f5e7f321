import * as ActionTypes from "../Constants/AgentCallReportConstants"

const initialState = {
    isLoading: false,
    data: [],
    outbound: [],
    errMess: ''
}

export const AgentCallReportReducer = (state = initialState, action) => {
    switch (action.type) {
        case ActionTypes.AGENT_CALL_REPORT_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.AGENT_CALL_REPORT_SUCCESS:
            return { ...state, isLoading: false, data: action.payload, errMess: '' }
        case ActionTypes.AGENT_CALL_REPORT_SUCCESS_OUTBOUND:
            return { ...state, isLoading: false, outbound: action.payload, errMess: '' }
        case ActionTypes.AGENT_CALL_REPORT_FAILED:
            return { ...state, isLoading: false, errMess: action.payload }
        case ActionTypes.AGENT_CALL_REPORT_RESET:
            return { ...state, data: [], outbound: [] }
        default:
            return state
    }
}