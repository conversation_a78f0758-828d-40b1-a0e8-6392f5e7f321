import * as ActionTypes from "../Constants/FormFieldConstants"

const initial = {
    fields: [],
    message: false,
    errMess: false,
    isLoading: false
}

export const FormFieldReducer = (state = initial, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.FORM_FIELD_LOADING:
            return {...state, isLoading: true, message: false, errMess: false}
        case ActionTypes.FORM_FIELDS_SUCCESS:
            return {...state, isLoading: false, message: false, errMess: false, fields: action.payload}
        case ActionTypes.FORM_FIELD_SUCCESS:
            return {...state, isLoading: false, message: action.payload, errMess: false}
        case ActionTypes.FORM_FIELD_FAILED:
            return {...state, isLoading: false, message: false, errMess: action.payload}
    }
}