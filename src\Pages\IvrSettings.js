import { <PERSON><PERSON>, Card, DatePicker, Form, Modal, Select, Space, Switch, Upload } from 'antd'
import moment from 'moment';
import React, { useEffect, useState } from 'react'
import apiClient from '../Shared/apiClient';
import { openNotificationWithIcon } from '../Shared/notification';
import { UploadOutlined } from '@ant-design/icons';


const getFiles = () => {
    // return apiClient.get("/api/getVoiceMailFiles");
    return apiClient.get("/api/getIvrFiles");
};

const updateSettings = (values) => {
    return apiClient.patch("/api/updateIvrSettings", values);
};
const fetchSettings = () => {
    return apiClient.get("/api/getIvrSettings");
};

function IvrSettings() {


    const [form] = Form.useForm();

    const [voiceMailFiles, setVoiceMailFiles] = useState([]);
    const [uploadModalVisible, setUploadModalVisible] = useState(false);
    const [fileList, setFileList] = useState([]);
    const handleSubmit = () => {
        form.validateFields().then((values) => {
            updateSettings(values)
                .then((res) => {
                    fetchIvrSettings();
                    openNotificationWithIcon("success", res.data.message)
                }).catch((e) => openNotificationWithIcon("error", e.message))
        }).catch((e) => console.log("Error ", e))
    }


    useEffect(() => {
        getFiles()
            .then((r) => {
                setVoiceMailFiles(r.data)

                console.log("files", r?.data)
            })
            .catch((e) => console.log(e));
    }, [uploadModalVisible]);

    const fetchIvrSettings = () => {
        fetchSettings()
            .then((r) => {
                form.setFieldsValue({
                    ivrFile: r.data.ivr_file_id,
                    weeks: r.data.weeks,
                    except: r.data.except,
                    specificDates: r.data.specificDates,
                    specificDateStart: r?.data?.specificDateStart ? (moment(r.data.specificDateStart, "HH:mm:ss")) : '',
                    specificDateEnd: r?.data?.specificDateEnd ? (moment(r.data.specificDateEnd, "HH:mm:ss")) : '',
                    start: moment(r.data.start, "HH:mm:ss"),
                    end: moment(r.data.end, "HH:mm:ss"),
                    status: r.data.status,
                })

                console.log("settings", r.data)
            }
            )
            .catch((e) => console.log(e));
    }

    useEffect(() => {
        fetchIvrSettings()
    }, []);

    const handleFileUpload = (file) => {
        setTimeout(() => {
            console.log("1001 file", file);
            openNotificationWithIcon(
                "success",
                `${file.name} uploaded successfully!`
            );
        }, 500);
    };

    const customRequest = ({ file, onSuccess, onError }) => {
        const formData = new FormData();
        formData.append("file", file);

        // Assuming you have an API endpoint to handle the file upload
        // fetch(uploadURL, {
        //   method: "POST",
        //   headers: {
        //     Authorization: `Bearer ${getToken()}`,
        //   },
        //   body: formData,
        // })
        apiClient
            .post("/api/uploadIvrFile", formData)
            .then((data) => {
                handleFileUpload(file); // Call the handleFileUpload function upon successful upload
                onSuccess(data, file);
                // Clear file list after successful upload
                formData.delete(file);

            })
            .catch((error) => {
                console.error("Error uploading file:", error);
                onError(error);
            });
    };
    const handleCancel = () => {
        setFileList([]); // Clear file list when modal is closed
        setUploadModalVisible(false);
    };

    return (


        <>

            <div className='settings-cont' style={{ maxWidth: '100%', background: '#FFF', padding: '10px' }}>

                <div style={{ maxWidth: '500px', boxShadow: 'rgba(149, 157, 165, 0.2) 0px 8px 24px', padding: '10px' }}>


                    <Card
                        size="small"
                        title="GreetEase"
                        extra={
                            <Space>

                                <Button
                                    onClick={() => setUploadModalVisible(true)}
                                    icon={<UploadOutlined />}
                                />
                                {/* <Button onClick={handleExportClick} icon={<ExportOutlined />} /> */}
                            </Space>
                        }
                    />


                    <Form form={form} layout="vertical" name="SettingsForm">
                        <Form.Item
                            name="ivrFile"
                            label="Select file to play recording GreetEase"
                            rules={[
                                {
                                    required: true,
                                    message: "This field is required.",
                                },
                            ]}
                        >
                            <Select>
                                {voiceMailFiles &&
                                    voiceMailFiles.map((r) => (
                                        <Select.Option key={r.id} value={r.id}>
                                            {r.name}
                                        </Select.Option>
                                    ))}
                            </Select>
                        </Form.Item>
                        <Form.Item
                            name="weeks"
                            label="GreetEase Weekdays"
                            rules={[
                                {
                                    required: true,
                                    message: "This field is required.",
                                },
                            ]}
                        >
                            <Select
                                mode="multiple"
                                allowClear
                                placeholder="Please Select Weekdays"
                            >
                                <Select.Option value="Monday">Monday</Select.Option>
                                <Select.Option value="Tuesday">Tuesday</Select.Option>
                                <Select.Option value="Wednesday">Wednesday</Select.Option>
                                <Select.Option value="Thursday">Thursday</Select.Option>
                                <Select.Option value="Friday">Friday</Select.Option>
                                <Select.Option value="Saturday">Saturday</Select.Option>
                                <Select.Option value="Sunday">Sunday</Select.Option>
                            </Select>
                        </Form.Item>
                        <Form.Item
                            name="except"
                            label="Except..."
                            extra="GreetEase will be enabled whole day for the selected."
                        >
                            <Select allowClear mode="multiple">
                                <Select.Option value="Monday">Monday</Select.Option>
                                <Select.Option value="Tuesday">Tuesday</Select.Option>
                                <Select.Option value="Wednesday">Wednesday</Select.Option>
                                <Select.Option value="Thursday">Thursday</Select.Option>
                                <Select.Option value="Friday">Friday</Select.Option>
                                <Select.Option value="Saturday">Saturday</Select.Option>
                                <Select.Option value="Sunday">Sunday</Select.Option>
                            </Select>
                        </Form.Item>
                        <Form.Item
                            name="specificDates"
                            label="Or.. Add Specific Dates (format: yyyy-mm-dd)"
                        >
                            <Select mode="tags" allowClear multiple />
                        </Form.Item>
                        <Form.Item
                            name="specificDateStart"
                            label="Start Time for Specific Days Selected Above"
                        >
                            <DatePicker.TimePicker format="HH:mm:ss" />
                        </Form.Item>
                        <Form.Item
                            name="specificDateEnd"
                            label="End Time for Specific Days Selected Above"
                        >
                            <DatePicker.TimePicker format="HH:mm:ss" />
                        </Form.Item>
                        <Form.Item
                            name="start"
                            label="GreetEase Start Time"
                            rules={[
                                {
                                    required: true,
                                    message: "This field is required.",
                                },
                            ]}
                        >
                            <DatePicker.TimePicker format="HH:mm:ss" />
                        </Form.Item>
                        <Form.Item
                            name="end"
                            label="GreetEase End Time"
                            rules={[
                                {
                                    required: true,
                                    message: "This field is required.",
                                },
                            ]}
                        >
                            <DatePicker.TimePicker format="HH:mm:ss" />
                        </Form.Item>
                        <Form.Item
                            name="status"
                            label="Enable GreetEase"
                            valuePropName="checked"
                            rules={[
                                {
                                    required: true,
                                    message: "This field is required.",
                                },
                            ]}
                        >
                            <Switch size="large" />
                        </Form.Item>

                        <Button
                            type="primary"
                            onClick={() => {
                                handleSubmit()
                            }}
                        >
                            Save</Button>

                    </Form>

                </div>
            </div>

            <Modal
                title={
                    <>
                        <UploadOutlined /> GreetEase File Upload
                    </>
                }
                visible={uploadModalVisible}
                onCancel={() => setUploadModalVisible(false)}
                onOk={() => {
                    getFiles();
                    handleCancel();
                    setUploadModalVisible(false);
                }}
            >
                <Upload
                    name="file"
                    customRequest={customRequest}
                    fileList={fileList} // Controlled file list
                    onChange={({ fileList }) => setFileList(fileList)} // Update file list on change
                //   headers={{ Authorization: `Bearer ${getToken()}` }}
                //   action={uploadURL}
                >
                    <Button icon={<UploadOutlined />}>Click to upload</Button>
                </Upload>
            </Modal>
        </>



    )
}

export default IvrSettings