import { useDispatch, useSelector } from "react-redux";
import { filterReport, getAllLoginReport } from "../../Actions/AgentReportActions";
import { Button, Descriptions, Modal, Space, Spin, Table } from "antd";
import { CSVDownload, CSVLink } from "react-csv";
import ReactExport from "react-export-excel";
import { DesktopOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { useState, useEffect } from 'react'
import LoginReportFilter from "../../Components/Reports/LoginReportFilter";
import generateAgentReportPDF from './PDFGenerator/generateAgentReportPDF'
import { CdrReportFilter, getAllReportsPaginate } from "../../Actions/CallDetailReportsActions";
import CallDetailReportFilter from "../../Components/Reports/CdrReportFilter";

const LoginReport = () => {
    const report = useSelector(state => state.AgentReportReducer)
    const [record, setRecord] = useState(null)
    const [showDetails, setShowDetails] = useState(false)
    const [filter, setFilter] = useState(false)
    const [values, setValues] = useState()
    const [loading, isLoading] = useState(false)
    const [filteredPaginate, setFilteredPaginate] = useState(false)
    const dispatch = useDispatch()

    const getData = (page, pageSize = 10) => {
        const data = getRandomuserParams({})
        dispatch(getAllLoginReport(page, pageSize, data))
    }

    function onFilter(values, pagination) {
        console.log(pagination)
        const data = getRandomuserParams({})
        setValues(values)
        // let obj = {
        //     values,
        //     pagination,
        //     data
        // }
        dispatch(filterReport(values))
        setFilter(false)
    }

    const getRandomuserParams = params => ({
        results: params?.pagination?.pageSize,
        page: params?.pagination?.current,
        ...params,
    });

    useEffect(() => {
        getData(1, 10)
    }, [])

    const handleTableChange = (pagination) => {
        if (!filteredPaginate)
            getData(pagination.current, pagination.pageSize)
        else
            onFilter(values, pagination)
    }

    return (

        <Spin spinning={report.isLoading}>
            <LoginReportFilter setVisible={setFilter} setFilteredPaginate={setFilteredPaginate} isLoading={report.isLoading} onChange={handleTableChange} onCancel={() => setFilter(false)} visible={filter} onCreate={onFilter} record={"ok"} />

            <Button onClick={() => setFilter(true)} type="primary" icon={<PlusCircleOutlined />}>
                Filter
            </Button>

            <Modal
                width="100%"
                visible={showDetails}
                closable={true}
                onCancel={() => setShowDetails(false)}
                onOk={() => setShowDetails(false)}
                centered
            >
                <Descriptions style={{ padding: 20 }} title="Agent Login Info" bordered size="small">
                    {record && Object.keys(record).map((key, index) => (
                        <Descriptions.Item label={key}>
                            {(record[key]) ? (record[key]) : ""}
                        </Descriptions.Item>))}
                </Descriptions>

            </Modal>

            <div style={{ float: 'right' }}>
                <div style={{ marginBottom: 16 }}>
                    <Space>
                        {report.agentReport && <Button type={"primary"} onClick={(<CSVDownload data={report.agentReport} target="_blank" />)}>
                            <CSVLink data={report.agentReport}> Download CSV</CSVLink>
                        </Button>}
                        {report.agentReport && <Button type={"default"} onClick={() => {
                            generateAgentReportPDF(report.agentReport)
                        }} target="_blank">Generate PDF</Button>}
                    </Space>
                </div>
            </div>

            <Table scroll={{ x: 800 }} size="small" bordered dataSource={report.agentReport} loading={loading} pagination={report.pagination} >
                <Table.Column dataIndex="queuename" key="queuename" title="queuename" />
                <Table.Column dataIndex="agent" key="agent" title="Agent" />
                <Table.Column dataIndex="intime" key="intime" title="Login Time" />
                <Table.Column dataIndex="outtime" key="outtime" title="Logout Time" />
                <Table.Column dataIndex="duration" key="duration" title="Duration (H:M:S)" />
                <Table.Column align="center" title="Actions" render={(text, record) => (
                    <Space>
                        <Button onClick={() => {
                            setRecord(record)
                            setShowDetails(true)
                        }} type="outlined" icon={<DesktopOutlined />}>Details</Button>
                    </Space>
                )} />
            </Table>
        </Spin>
    )
}

export default LoginReport