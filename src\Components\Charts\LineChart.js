import React, { useEffect, useState } from 'react';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';

ChartJS.register(
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend
);


export default function LineChart({ chartData, title }) {

    const options = {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: true,
                text: `${title} Call Summary `,
            },
        },
    };

    const state = {
        title: `${title} Call Summary`,
        labels: chartData?.map((e) => e.Date),
        option: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: true,
                    text: `${title} Call Summary `,
                },
            },
        },
        datasets: [
            {
                label: `${title} Success`,
                data: chartData?.map((e) => e.Success),
                fill: true,
                backgroundColor: "#15347c",
            },
            {
                label: `${title} Failed`,
                data: chartData?.map((e) => e.Failed),
                fill: false,
                backgroundColor: '#f4bf20'
            }
        ]
    };


    return <Bar options={options} data={state} />;
}
