import * as ActionTypes from "../Constants/QueueConstants"

const initialState = {
    isLoading: false,
    queues: [],
    allQueues: [],
    enums: [],
    errMess: null,
    message: null,
    column: ''
}

export const QueueReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.QUEUE_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.QUEUE_SUCCESS:
            return { ...state, isLoading: false, queues: action.payload, message: null, errMess: null }
        case 'GET_ALL_QUEUES_SUCCESS':
            return { ...state, isLoading: false, allQueues: action.payload, message: null, errMess: null }
        case ActionTypes.QUEUE_FAILED:
            return { ...state, isLoading: false, errMess: action.payload }
        case ActionTypes.ENUM_SUCCESS:
            return { ...state, isLoading: false, enums: action.payload.enum, column: action.payload.column, errMess: null }
        case ActionTypes.ENUM_FAILED:
            return { ...state, isLoading: false, errMess: action.payload }
        case ActionTypes.UPDATE_SUCCESS:
            return { ...state, isLoading: false, message: action.payload, errMess: null }
        case ActionTypes.UPDATE_FAILED:
            return { ...state, isLoading: false, errMess: action.payload, statusCode: 0 }
        case ActionTypes.DELETE_SUCCESS:
            return { ...state, isLoading: false, message: action.payload, errMess: null }
        case ActionTypes.DELETE_FAILED:
            return { ...state, isLoading: false, errMess: action.payload, statusCode: 0 }
        case ActionTypes.CREATE_SUCCESS:
            return { ...state, isLoading: false, message: action.payload, errMess: null }
        case ActionTypes.CREATE_FAILED:
            console.log(action.payload)
            return { ...state, isLoading: false, errMess: action.payload }
    }
}