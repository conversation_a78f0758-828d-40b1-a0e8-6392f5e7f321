import * as ActionTypes from "../Constants/AgentRoutesConstant"

const initialState = {
    agent: [],
    errMess: null,
    isLoading: false,
    message: ''
}

export const AgentReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.AGENT_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.AGENT_SUCCESS:
            return { ...state, isLoading: false, users: action.payload, errMess: null, message: '' }
        case ActionTypes.AGENT_FAILED:
            return { ...state, isLoading: false, message: '', errMess: action.payload }
        case ActionTypes.AGENT_CREATE_SUCCESS:
            return { ...state, isLoading: false, message: action.payload, errMess: null }
        case ActionTypes.AGENT_UPDATE_SUCCESS:
            return { ...state, isLoading: false, message: action.payload, errMess: null }
        case ActionTypes.AGENT_DELETE_SUCCESS:
            return { ...state, isLoading: false, message: action.payload, errMess: null }
    }
}