
import { <PERSON><PERSON>, Card, DatePicker, Form, Input, Select, Space, Spin } from "antd";
import { useEffect, useState } from "react";
import apiClient from "../../Shared/apiClient";
import { handleError } from "../../Shared/handleError";

import { Chart } from "react-google-charts";
import { FilterOutlined, ReloadOutlined } from "@ant-design/icons";
import ChannelOccupancyFilter from "../../Components/Reports/ChannelOccupancyFilter";

export const options = {
    title: "Company Performance",
    curveType: "function",
    legend: { position: "bottom" }
};

export const ChannelOccupancy = () => {
    const [data, setData] = useState([])
    const [error, setError] = useState('')
    const [inboundData, setInboundData] = useState([])
    const [outboundData, setOutboundData] = useState([])
    const [visible, setVisible] = useState(false)
    const [loading, setLoading] = useState(false)


    const [type, setType] = useState('')
    const getData = () => apiClient.post(`/api/report/channel-occupancy`)

    useEffect(() => {
        getData()
            .then(r => setData(r.data))
            .catch(e => setError(e))
    }, [])



    const onFinish = v => {
        setLoading(true)
        let url = "";
        setType(v.type)
        console.log(v)
        if (v.type == "inbound")
            url = "api/report/trunk-per-hour"
        else if (v.type == "outbound")
            url = "api/report/trunk-per-hour-outbound"
        apiClient.post(url, v).then((r) => {
            setData(r.data)
            setLoading(false)
        }).catch((e) => {
            setError(handleError(e))
            setLoading(false)
        })
        setVisible(false)
    }

    useEffect(() => {
        if (data) {
            if (data.length && data.length > 0) {
                if (data[0]?.entered) {
                    let arr = []
                    arr.push(["time_period", "abandoned", "answered", "entered"])
                    data.map(v => arr.push([v.time_period, parseInt(v.abandoned), parseInt(v.answered), parseInt(v.entered)]))
                    setInboundData(arr)
                }
                else {
                    let arr = []
                    arr.push(["name", "outbound", "queue"])
                    data.map(v => arr.push([v.name, parseInt(v.outbound), parseInt(v.queue)]))
                    setOutboundData(arr)
                }
            }
        }
    }, [data])

    return (
        <Card title="Channel Occupancy Graph"
            extra={
                <>
                    <Button
                        style={{ marginRight: '10px' }}
                        danger
                        type="primary"
                        icon={<ReloadOutlined />}
                        onClick={() => {
                            setOutboundData([]);
                            setInboundData([]);
                        }}
                    >
                        Reset
                    </Button>
                    <Button onClick={() => setVisible(true)} icon={<FilterOutlined />}>
                        Filter
                    </Button>
                </>
            }
        >

            {
                loading ? <div style={{ display: 'flex', justifyContent: 'center', width: '100%' }}><Spin /></div> :

                    <>
                        {type === "outbound" && <div>
                            {outboundData?.length > 0 ? <Chart
                                errorElement={<div>asdsds</div>}
                                chartType="LineChart"
                                width="100%"
                                height="400px"
                                data={outboundData}
                                options={{
                                    title: "Outbound Channel Occupancy Report",
                                    curveType: "function",
                                    legend: { position: "bottom" }
                                }}
                            /> : <div style={{ color: 'red' }}> {error}</div>}
                        </div>}

                        {type === "inbound" && <div>
                            {inboundData?.length > 0 ? <Chart
                                chartType="LineChart"
                                width="100%"
                                height="400px"
                                data={inboundData}
                                options={{
                                    title: "Inbound Channel Occupancy Report",
                                    curveType: "function",
                                    legend: { position: "bottom" }
                                }}
                            /> : <div style={{ color: 'red' }}>{error}</div>}
                        </div>}
                        <ChannelOccupancyFilter visible={visible} setVisible={setVisible} onFinish={onFinish} />
                    </>
            }
        </Card>
    )
}

