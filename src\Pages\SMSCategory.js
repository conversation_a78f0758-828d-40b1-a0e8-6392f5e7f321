import { <PERSON><PERSON>, Card, Col, Form, Input, Row, Space, Spin, Table } from "antd";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { deleteCategory, getCategory, saveCategory, updateCategory } from "../Actions/SMSCategoryActions";
import { SMSCategoryReducer } from "../Reducer/SMSCategoryReducer";
import { DeleteTwoTone, EditTwoTone } from "@ant-design/icons";
import { openNotificationWithIcon } from "../Shared/notification";



export const SMSCategory = () => {

    const [form] = Form.useForm()
    const dispatch = useDispatch()
    const [btnText, setBtnTxt] = useState("Save")
    const smsCategory = useSelector(state => state.SMSCategoryReducer)

    useEffect(() => dispatch(getCategory()), [])
    useEffect(() => {
        if (smsCategory.message != null) {
            dispatch(getCategory())
            openNotificationWithIcon('success', smsCategory.message)
        }
    }, [smsCategory.message])

    const handleSubmit = v => {
        if (btnText == "Save")
            dispatch(saveCategory(v))
        else
            dispatch(updateCategory(v))
        form.resetFields()
        setBtnTxt("Save")
    }

    const columns = [
        {
            title: '#',
            key: '#',
            render: (txt, record, index) => ++index
        },
        {
            title: 'Name',
            dataIndex: 'name',
            key: 'name'
        },
        {
            title: 'CreatedAt',
            dataIndex: 'created_at',
            key: 'created_at'
        },
        {
            title: 'UpdatedAt',
            dataIndex: 'updated_at',
            key: 'updated_at'
        },
        {
            title: 'Actions',
            key: 'actions',
            render: (txt, v) => {
                return (<Space>
                    <Button onClick={() => {
                        form.setFieldsValue(v)
                        setBtnTxt('Update')
                    }}><EditTwoTone /></Button>
                    <Button onClick={() => {
                        form.resetFields()
                        setBtnTxt('Save')
                        dispatch(deleteCategory(v))
                    }}><DeleteTwoTone twoToneColor="red" /></Button>
                </Space>)
            }
        }
    ]


    return (<>
        <Spin spinning={smsCategory.isLoading}>
            <Card title={"SMS Category"}>
                <Form
                    form={form}
                    // initialValues={record}
                    onFinish={handleSubmit}
                    layout="vertical"
                >

                    <Form.Item name="name" label="Category Name" rules={[{ required: true, message: 'Please input category name!' }]}>
                        <Input />
                    </Form.Item>

                    <Form.Item hidden name="id">
                        <Input />
                    </Form.Item>

                    <Form.Item>
                        <Button type="primary" htmlType="submit">
                            {btnText}
                        </Button>
                    </Form.Item>
                </Form>
            </Card>

            <Card title={"SMS Category Data"} style={{ marginTop: '10px' }}>
                <Table columns={columns} dataSource={smsCategory.data} />
            </Card>
        </Spin>
    </>)
}