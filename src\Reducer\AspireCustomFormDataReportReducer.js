import * as ActionTypes from "../Constants/AspireCustomFormDataReportConstants"
const initialState = {
    formData: [],
    isLoading: false,
    errMess: ''
}

export const AspireCustomFormDataReportReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.FORM_DATA_REPORT_LOADING:
            return {...state, isLoading: true}
        case ActionTypes.FORM_DATA_REPORT_SUCCESS:
            return {...state, isLoading: false, formData: action.payload}
        case ActionTypes.FORM_DATA_REPORT_FAILED:
            return {...state, isLoading: false, errMess: action.payload}
    }
}