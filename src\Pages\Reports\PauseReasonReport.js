import { useDispatch, useSelector } from "react-redux";
import {
    getPauseReasonReport,
    getPauseReasonReportFilter
} from "../../Actions/AgentReportActions";
import { Button, Descriptions, Modal, Space, Spin, Table } from "antd";
import { CSVDownload, CSVLink } from "react-csv";
import ReactExport from "react-export-excel";
import { DesktopOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { useState, useEffect } from 'react'
import PauseReasonReportFilter from "../../Components/Reports/PauseReasonReportFilter";

// const getColumnSearchProps = dataIndex => ({
//     filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
//         <div style={{ padding: 8 }}>
//             <Input
//                 ref={node => {
//                     this.searchInput = node;
//                 }}
//                 placeholder={`Search ${dataIndex}`}`
//                 value={selectedKeys[0]}
//                 onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
//                 onPressEnter={() => this.handleSearch(selectedKeys, confirm, dataIndex)}
//                 style={{ width: 188, marginBottom: 8, display: 'block' }}
//             />
//             <Space>
//                 <Button
//                     type="primary"
//                     onClick={() => this.handleSearch(selectedKeys, confirm, dataIndex)}
//                     icon={<SearchOutlined />}
//                     size="small"
//                     style={{ width: 90 }}
//                 >
//                     Search
//                 </Button>
//                 <Button onClick={() => this.handleReset(clearFilters)} size="small" style={{ width: 90 }}>
//                     Reset
//                 </Button>
//             </Space>
//         </div>
//     ),
//     filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
//     onFilter: (value, record) =>
//         record[dataIndex]
//             ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
//             : '',
//     onFilterDropdownVisibleChange: visible => {
//         if (visible) {
//             setTimeout(() => this.searchInput.select(), 100);
//         }
//     },
//     render: text =>
//         (text),
// });
//
// const handleSearch = (selectedKeys, confirm, dataIndex) => {
//     confirm();
//     this.setState({
//         searchText: selectedKeys[0],
//         searchedColumn: dataIndex,
//     });
// };
//
// const handleReset = clearFilters => {
//     clearFilters();
//     this.setState({ searchText: '' });
// };


const PauseReasonReport = () => {
    const report = useSelector(state => state.AgentReportReducer)
    const [record, setRecord] = useState(null)
    const [showDetails, setShowDetails] = useState(false)
    const [filter, setFilter] = useState(false)

    const dispatch = useDispatch()

    useEffect(() => {
        dispatch(getPauseReasonReport())
    }, [])

    // useEffect(() => {
    //     console.log(record)
    // },[record])

    function onFilter(values) {
        console.log(values)
        dispatch(getPauseReasonReportFilter(values))
        setFilter(false)
    }

    return (
        <Spin spinning={report.isLoading}>
            <PauseReasonReportFilter setVisible={setFilter} isLoading={report.isLoading} onCancel={() => setFilter(false)} visible={filter} onCreate={onFilter} record={"ok"} />

            <Button onClick={() => setFilter(true)} type="primary" icon={<PlusCircleOutlined />}>
                Filter
            </Button>

            <Modal
                width="100%"
                visible={showDetails}
                closable={true}
                onCancel={() => setShowDetails(false)}
                onOk={() => setShowDetails(false)}
                centered
            >
                <Descriptions style={{ padding: 20 }} title="Pause Reason Info" bordered size="small">
                    {record && Object.keys(record).map((key, index) => (
                        <Descriptions.Item label={key}>
                            {(record[key]) ? (record[key]) : ""}
                        </Descriptions.Item>))}
                </Descriptions>

            </Modal>

            <div style={{ float: 'right' }}>
                <div style={{ marginBottom: 16 }}>
                    <Space>
                        <Button type={"primary"} onClick={(<CSVDownload data={report.agentReport} target="_blank" />)}>
                            <CSVLink data={report.agentReport} > Download CSV</CSVLink>
                        </Button>

                        <ReactExport.ExcelFile element={<Button type={"default"}>Download Excel</Button>} fileExtension={"xlsx"} filename={"CDR Report"}>
                            <ReactExport.ExcelFile.ExcelSheet data={report.agentReport} >
                                {record && Object.entries(record).map((key, index) => (
                                    <ReactExport.ExcelFile.ExcelColumn label={key} value={key} />
                                ))}
                            </ReactExport.ExcelFile.ExcelSheet>
                        </ReactExport.ExcelFile>
                    </Space>
                </div>
            </div>

            <Table scroll={{ x: 800 }} size="small" bordered dataSource={report.agentReport}>
                <Table.Column dataIndex="queuename" key="queuename" title="queuename" />
                <Table.Column dataIndex="agent" key="agent" title="Agent" />
                {/*<Table.Column dataIndex="Event" key="Event" title="Event" sorter={true} />*/}
                <Table.Column dataIndex="intime" key="intime" title="Login Time" />
                <Table.Column dataIndex="outtime" key="outtime" title="Logout Time" />
                <Table.Column dataIndex="duration" key="duration" title="Duration (H:M:S)" />
                <Table.Column align="center" title="Actions" render={(text, record) => (
                    <Space>
                        <Button onClick={() => {
                            setRecord(record)
                            setShowDetails(true)
                        }} type="outlined" icon={<DesktopOutlined />}>Details</Button>
                        {/*<Button onClick={() => {*/}
                        {/*    dispatch(deleteQueue(record.name))*/}
                        {/*}} icon={<DeleteOutlined />} type="danger">Delete</Button>*/}
                    </Space>
                )} />
            </Table>
        </Spin>
    )
}

export default PauseReasonReport