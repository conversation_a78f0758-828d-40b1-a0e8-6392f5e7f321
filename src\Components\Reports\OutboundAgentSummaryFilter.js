import { useDispatch, useSelector } from "react-redux";
import { DatePicker, Form, Input, Modal, Select, Spin } from "antd";
import { useEffect, useState } from "react"
import { getQueues } from "../../Actions/QueueActions";
import { getAgent } from "../../Actions/AgentActions";

export const OutboundAgentSummaryFilter = ({ visible, setVisible, onCreate, isLoading }) => {
    const [form] = Form.useForm()
    // const QueueState = useSelector(state => state.QueueReducer)
    // const [showQueue, setShowQueue] = useState(false)
    const dispatch = useDispatch()
    const agentState = useSelector(state => state.AgentReducer)

    // useEffect(() => {
    //     if (visible) {
    //         // dispatch(getQueues())
    //         // setShowQueue(true)
    //     }
    // }, [visible])


    useEffect(() => {
        if (visible) {

            dispatch(getAgent())
        }
    }, [visible])
    return (
        <Spin spinning={false}>
            <Modal
                visible={visible}
                onCancel={() => {
                    form.resetFields()
                    setVisible(false)
                }}
                onOk={() => {
                    form
                        .validateFields()
                        .then((values) => {
                            console.log("validated")
                            onCreate(values);
                            form.resetFields();
                        })
                        .catch((info) => {
                            console.log('Validate Failed:', info);
                        })
                }}
            >
                <Form
                    form={form}
                    layout="vertical"
                    name="form_in_modal"
                >
                    <Form.Item
                        name="time"
                        label="Time"
                    >
                        <DatePicker.RangePicker showTime />
                    </Form.Item>
                    <Form.Item name="agent" label="Agent" style={{ width: '55%' }}>
                        <Select >
                            {agentState.users && agentState.users.map((value, index) => <Select.Option value={value.auth_username} key={value.id}>
                                {value.name}
                            </Select.Option>)}
                        </Select>
                    </Form.Item>
                </Form>

            </Modal>
        </Spin>
    );
}

export default OutboundAgentSummaryFilter;