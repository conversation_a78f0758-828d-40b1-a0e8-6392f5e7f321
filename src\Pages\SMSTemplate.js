import { Button, Card, Form, Input, Select, Space, Spin, Table } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";
import { deleteTemplate, getTemplate, saveTemplate, updateTemplate } from "../Actions/SMSTemplateActions";
import { openNotificationWithIcon } from "../Shared/notification";
import { DeleteTwoTone, EditTwoTone } from "@ant-design/icons";
import { SMSTemplateReducer } from "../Reducer/SMSTemplateReducer";
import { getCategory } from "../Actions/SMSCategoryActions";
import TextArea from "antd/es/input/TextArea";


export const SMSTemplate = () => {

    const [form] = Form.useForm()
    const dispatch = useDispatch()
    const [btnText, setBtnTxt] = useState("Save")
    const smsTemplate = useSelector(state => state.SMSTemplateReducer)
    const smsCategory = useSelector(state => state.SMSCategoryReducer)

    useEffect(() => {
        dispatch(getTemplate())
        dispatch(getCategory())
    }, [])
    useEffect(() => {
        if (smsTemplate.message != null) {
            openNotificationWithIcon('success', smsTemplate.message)
            dispatch(getTemplate())
        }
    }, [smsTemplate.message])

    const handleSubmit = v => {
        if (btnText == "Save")
            dispatch(saveTemplate(v))
        else
            dispatch(updateTemplate(v))
        form.resetFields()
        setBtnTxt("Save")
    }

    const columns = [
        {
            title: '#',
            key: '#',
            render: (txt, record, index) => ++index
        },
        {
            title: 'MSG',
            dataIndex: 'msg',
            key: 'msg'
        },
        {
            title: 'Category',
            // dataIndex: 's_m_s_category.name',
            key: 'category',
            render: (v) => v.s_m_s_category.name
        },
        {
            title: 'CreatedAt',
            dataIndex: 'created_at',
            key: 'created_at'
        },
        {
            title: 'UpdatedAt',
            dataIndex: 'updated_at',
            key: 'updated_at'
        },
        {
            title: 'Actions',
            key: 'actions',
            render: (txt, v) => {
                return (<Space>
                    <Button onClick={() => {
                        form.setFieldsValue(v)
                        setBtnTxt('Update')
                    }}><EditTwoTone /></Button>
                    <Button onClick={() => {
                        form.resetFields()
                        setBtnTxt('Save')
                        dispatch(deleteTemplate(v))
                    }}><DeleteTwoTone twoToneColor="red" /></Button>
                </Space>)
            }
        }
    ]

    return (<>
        <Spin spinning={smsTemplate.isLoading}>
            <Card title={"SMS Template"}>
                <Form
                    form={form}
                    // initialValues={record}
                    onFinish={handleSubmit}
                    layout="vertical"
                >

                    <Form.Item name="sms_category_id" label="SMS Category" rules={[{ required: true, message: 'Please input category name!' }]}>
                        <Select>
                            {smsCategory && smsCategory.data && smsCategory.data.map(v => <Select.Option value={v.id}>{v.name} </Select.Option>)}
                        </Select>
                    </Form.Item>

                    <Form.Item name="msg" label="Template Name" rules={[{ required: true, message: 'Please input template name!' }]}>
                        <TextArea rows={4} />
                    </Form.Item>

                    <Form.Item hidden name="id">
                        <Input />
                    </Form.Item>

                    <Form.Item>
                        <Button type="primary" htmlType="submit">
                            {btnText}
                        </Button>
                    </Form.Item>
                </Form>
            </Card>

            <Card title={"SMS Template Data"} style={{ marginTop: '10px' }}>
                <Table columns={columns} dataSource={smsTemplate.data} />
            </Card>
        </Spin>
    </>)
}