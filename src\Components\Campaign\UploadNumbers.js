import React, { useState, useEffect } from "react";
import { Button, Form, Input, Modal, notification, Upload } from "antd";
import { UploadOutlined } from "@ant-design/icons";
// import { postCustomNumbers } from "../../Actions/CustomNumberActions";
import { useDispatch, useSelector } from "react-redux";
import { openNotificationWithIcon } from "../../Shared/notification";
import { postCampaignNumbers } from "../../Actions/CampaignNumberActions";

const UploadNumbers = (props) => {
    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const state = useSelector((state) => state.CampaignNumberReducer);

    const [file, setFile] = useState(null);

    useEffect(() => {
        form.setFieldsValue({ campaign: props.item.name });
    }, [props.item]);

    // useEffect(() => {
    //     if (state && state.message)
    //         openNotificationWithIcon("success", state.message);
    // }, [state.message]);

    useEffect(() => {
        if (state && state.errMess) {
            if (state.errMess?.message)
                openNotificationWithIcon("error", state.errMess?.message);
            else
                openNotificationWithIcon("error", state.errMess);
        }
    }, [state.errMess]);

    const beforeUpload = (file) => {
        setFile(file);
        return false;
    };

    return (
        <Modal
            okText="Upload"
            title="Upload Numbers"
            visible={props.showUploadCampaign}
            onOk={() => {
                form
                    .validateFields()
                    .then((values) => {
                        form.resetFields();
                        let formData = new FormData();
                        formData.append("file", file);
                        dispatch(postCampaignNumbers(props.item.id, formData));
                        props.setShowUploadCampaign(false);
                    })
                    .catch((e) => console.log(e));
            }}
            onCancel={props.onUploadCancel}
        >
            <Form form={form} layout="vertical">
                <Form.Item name="campaign" label="Campaign">
                    <Input disabled />
                </Form.Item>
                <Form.Item name="file" label="Select File">
                    <Upload
                        multiple={false}
                        beforeUpload={beforeUpload}
                        listType="picture"
                    >
                        <Button icon={<UploadOutlined />}>Click to upload</Button>
                    </Upload>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default UploadNumbers;
