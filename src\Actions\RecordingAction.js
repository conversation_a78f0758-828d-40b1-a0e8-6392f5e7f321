import apiClient from "../Shared/apiClient";
import {handleError} from "../Shared/handleError";


export const getRecordings = data => dispatch => {
    dispatch(recordingReportLoading())
    apiClient.post(`api/download/test`, data).then(r => {
        const url = window.URL.createObjectURL(new Blob([r.data], {type: "application/zip"}));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'file.zip'); //or any other extension
        document.body.appendChild(link);
        link.click();

        dispatch(recordingReportSuccess(r.data))
    }).catch(e => dispatch(recordingReportFailed(handleError(e))))
}

const recordingReportSuccess = data => ({
    type: "RECORDING_REPORT_SUCCESS",
    payload: data
})

const recordingReportFailed = err => ({
    type: "RECORDING_REPORT_FAILED",
    payload: err
})

const recordingReportLoading = () => ({
    type: "RECORDING_REPORT_LOADING"
})