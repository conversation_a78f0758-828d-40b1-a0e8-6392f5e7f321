import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Card, DatePicker, Form, Input, Spin, Table, Select, Space, Modal } from "antd";
import apiClient from "../../Shared/apiClient";
import { CSVLink } from "react-csv";
import {  DownloadOutlined, FilterOutlined, ReloadOutlined } from "@ant-design/icons";

const columns = [
    {
        title: "Date",
        dataIndex: "Date(time)",
        key: "Date(time)",
        //width: 150,
    },
    {
        title: "Total Incoming Calls",
        dataIndex: "totalInbounCalls",
        key: "totalInbounCalls",
        //width: 150,
    },
    {
        title: "Total Answered Calls",
        dataIndex: "totalAnswerCalls",
        key: "totalAnswerCalls",
        //width: 150,
    },
    {
        title: "Abandoned/Lost Calls",
        dataIndex: "totalAbandonCalls",
        key: "totalAbandonCalls",
        //width: 150,
    },
    {
        title: "Customer Service Factor",
        dataIndex: "CustomerServiceFactor",
        key: "CustomerServiceFactor",
        //width: 150,
        render: (v) => `${v}%`
    },
    {
        title: "Answered Calls Percentage",
        dataIndex: "percentageOfAnsweredCalls",
        key: "percentageOfAnsweredCalls",
        //width: 150,
        render: (v) => `${v}%`,
    },
];

const exportHeaders = [
    { label: 'Date', key: 'Date(time)' },
    { label: 'Total Incoming Calls', key: 'totalInbounCalls' },
    { label: 'Total Answered Calls', key: 'totalAnswerCalls' },
    { label: 'Abandoned/Lost Calls', key: 'totalAbandonCalls' },
    { label: 'Customer Service Factor', key: 'CustomerServiceFactor' },
    { label: 'Answered Calls Percentage', key: 'percentageOfAnsweredCalls' },
]

const MonthlyReportBykea = () => {
    const [form] = Form.useForm();
    const [data, setData] = useState([]);
    const [month, setMonth] = useState();
    const [fetchReportCheck, setFetchReportCheck] = useState(true);
    const [loading, setLoading] = useState(false);
    const [queues,setQueues] = useState([])
    const [filterVisible, setFilterVisible] = useState(false);

    const onChange = (date, dateString) => {
        setMonth(dateString);
        setFetchReportCheck(false);
    };
    
    useEffect(() => {
        apiClient.get('/api/queue').then(res=> setQueues(res.data)).catch(err=> console.log(err.response))
    }, []);


    const fetchReport = ({queue}) => {
        setLoading(true);
        apiClient
            .post(`/api/report/getCallQueueSummaryReport`, { month: month,queue })
            .then((resp) => {
                setData(resp.data);
                setLoading(false);
                setFetchReportCheck(true);
                form.resetFields();
            });
    };

    return (
        <>
            {/* <Card
                title="Call Queue Summary Monthly"
                style={{ background: "#fff", borderRadius: "2px", padding: 10 }}
                extra={
                    <Form
                        form={form}
                        layout="inline"
                        size="large"
                        style={{ marginTop: "3px" }}
                        onFinish={fetchReport}
                    >
                        <Form.Item name={'queue'} rules={[{required:true,message:'please select queue first'}]}>
                            <Select placeholder="Select Queue.">
                                {queues.length > 0 && queues.map((queue)=><Select.Option key={queue.name} value={queue.name}>{queue.name}</Select.Option>)}
                            </Select>
                        </Form.Item>
                        <Form.Item name={"picker"}>
                            <DatePicker
                                onChange={onChange}
                                value={month}
                                picker="month"
                            />
                        </Form.Item>
                        <Form.Item>
                            <Button
                                size="large"
                                htmlType="submit"
                                // onClick={fetchReport}
                                disabled={fetchReportCheck}
                            >
                                Fetch Report
                            </Button>
                        </Form.Item>
                        <Form.Item>
                            <CSVLink filename="Call Queue Summary Monthly.csv" data={data} headers={exportHeaders}>
                                <Button disabled={data.length === 0}>Export Report</Button>
                            </CSVLink>
                        </Form.Item>
                    </Form>
                }
            >
                <Table
                    columns={columns}
                    dataSource={data}
                    pagination={false}
                    loading={loading}
                    bordered
                    size="middle"
                    scroll={{
                        x: "calc(600px + 50%)",
                        y: 540,
                    }}
                />
            </Card> */
            }
                  <Spin spinning={loading}>
                <Table title={() =>
                    <>
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        Monthly Report Bykea
                            <Space>
                                <Button
                                    danger
                                    type="primary"
                                    icon={<ReloadOutlined />}
                                    onClick={() => {
                                        setData([]);
                                    }}
                                >Reset Filter</Button>
                                <Button
                                    icon={<FilterOutlined />}
                                    onClick={() => {
                                        setFilterVisible(true);
                                    }} >Filter</Button>

                                <CSVLink filename="MonthlyReportBykea.csv"
                                    data={data}
                                    target="_blank" >
                                    <Button
                                        disabled={data.length == 0}
                                        type="primary"
                                        icon={<DownloadOutlined />}>
                                        Download CSV
                                    </Button>
                                </CSVLink>
                            </Space>
                        </div>
                    </>

                }
                    dataSource={data}
                    columns={columns}
                    scroll={{ x: 1100 }} />
            </Spin>
            <MonthlyReportBykeaFilter visible={filterVisible} setVisible={setFilterVisible} setMonth={setMonth} month={month} setFetchReportCheck={setFetchReportCheck} setFilterVisible={setFilterVisible}   loading={loading} setLoading={setLoading} data={data} setData={setData}  queues={queues} setQueues={setQueues} />

        </>
    );
};

export default MonthlyReportBykea;

const MonthlyReportBykeaFilter = ({ visible, setVisible,month, resetFilter, setMonth,setFetchReportCheck, setLoading, setFilterVisible,loading, data, setData}) => {

    const [form] = Form.useForm()
    const [queues, setQueues] = useState([])
    const [queue, setQueueOption] = useState("");
    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data))
    }, [])
    useEffect(() => {
        console.log('monthtest',month)
    }, [month])
    var handleChange = (queue) => {
        console.log(queue, 'queueoption');
        setQueueOption(queue);
    };

    const fetchReport = () => {
        setLoading(true);
        apiClient
            .post(`/api/report/getCallQueueSummaryReport`, { month: month,queue })
            .then((resp) => {
                setData(resp.data);
                setLoading(false);
                setFetchReportCheck(true);
                form.resetFields();
            });
    };

    const onChange = (date, dateString) => {
        setMonth(dateString);
        console.log('month',month)
        setFetchReportCheck(false);
    };
    


    useEffect(() => {
        if (resetFilter) {
            form.resetFields()
        }
    }, [resetFilter])


    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            title="Monthly Report Bykea Filter"
            size="small"
            okText="Submit"
            onOk={() => {
                form.validateFields()
                    .then((values) => { fetchReport()
                        form.resetFields();
                    })

                    .catch(e => console.log(e))

                setVisible(false)

            }
            }
        // oKButtonProps={{
        //     loading: buttonLoading
        // }}
        >
            <Card style={{ marginBottom: 10 }} bordered={false} >
                <Form form={form} labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                <Form.Item name={"queue"} label={"Queue"} >
                <Select placeholder="Select Queue" onChange={handleChange} style={{ width: '50%' }}>
                    {queues && queues.map((queue, index) => <Select.Option key={index} value={queue.name}>
                        {queue.name}
                    </Select.Option>)}
                </Select>
            </Form.Item>       
                        <Form.Item name={"picker"} label={"picker"}>
                            <DatePicker
                                onChange={onChange}
                                value={month}
                                picker="month"
                            />
                        </Form.Item>
                    {/* <Form.Item wrapperCol={{ span: 16, offset: 8 }}>
                        <Button type="primary" htmlType="submit">Submit</Button>
                    </Form.Item> */}
                </Form>
            </Card>
        </Modal>
    )
}