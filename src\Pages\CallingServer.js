import React, { useState, useEffect } from "react";
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  message,
  Popconfirm,
  Space,
  Card,
  Row,
  Col,
  Typography,
  Divider,
  Statistic,
} from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import apiClient from "../Shared/apiClient";

const { Title } = Typography;

const ServerManagement = () => {
  const [servers, setServers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editData, setEditData] = useState(null);
  const [form] = Form.useForm();

  const fetchServers = async () => {
    setLoading(true);
    try {
      const response = await apiClient.get("/api/agentless/servers");
      setServers(response.data.servers);
    } catch (error) {
      message.error(error?.response?.data?.message || "Failed to fetch servers");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      if (editData) {
  
        const response = await apiClient.put(`/api/agentless/servers/${editData.id}`, values);
        message.success(response?.data?.message);
        fetchServers()
      } else {
        
        const response = await apiClient.post("/api/agentless/servers", values);
        message.success(response?.data?.message);
        fetchServers()
      }
      setIsModalVisible(false);
      setEditData(null);
      form.resetFields(); 
      fetchServers(); 
    } catch (error) {
      message.error(error?.response?.data?.message || "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      const response = await apiClient.delete(`/api/agentless/servers/${id}`);
      message.success(response?.data?.message);
      fetchServers();
    } catch (error) {
      message.error(error?.response?.data?.message || "Failed to delete server");
    }
  };

  const columns = [
    { title: "ID", dataIndex: "id", key: "id" },
    { title: "Host", dataIndex: "host", key: "host" },
    { title: "Port", dataIndex: "port", key: "port" },
    { title: "Scheme", dataIndex: "scheme", key: "scheme" },
    { title: "Username", dataIndex: "username", key: "username" },
    { title: "Secret", dataIndex: "secret", key: "secret" },
    { title: "Connect Timeout", dataIndex: "connect_timeout", key: "connect_timeout" },
    { title: "Read Timeout", dataIndex: "read_timeout", key: "read_timeout" },
    { title: "Allowed Limit", dataIndex: "allowed_limit", key: "allowed_limit" },
    { title: "Trunk", dataIndex: "trunk", key: "trunk" },
    { title: "Caller ID", dataIndex: "caller_id", key: "caller_id" },
    { title: "Context", dataIndex: "context", key: "context" },
    { title: "Timeout", dataIndex: "timeout", key: "timeout" },
    {
      title: "Action",
      key: "action",
      render: (_, record) => (
        <Space>
          <Button
            icon={<EditOutlined />}
            onClick={() => {
              setEditData(record);
              setIsModalVisible(true);
              form.setFieldsValue(record);
            }}
          >
            Edit
          </Button>
          <Popconfirm
            title="Are you sure to delete this server?"
            onConfirm={() => handleDelete(record.id)}
          >
            <Button icon={<DeleteOutlined />} danger>
              Delete
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    fetchServers();
  }, []);

  return (
    <div style={{ padding: "20px" }}>
      {/* Server Cards Section */}
      <Divider orientation="center">Servers</Divider>
      <Row style={{ textAlign: "center" }} gutter={[20, 20]}>
        {servers.map((server) => (
          <Col key={server.id} span={8}>
            <Card>
              <Statistic
                title={<Typography.Title level={4}>Server {server.id}</Typography.Title>}
                value={server.host}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* Server Management Table */}
      <Card
        title="Server Management"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setIsModalVisible(true);
              setEditData(null);
              form.resetFields();
            }}
          >
            Add Server
          </Button>
        }
        style={{ marginTop: "20px" }}
      >
        <Table
          columns={columns}
          dataSource={servers}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 5 }}
          scroll={{ x: 1500 }}
        />
      </Card>

      {/* Modal for creating/editing servers */}
      <Modal
        title={editData ? "Edit Server" : "Add Server"}
        visible={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setEditData(null);
          form.resetFields();
        }}
        footer={null}
        centered
        width={800}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Row gutter={16}>
            {/* Host and Port */}
            <Col span={12}>
              <Form.Item
                name="host"
                label="Host"
                rules={[{ required: true, message: "Please enter a host" }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="port"
                label="Port"
                rules={[{ required: true, message: "Please enter a port" }]}
              >
                <InputNumber style={{ width: "100%" }} />
              </Form.Item>
            </Col>

            {/* Scheme and Username */}
            <Col span={12}>
              <Form.Item
                name="scheme"
                label="Scheme"
                rules={[{ required: true, message: "Please enter a scheme" }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="username"
                label="Username"
                rules={[{ required: true, message: "Please enter a username" }]}
              >
                <Input />
              </Form.Item>
            </Col>

            {/* Secret and Connect Timeout */}
            <Col span={12}>
              <Form.Item
                name="secret"
                label="Secret"
                rules={[{ required: true, message: "Please enter a secret" }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="connect_timeout"
                label="Connect Timeout"
                rules={[{ required: true, message: "Please enter a connect timeout" }]}
              >
                <InputNumber style={{ width: "100%" }} />
              </Form.Item>
            </Col>

            {/* Read Timeout and Allowed Limit */}
            <Col span={12}>
              <Form.Item
                name="read_timeout"
                label="Read Timeout"
                rules={[{ required: true, message: "Please enter a read timeout" }]}
              >
                <InputNumber style={{ width: "100%" }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="allowed_limit"
                label="Allowed Limit"
                rules={[{ required: true, message: "Please enter an allowed limit" }]}
              >
                <InputNumber style={{ width: "100%" }} />
              </Form.Item>
            </Col>

            {/* Trunk and Caller ID */}
            <Col span={12}>
              <Form.Item
                name="trunk"
                label="Trunk"
                rules={[{ required: true, message: "Please enter a trunk" }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="caller_id"
                label="Caller ID"
                rules={[{ required: true, message: "Please enter a caller ID" }]}
              >
                <Input />
              </Form.Item>
            </Col>

            {/* Context and Timeout */}
            <Col span={12}>
              <Form.Item
                name="context"
                label="Context"
                rules={[{ required: true, message: "Please enter a context" }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="timeout"
                label="Timeout"
                rules={[{ required: true, message: "Please enter a timeout" }]}
              >
                <InputNumber style={{ width: "100%" }} />
              </Form.Item>
            </Col>
          </Row>

          {/* Submit Button */}
          <Form.Item>
            <Button type="primary" htmlType="submit">
              {editData ? "Update" : "Create"}
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ServerManagement;