const initialState = {
    data: [],
    isLoading: false,
    errMess: '',
    columns: []
}

export const BreakReportReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case "BREAK_REPORT_LOADING":
            return { ...state, isLoading: true }
        case "BREAK_REPORT_SUCCESS":
            return { ...state, isLoading: false, data: action.payload, errMess: '' }
        case "BREAK_REPORT_FAILED":
            return { ...state, isLoading: false, errMess: action.payload }
        case "BREAK_REPORT_COLUMN":
            return { ...state, isLoading: false, errMess: '', columns: action.payload }
        case "BREAK_RESET":
            return { ...state, isLoading: false, data: [] }
    }
}