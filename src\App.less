@import '../node_modules/antd/dist/antd.less';
@import '~antd/dist/antd.compact.less';
.logo {
  height: 32px;
  margin: 16px;
  //background: rgba(255, 255, 255, 0.2);
}
.site-layout .site-layout-background {
  background: #fff;
}
.bg {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-image: url('Assets/bg-01.jpg');
}

.ql-editor{
  min-height:200px;
}

@primary-color: #15347c;
@info-color: #15347c;
@warning-color: #ff9f43;
@error-color: #ea5455;
@component-background: #ffffff;
@text-color: #000;
@layout-header-background: #F4F5F6;
@layout-sider-background: #F4F5F6;
@font-size-base: 15.4px;
@border-radius-base: 7px;
@btn-font-weight: 450;
@btn-shadow: 10px 12px 10px rgba(0, 0, 0, 0.1);
@btn-text-shadow: 0 -2px 0 rgba(0, 0, 0, 0.12);
@btn-padding-base: -0 @padding-md - 1px;
@btn-height-base: 36px;
@menu-dark-color: @text-color;
@card-background: #ffffff;
@card-shadow: 10px 12px 10px rgba(0, 0, 0, 10.1);
@switch-shadow-color: fade(#00230b, 90%);
@menu-inline-toplevel-item-height : 50px;
@menu-item-active-border-width : 5px;
@menu-item-active-bg : @primary-1;
@menu-highlight-color : @primary-color;
@tooltip-bg: @primary-color;
// @card-head-background : @primary-color;
// @card-head-color : white;
@card-head-color : black;