import { useDispatch, useSelector } from "react-redux";
import { useEffect, useRef, useState } from "react";
import { openNotificationWithIcon } from "../../Shared/notification";
import { Button, DatePicker, Form, Input, Modal, Select, Space, Spin, Table } from "antd";
import {
    CloudDownloadOutlined,
    DownloadOutlined,
    FilterOutlined,
    ReloadOutlined,
    SaveOutlined,
    SearchOutlined,
    SyncOutlined
} from "@ant-design/icons";
import { CSVLink } from "react-csv";
import { getHoldTimeReport, getHoldTimeReportFiltered, resetHoldTimeReport } from "../../Actions/HoldTimeReportAction";
import Highlighter from "react-highlight-words";
import apiClient from "../../Shared/apiClient";

export const HoldTimeReport = () => {

    const holdTimeReportReducer = useSelector(state => state.HoldTimeReportReducer)
    const [showFilter, setShowFilter] = useState(false)
    const [resetFilter, setResetFilter] = useState(false)
    const [visible, setVisible] = useState(false)
    const dispatch = useDispatch()
    const agentState = useSelector(state => state.AgentReducer)

    // useEffect(() => {
    //     dispatch(getAgent())
    //     dispatch(getHoldTimeReport(null))
    // }, [])
    console.log("check", holdTimeReportReducer.data)

    useEffect(() => {
        if (holdTimeReportReducer.errMess !== '') openNotificationWithIcon('error', holdTimeReportReducer.errMess)
    }, [holdTimeReportReducer.errMess])

    const resetFormFilter = () => {
        // dispatch(getHoldTimeReport(null))
        dispatch(resetHoldTimeReport());
        setResetFilter(true)
    }

    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')

    const searchInput = useRef()

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }


    const columns = [
        {
            title: 'Queue',
            dataIndex: 'queue',
            key: 'queue',
            ...getColumnSearchProps('queue')
            // render: (text, record) => record.recordingfile !== '' ? <Button href={process.env.REACT_APP_baseURL + '/api/download/' + record.recordingfile} target="_blank" type="primary" block icon={<DownloadOutlined />}/> : ''
        },
        {
            title: '0-10',
            dataIndex: '0-10',
            key: '0-10',
            ...getColumnSearchProps('0-10')
        },
        {
            title: '11-20',
            dataIndex: '11-20',
            key: '11-20',
            ...getColumnSearchProps('11-20')
        },
        {
            title: '21-30',
            dataIndex: '21-30',
            key: '21-30',
            ...getColumnSearchProps('21-30')
        },
        {
            title: '31-40',
            dataIndex: '31-40',
            key: '31-40',
            ...getColumnSearchProps('31-40')
        },
        {
            title: '41-50',
            dataIndex: '41-50',
            key: '41-50',
            ...getColumnSearchProps('41-50')
        },
        {
            title: '51-60',
            dataIndex: '51-60',
            key: '51-60',
            ...getColumnSearchProps('51-60')
        },
        {
            title: '> 60',
            dataIndex: '61 >',
            key: '61 >',
            ...getColumnSearchProps('61 >')
        },
        {
            title: 'avg waiting time',
            dataIndex: 'avg_wait_time',
            key: 'avg_wait_time',
            ...getColumnSearchProps('avg_wait_time')

        },
        {
            title: 'Longest wait',
            dataIndex: 'longest_wait_time',
            key: 'longest_wait_time',
            ...getColumnSearchProps('longest_wait_time')
        },
        {
            title: 'Total',
            dataIndex: 'total',
            key: 'total',
            ...getColumnSearchProps('total')
        }
    ]

    return (
        <>
            <Table
                title={data =>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        Queue Wait Time
                        Report
                        <Space>
                            <Button onClick={() => resetFormFilter()} type="danger" icon={<ReloadOutlined />}>Reset Filter</Button>
                            <Button onClick={() => setShowFilter(true)} icon={<FilterOutlined />}>Filter</Button>
                            <CSVLink data={holdTimeReportReducer.data} filename="WaitTimereport.csv">
                                <Button icon={<CloudDownloadOutlined />} type="primary" disabled={holdTimeReportReducer.data.length == 0}>Download</Button>
                            </CSVLink>
                        </Space>
                    </div>}
                dataSource={holdTimeReportReducer.data}
                columns={columns}
                scroll={{ x: 1100 }}
                bordered
                loading={{ spinning: holdTimeReportReducer.isLoading, indicator: <SyncOutlined spin /> }}
            />
            {/*<PlayAudio visible={visible} setVisible={setVisible} />*/}
            <HoldTimeReportFilter resetFilter={resetFilter} btnLoading={holdTimeReportReducer.isLoading} visible={showFilter} setVisible={setShowFilter} />
        </>
    )
}

export const HoldTimeReportFilter = ({ visible, setVisible, btnLoading, resetFilter }) => {

    const [form] = Form.useForm()
    const dispatch = useDispatch()

    useEffect(() => form.resetFields(), [resetFilter])
    const [queues, setQueues] = useState([])

    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data))
    }, [])

    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            okText="Submit"
            onOk={() => form.validateFields()
                .then(value => {
                    dispatch(getHoldTimeReport(value))
                    setVisible(false)
                })
            }
            okButtonProps={{
                loading: btnLoading,
                icon: <SaveOutlined />
            }}
            title="Wait Time Report Filter"
        >
            <Form
                form={form}
                layout="vertical"
            >
                <Form.Item name="date" label="Date">
                    <DatePicker.RangePicker style={{ width: '100%' }} />
                </Form.Item>
                <Form.Item colon={false} name="queue" label="Queue" >
                    <Select placeholder="Select Queue" >
                        {queues && queues.map((queue, index) => <Select.Option key={index} value={queue.name}>
                            {queue.name}
                        </Select.Option>)}
                    </Select>
                </Form.Item>
                {/*<Form.Item name="status" label="Status">*/}
                {/*    <Select>*/}
                {/*        <Select.Option>All</Select.Option>*/}
                {/*        <Select.Option value="successfull">Successfull</Select.Option>*/}
                {/*        <Select.Option value="abanded">Abanded</Select.Option>*/}
                {/*    </Select>*/}
                {/*</Form.Item>*/}
            </Form>
        </Modal>
    )
}