@font-face {
  font-family: '<PERSON><PERSON>';
  src: url(Assets/fonts/Gilroy-Regular.ttf);
}

@font-face {
  font-family: 'Gilroy-light';
  src: url(Assets/fonts/Gilroy-Light.ttf);
}
@font-face {
  font-family: '<PERSON>roy-Bold';
  src: url(Assets/fonts/Gilroy-Bold.ttf);
}
@font-face {
  font-family: 'Gilroy-Extra-Bold';
  src: url(Assets/fonts/Gilroy-ExtraBold.otf);
}
@font-face {
  font-family: 'Gilroy-Extra-Heavy';
  src: url(Assets/fonts/Gilroy-Heavy.ttf);
}
@font-face {
  font-family: '<PERSON>roy-Extra-Medium';
  src: url(Assets/fonts/Gilroy-Medium.ttf);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Gilroy-Light';
}
.ant-menu-overflow {
  display: 'inline-block'!important;
  /* flex-direction: row-reverse!important; */
}

.ant-card-head {
  min-height: 58px;
}

.hideOnMobile {
  display: none;
}
@media only screen and (min-width: 768px) {
  .hideOnMobile {
    display: block;
  }
}

.hideOnDesktop {
  display: block;
}
@media only screen and (min-width: 768px) {
  .hideOnDesktop {
    display: none;
  }
}

.ant-drawer-body {
  padding: 0;
}