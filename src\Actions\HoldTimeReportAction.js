import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";

export const getHoldTimeReport = data => dispatch => {
    dispatch(holdTimeReportLoading())
    apiClient.post(`/api/report/hold-time`, data).then(r => dispatch(holdTimeReportSuccess(r.data))).catch(e => dispatch(holdTimeReportFailed(handleError(e)))).catch(e => dispatch(holdTimeReportFailed(handleError(e))))
}

// export const getHoldTimeReportFiltered = data => dispatch => {
//     dispatch(holdTimeReportLoading())
//     apiClient.post(`/api/report/hold-time-report-filtered`, data).then(r => dispatch(holdTimeReportSuccess(r.data))).catch(e => dispatch(holdTimeReportFailed(handleError(e)))).catch(e => dispatch(holdTimeReportFailed(handleError(e))))
// }

export const resetHoldTimeReport = () => dispatch => {

    dispatch(holdTimeReportReset())
}

const holdTimeReportSuccess = data => ({
    type: "HOLD_TIME_REPORT_SUCCESS",
    payload: data
})

const holdTimeReportFailed = err => ({
    type: "HOLD_TIME_REPORT_FAILED",
    payload: err
})

const holdTimeReportLoading = () => ({
    type: "HOLD_TIME_REPORT_LOADING"
})
const holdTimeReportReset = () => (
    {
        type: "RESET_TIME_REPORT"
    }
)