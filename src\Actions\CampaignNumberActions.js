import apiClient from "../Shared/apiClient";
import { CAMPAIGN } from "../Endpoints/CampaignRoutes";
import * as ActionTypes from "../Constants/CampaignNumberConstants";

const endpoint = "campaignNumber";

const handleError = (error) => {
    if (error.response.data && error.response.data.message)
        return error.response.data.message;
    else if (error.response) {
        return error.response.data;
    } else if (error?.message) {
        return error.message
    } else {
        return error;
    }
};

export const getCampaignNumbers = (campaign, page, payload) => (dispatch) => {
    dispatch(campaignNumberLoading());
    apiClient
        .get(`/api/${campaign}/${endpoint}?page=${page}`, payload)
        .then((r) => dispatch(campaignNumbersSuccess(r.data)))
        .catch((e) => dispatch(campaignNumberFailed(handleError(e))))
        .catch((e) => dispatch(campaignNumberFailed(handleError(e))));
};

export const postCampaignNumbers = (campaign, data) => (dispatch) => {
    dispatch(campaignNumberLoading());
    apiClient
        .post(`/api/${campaign}/${endpoint}`, data)
        .then((r) => dispatch(campaignNumberSuccess(r.data)))
        .then(() => dispatch(getCampaignNumbers(campaign)))
        .catch((e) => {
            if (e) {
                dispatch(campaignNumberFailed(e || "Network Error!"))
            }
        })
};

export const patchCampaignNumbers = (campaign, campaignNumber, data) => (
    dispatch
) => {
    dispatch(campaignNumberLoading());
    apiClient
        .patch(`/api/${campaign}/${endpoint}/${campaignNumber}`, data)
        .then((r) => dispatch(campaignNumberSuccess(r.data)))
        .then(() => dispatch(getCampaignNumbers(campaign)))
        .catch((e) => dispatch(campaignNumberFailed(handleError(e))))
        .catch((e) => dispatch(campaignNumberFailed(handleError(e))));
};

export const deleteCampaignNumbers = (campaign, campaignNumber) => (
    dispatch
) => {
    dispatch(campaignNumberLoading());
    apiClient
        .delete(`/api/${campaign}/${endpoint}/${campaignNumber}`)
        .then((r) => dispatch(campaignNumberSuccess(r.data)))
        .then(() => dispatch(getCampaignNumbers(campaign)))
        .catch((e) => dispatch(campaignNumberFailed(handleError(e))))
        .catch((e) => dispatch(campaignNumberFailed(handleError(e))));
};

const campaignNumberLoading = () => ({
    type: ActionTypes.NUMBERS_LOADING,
});

const campaignNumbersSuccess = (data) => ({
    type: ActionTypes.ALL_NUMBERS,
    payload: data,
});

const campaignNumberSuccess = (message) => ({
    type: ActionTypes.NUMBERS_SUCCESS,
    payload: message,
});

const campaignNumberFailed = (errMess) => ({
    type: ActionTypes.NUMBERS_FAILED,
    payload: errMess,
});
