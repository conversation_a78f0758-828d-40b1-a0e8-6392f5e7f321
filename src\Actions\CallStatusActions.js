import * as ActionTypes from "../Constants/CallStatusConstants"
import apiClient from "../Shared/apiClient";
import {handleError} from "../Shared/handleError";

export const getCallStatuses = () => dispatch => {
    dispatch(callStatusLoading())
    apiClient.get(`/api/callStatus`).then(r => dispatch(callStatusSuccess(r.data))).catch(e => dispatch(callStatusFailed(handleError(e)))).catch(e => dispatch(callStatusFailed(handleError(e))))
}

export const getAccountCodes = () => dispatch => {
    dispatch(callStatusLoading())
    apiClient.get(`/api/accountCodes`).then(r => dispatch(accountCodeSuccess(r.data))).catch(e => dispatch(accountCodeFailed(handleError(e))))
}

const callStatusLoading = () => ({
    type: ActionTypes.CALL_STATUS_LOADING
})

const callStatusSuccess = statuses => ({
    type: ActionTypes.CALL_STATUS_SUCCESS,
    payload: statuses
})

const callStatusFailed = err => ({
    type: ActionTypes.CALL_STATUS_FAILED,
    payload: err
})

const accountCodeSuccess = data => ({
    type: ActionTypes.ACCOUNT_CODES_SUCCESS,
    payload: data
})

const accountCodeFailed = err => ({
    type: ActionTypes.ACCOUNT_CODES_SUCCESS,
    payload: err
})