import * as ActionTypes from "../Constants/InboundAgentSummaryConstants"
import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";

export const getInboundSummaryReportColumns = () => dispatch => {
    dispatch(inboundSummaryLoading())
    apiClient.post(`/api/report/inbound-agent-summary-columns`).then(r => dispatch(inboundSummaryColumns(r.data))).catch(e => dispatch(inboundSummaryFailed(handleError(e))))
}

export const getFilteredInboundAgentSummary = (obj) => dispatch => {
    dispatch(inboundSummaryLoading())
    apiClient.post('api/report/inbound-agent-summary/filter', obj).then(r => dispatch(inboundSummarySuccess(r.data))).catch(e => dispatch(inboundSummaryFailed(handleError(e))))
}

export const getInboundSummaryReport = () => dispatch => {
    dispatch(inboundSummaryLoading())
    apiClient.post(`/api/report/inbound-agent-summary`).then(r => dispatch(inboundSummarySuccess(r.data))).catch(e => dispatch(inboundSummaryFailed(handleError(e))))
}

export const inboundAgentSumReset = () => dispatch => {
    dispatch(inboundAgentSummaryReset())

}

const inboundSummaryLoading = () => ({
    type: ActionTypes.INBOUND_AGENT_SUMMARY_LOADING
})

const inboundSummaryColumns = data => ({
    type: ActionTypes.INBOUND_AGENT_SUMMARY_COLUMNS,
    payload: data
})

const inboundSummarySuccess = data => ({
    type: ActionTypes.INBOUND_AGENT_SUMMARY_SUCCESS,
    payload: data
})

const inboundSummaryFailed = err => ({
    type: ActionTypes.INBOUND_AGENT_SUMMARY_FAILED,
    payload: err
})
const inboundAgentSummaryReset = () => ({
    type: ActionTypes.INBOUND_AGENT_SUMMARY_RESET,

})
