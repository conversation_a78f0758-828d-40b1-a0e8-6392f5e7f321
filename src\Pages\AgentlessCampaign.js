import React, { useState, useEffect } from "react";
import {
  But<PERSON>,
  Divider,
  Typography,
  Form,
  Input,
  message,
  Modal,
  Table,
  Card,
  Descriptions,
  Upload,
  Space,
  Popconfirm,
  Tag,
  TimePicker,
  DatePicker,
  Select,
  Checkbox,
  Row,
  Col,
} from "antd";
import {
  PlusOutlined,
  UploadOutlined,
  DeleteOutlined,
  StopOutlined,
  SyncOutlined,
  EyeOutlined,
  CloudDownloadOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from "@ant-design/icons";
import apiClient from "../Shared/apiClient";
import { render } from "@testing-library/react";
import { saveAs } from 'file-saver';
import echo from "../Shared/echo";
import ViewReportSummary from "../Components/ViewReportSummary";
const { RangePicker } = DatePicker;
const { Title } = Typography;
const { Text } = Typography;


const dayOptions = [
  { label: "Mon", value: "monday" },
  { label: "Tue", value: "tuesday" },
  { label: "Wed", value: "wednesday" },
  { label: "Thu", value: "thursday" },
  { label: "Fri", value: "friday" },
  { label: "Sat", value: "saturday" },
  { label: "Sun", value: "sunday" },
];


const CampaignPortal = () => {
  const [campaigns, setCampaigns] = useState([]);
  const [recordings, setRecordings] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [summaryVisible, setSummaryVisible] = useState(false);
  const [summaryData, setSummaryData] = useState({});
  const [form] = Form.useForm();


  const fetchCampaigns = async () => {
    // setLoading(true);
    try {
      echo.channel('campaigns')
        .listen('.campaigns', (data) => {
          console.log(data.campaigns);
          setCampaigns(data.campaigns);
        });
      const response = await apiClient.get("api/agentless/campaigns");
      setCampaigns(response.data);
    } catch (error) {
      message.error(error?.response?.data?.message ?? "Failed to fetch campaigns");
    } finally {
      setLoading(false);
    }
  };

  const fetchRecordings = async () => {
    try {

      const response = await apiClient.get("api/agentless/recordings");
      setRecordings(response.data);
    } catch (error) {
      message.error(error?.response?.data?.message ?? "Failed to fetch recordings");
    }
  };


  const handleCreate = async (values) => {
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append("file", values.file.fileList[0].originFileObj);
      formData.append("name", values.name);
      formData.append("recording", values.recording);
      formData.append("date_between", values.date_between.map(date => date.format("YYYY-MM-DD")).join(","));
      formData.append("time_between", values.time_from.format("HH:mm:ss"));
      formData.append("time_to", values.time_to.format("HH:mm:ss"));
      formData.append("days", values.days.join(","));

      const response = await apiClient.post("api/agentless/campaign", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      message.success(response?.data?.message ?? "Campaign created successfully");
      setIsModalVisible(false);
      form.resetFields();
      fetchCampaigns();
    } catch (error) {
      message.error(error?.response?.data.message ?? "Failed to create campaign");
    } finally {
      setLoading(false);
    }
  };

  // Handle campaign deletion
  const handleDelete = async (id) => {
    setLoading(true);
    try {
      const res = await apiClient.delete(`api/agentless/campaign/${id}`);
      message.success(res.data ?? "Campaign deleted successfully");
      fetchCampaigns();
    } catch (error) {
      message.error("Failed to delete campaign");
    } finally {
      setLoading(false);
    }
  };

  // Handle stopping a campaign
  const handleActiveCampaign = async (id) => {
    setLoading(true);
    try {
      const res = await apiClient.post(`api/agentless/campaign/${id}`);
      message.success(res?.data ?? "Campaign stopped successfully");
      fetchCampaigns();
    } catch (error) {
      message.error("Failed to stop campaign");
    } finally {
      setLoading(false);
    }
  };

  // Handle restarting a campaign
  const handleRestartCampaign = async (id) => {
    setLoading(true);
    try {
      const res = await apiClient.post(`api/agentless/restartCampaign/${id}`);
      message.success(res?.data ?? "Campaign restarted successfully");
      fetchCampaigns();
    } catch (error) {
      message.error(error?.response?.data ?? "Failed to restart campaign");
    } finally {
      setLoading(false);
    }
  };

  // Handle viewing campaign summary
  const handleViewSummary = async (id) => {
    setLoading(true);
    try {
      const response = await apiClient.get(`api/agentless/viewCampaignSummary/${id}`);
      setSummaryData(response.data);
      setSummaryVisible(true);
    } catch (error) {
      message.error("Failed to fetch campaign summary");
    } finally {
      setLoading(false);
    }
  };

  // Table columns
  const columns = [
    { title: "ID", dataIndex: "id", key: "id" },
    { title: "Name", dataIndex: "name", key: "name", width: 300, },
    {
      title: "Status",
      dataIndex: "stage",
      key: "stage",
      width: 200,
      render: (stage) => (
        <>
          <Tag
            color={stage === 'in progress' ? 'green' : stage === 'scheduled' ? 'warning' : 'error'}
            style={{ textTransform: 'uppercase' }}
          >
            {stage === 'cancelled' ? 'ended' : stage}
          </Tag>
        </>
      ),
    },
    {
      title: "Summary",
      key: "summary",
      width: 200,
      render: (_, record) => (
        <Button
          icon={<EyeOutlined />}
          onClick={() => handleViewSummary(record.id)}
        >
          Summary
        </Button>
      ),
    },
    {
      title: "File",
      key: "file",
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            size="large"
            title="Download"
            icon={<CloudDownloadOutlined />}
            onClick={() => {
              apiClient
                .get(`/api/agentless/campaign/${record.id}`)
                .then((res) => {
                  let numbersString = String(res.data).trim();
                  // console.log("Raw data:", numbersString);


                  let numbersArray =
                    numbersString.includes("\n") || numbersString.includes(",")
                      ? numbersString.split(/\s*[\n,]\s*/)
                      : [numbersString];

                  // console.log("Numbers Array:", numbersArray);

                  const csvContent = numbersArray.join("\n");
                  // console.log("CSV Content:", csvContent);

                  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
                  saveAs(blob, `${record.name}.csv`);
                })
                .catch((error) => {
                  console.error("Error fetching numbers:", error);
                });
            }}
          >
            Numbers
          </Button>
          
          <Button
            size="large"
            title="Download"
            icon={<CloudDownloadOutlined />}
            onClick={() => {
              apiClient.get(`/api/agentless/campaignExport/${record.id}`, {
                responseType: 'blob' 
              })
                .then(response => {
                  const url = window.URL.createObjectURL(new Blob([response.data]));
                  const link = document.createElement('a');
                  link.href = url;
                  link.setAttribute('download', `campaign_export.csv`);
                  document.body.appendChild(link);
                  link.click();
                  link.remove();
                })
                .catch(error => {
                  console.error("Export failed:", error);
                });
            }}
          >
            Export CSV
          </Button>
        </Space>

      ),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_, record) => (
        <Space>

          {!record.status ? <Popconfirm
            title="Are you sure you want to activate this campaign?"
            okText="Yes"
            cancelText="No"
            onConfirm={() => {
              apiClient.post(`/api/agentless/campaign/${record.id}`)
                .then(r => message.success(r.data))
                .catch(e => message.error(e.response.data.message))
            }
            }
          >
            <Button
              size="large"
              title="Activate"
              icon={<CheckCircleOutlined />}
              disabled={record.status === 1}
              type="primary"
              ghost
            />
          </Popconfirm> :
            <Popconfirm
              title="Are you sure you want to deactivate this campaign?"
              onConfirm={() => apiClient.post(`/api/agentless/campaign/${record.id}`)
                .then(r => {
                  message.success(r.data);
                  fetchCampaigns();
                })
                .catch(e => message.error(e?.response?.data))}
              okText="Yes"
              cancelText="No"
            >
              <Button
                size="large"
                title="Deactivate"
                icon={<CloseCircleOutlined />}
                disabled={record.status !== 1}
                danger
              />
            </Popconfirm>}
          {
            !record.terminated ? <Popconfirm
              title="Are you sure want to stop this campaign?"

              okText="Yes"
              cancelText="No"
            >
              <Button
                size="large"
                title="Stop"
                icon={<StopOutlined />}
              />
            </Popconfirm> : <Popconfirm
              title="Are you sure want to restart this campaign?"
              okText="Yes"
              cancelText="No"
            >
              <Button
                size="large"
                title="Restart"
                icon={<SyncOutlined />}
              />
            </Popconfirm>
          }
          <Popconfirm
            title="Are you sure want to delete this campaign?"
            okText="Yes"
            cancelText="No"
            onConfirm={() => handleDelete(record.id)}
          >
            <Button size="large" title="Delete" icon={<DeleteOutlined />} type="danger" />
          </Popconfirm>

        </Space>
      )
    },
  ];

  // Fetch data on component mount
  // useEffect(() => {
  //   const id = setInterval(() => {
  //     fetchCampaigns();
  // }, 5000)
  // return () => clearInterval(id)
  // }, []);


  useEffect(() => {
    fetchCampaigns();
    fetchRecordings();
  }, [])
  const renderDays = (daysString) => {
    // Split the days string into an array
    const selectedDays = daysString ? daysString.split(",") : [];

    return (
      <div>
        {dayOptions.map((day) => (
          <Checkbox
            key={day.value}
            checked={selectedDays.includes(day.value)}
          // disabled
          >
            {day.label}
          </Checkbox>
        ))}
      </div>
    );
  };

  return (
    <div style={{ padding: "20px" }}>
      {/* Campaign Overview Section */}
      <Divider orientation="center">Campaigns</Divider>

      {/* Campaign Management Table */}
      <Card
        title="Campaign Management"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsModalVisible(true)}
          >
            Add Campaign
          </Button>
        }
        style={{ marginTop: "20px" }}
      >

        <Table
          columns={columns}
          dataSource={campaigns}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 5 }}
          scroll={{ x: 800 }}
          expandable={{
            expandedRowRender: (record) => (
              <Descriptions bordered style={{ width: "100%" }}>
                <Descriptions.Item label="Name">{record.name}</Descriptions.Item>
                <Descriptions.Item label="Recording">{record.recording_name}</Descriptions.Item>
                <Descriptions.Item label="Date from">{record.date_from}</Descriptions.Item>
                <Descriptions.Item label="Date to">{record.date_to}</Descriptions.Item>
                <Descriptions.Item label="Time From">{record.time_from}</Descriptions.Item>
                <Descriptions.Item label="Time to">{record.time_to}</Descriptions.Item>
                {/* <Descriptions.Item label="Time to">
                   {record.time_from}
                </Descriptions.Item>
                <Descriptions.Item label="Time to">
                   {record.time_to}
                </Descriptions.Item> */}
                <Descriptions.Item label="Days"> {renderDays(record.days)}</Descriptions.Item>
              </Descriptions>
            ),
          }}
        />
      </Card>


      {/* Modal for adding campaigns */}
      <Modal
        title="Add Campaign"
        visible={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        okText="Submit"
        width={700}
      >
        <Form
          form={form}
          onFinish={handleCreate}
          name="create-campaign"
          labelCol={{
            span: 24,
          }}
          wrapperCol={{
            span: 24,
          }}
          layout="vertical"
          size="large"
        >
          {/* Campaign Name */}
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label="Name"
                rules={[
                  {
                    required: true,
                    message: "Please Insert Name",
                  },
                ]}
              >
                <Input placeholder="Enter campaign name" />
              </Form.Item>
            </Col>
          </Row>

          {/* Recording Selection */}
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="recording"
                label="Recording"
                rules={[
                  {
                    required: true,
                    message: "Please Select Recording",
                  },
                ]}
              >
                <Select placeholder="Select a recording">
                  {recordings.map((record, index) => (
                    <Select.Option key={index} value={record.file_name}>
                      {record.file_name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* CSV File Upload */}
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="File"
                name="file"
                rules={[
                  {
                    required: true,
                    message: "Please Upload CSV File",
                  },
                ]}
              >
                <Upload.Dragger
                  listType="picture"
                  maxCount={1}
                  beforeUpload={() => false}
                >
                  <Button icon={<UploadOutlined />}>Upload</Button>
                  <br />
                  <Text>CSV Format Only.</Text>
                  <br />
                  <Text>Number Format: 340xxxxxxx</Text>
                  <br />
                  <Text>Example: 3403331963</Text>
                </Upload.Dragger>
              </Form.Item>
            </Col>
          </Row>

          <Divider>Date Time Setting</Divider>

          {/* Date Range Picker */}
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Date Between"
                name="date_between"
                rules={[
                  {
                    required: true,
                    message: "Please Select Date Range",
                  },
                ]}
              >
                <RangePicker
                  format="YYYY-MM-DD"
                  style={{ width: "100%" }}
                  placeholder={["Start Date", "End Date"]}
                />
              </Form.Item>
            </Col>
          </Row>

          {/* Time From */}
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Time From"
                name="time_from"
                rules={[
                  {
                    required: true,
                    message: "Please Select Start Time",
                  },
                ]}
              >
                <TimePicker
                  format="HH:mm:ss"
                  style={{ width: "100%" }}
                  placeholder="Time From"
                  disabledHours={() => [0, 1, 2, 3, 4, 5, 6, 7, 20, 21, 22, 23]}
                />
              </Form.Item>
            </Col>


            {/* Time To */}
            <Col span={12}>
              <Form.Item
                label="Time To"
                name="time_to"
                rules={[
                  {
                    required: true,
                    message: "Please Select End Time",
                  },
                ]}
              >
                <TimePicker
                  format="HH:mm:ss"
                  style={{ width: "100%" }}
                  placeholder="Time To"
                  disabledHours={() => [0, 1, 2, 3, 4, 5, 6, 7, 20, 21, 22, 23]}
                />
              </Form.Item>
            </Col>
          </Row>

          {/* Days Selection */}
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Days"
                name="days"
                rules={[
                  {
                    required: true,
                    message: "Please Select Days",
                  },
                ]}
              >
                <Checkbox.Group options={dayOptions} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* Modal for viewing campaign summary */}
      {/* <Modal
        title="Campaign Summary"
        visible={summaryVisible}
        onCancel={() => setSummaryVisible(false)}
        footer={null}
      >
        <Descriptions bordered>
          <Descriptions.Item label="Total Calls">{summaryData.total_calls}</Descriptions.Item>
          <Descriptions.Item label="Successful Calls">{summaryData.successful_calls}</Descriptions.Item>
          <Descriptions.Item label="Failed Calls">{summaryData.failed_calls}</Descriptions.Item>
        </Descriptions>
      </Modal> */}
      <ViewReportSummary
        isOpen={summaryVisible}
        onClose={() => setSummaryVisible(false)}
        data={summaryData}
        loading={loading}
      />
    </div>
  );

};

export default CampaignPortal;