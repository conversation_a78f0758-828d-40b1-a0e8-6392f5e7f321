import { useState, useEffect } from "react";
import {
    Button,
    DatePicker,
    Form,
    Input,
    Modal,
    Pagination,
    Select,
    Space,
    Spin,
    Table,
    Tag,
} from "antd";
import { useDispatch, useSelector } from "react-redux";
import {
    deleteCampaignNumbers,
    getCampaignNumbers,
    patchCampaignNumbers,
} from "../../Actions/CampaignNumberActions";
import {
    CloseCircleOutlined,
    DeleteOutlined,
    DownloadOutlined,
    EditOutlined,
    FilterOutlined,
} from "@ant-design/icons";
import { useParams } from "react-router";
import { openNotificationWithIcon } from "../../Shared/notification";
import EditCampaignNumber from "./EditCampaignNumber";
import apiClient from "../../Shared/apiClient";

const CampaignNumbers = (props) => {
    let { id } = useParams();
    let route = `${process.env.REACT_APP_baseURL}/api/${id}/campaignNumberExport`;
    const [dataSource, setDataSource] = useState([]);
    const [filterVisible, setFilterVisible] = useState(false);
    const [routeString, setRouteString] = useState(
        `${process.env.REACT_APP_baseURL}/api/${id}/campaignNumberExport`
    );
    const [resetFilter, setResetFilter] = useState(false);
    const dispatch = useDispatch();
    const state = useSelector((state) => state.CampaignNumberReducer);
    const [item, setItem] = useState(false);
    const [page, setPage] = useState(1);

    useEffect(() => {
        if (id) dispatch(getCampaignNumbers(id, page));
    }, [id, page]);

    useEffect(() => {
        state.numbers && setDataSource(state.numbers);
    }, [state.numbers]);

    useEffect(() => {
        state.message && openNotificationWithIcon("success", state.message);
    }, [state.message]);

    useEffect(() => {
        state.errMess && openNotificationWithIcon("error", state.errMess);
    }, [state.errMess]);

    const handleSubmit = (values) => {
        setItem(false);
        dispatch(patchCampaignNumbers(id, item.id, values));
    };

    const columns = [
        {
            title: "id",
            dataIndex: "id",
            key: "id",
        },
        {
            title: "Number",
            dataIndex: "number",
            key: "number",
        },
        {
            title: "Name",
            dataIndex: "name",
            key: "name",
        },
        {
            title: "Location",
            dataIndex: "city",
            key: "city",
        },
        {
            title: "File",
            dataIndex: "file",
            key: "file",
        },
        {
            title: "Attempt(s)",
            dataIndex: "attempts",
            key: "attempts",
        },
        {
            title: "Dial Time",
            dataIndex: "dial_time",
            key: "dial_time",
        },
        {
            title: "Duration",
            dataIndex: "duration",
            key: "duration",
        },
        {
            title: "Bill Seconds",
            dataIndex: "billsec",
            key: "billsec",
        },
        {
            title: "Disposition",
            dataIndex: "disposition",
            key: "disposition",
            render: (text) => (text ? text : "UNCALLED"),
        },
        {
            title: "Created At",
            dataIndex: "created_at",
            key: "created_at",
        },
        {
            title: "Actions",
            key: "actions",
            render: (text, record) => (
                <Space size="middle">
                    <Button onClick={() => setItem(record)} icon={<EditOutlined />}>
                        Edit
                    </Button>
                    <Button
                        onClick={() => dispatch(deleteCampaignNumbers(id, record.id))}
                        type="primary"
                        danger
                        icon={<DeleteOutlined />}
                    >
                        Delete
                    </Button>
                </Space>
            ),
        },
    ];

    //   useEffect(() => {
    //     console.log("object dataSource", dataSource);
    //     console.log("object dataSource number", state);
    //   }, [dataSource]);

    const onPageChange = (pageNumber) => {
        setPage(pageNumber);
    };

    const resetFormFilter = () => {
        dispatch(getCampaignNumbers(id, 1));
        setResetFilter(true);
        setRouteString(
            `${process.env.REACT_APP_baseURL}/api/${id}/campaignNumberExport`
        );
    };

    return (
        <>
            <div style={{ textAlign: "right", marginBottom: "10px" }}>
                <Space>
                    <Button
                        onClick={() => resetFormFilter()}
                        type="primary"
                        danger
                        icon={<CloseCircleOutlined />}
                    >
                        Reset Filter
                    </Button>
                    <Button
                        onClick={() => setFilterVisible(true)}
                        icon={<FilterOutlined />}
                    >
                        Filter
                    </Button>
                    {dataSource?.data && (
                        <Button
                            href={routeString}
                            target="_blank"
                            icon={<DownloadOutlined />}
                        >
                            Download CSV
                        </Button>
                    )}
                </Space>
            </div>
            <Table
                loading={state.isLoading}
                dataSource={dataSource?.data || []}
                columns={columns}
                scroll={{ x: 1100 }}
                pagination={false}
            />
            <Pagination
                total={dataSource?.total}
                showSizeChanger={false}
                defaultPageSize={15}
                // pageSize={15}
                showQuickJumper
                showTotal={(total, range) => `${total} total item(s)`}
                onChange={onPageChange}
                style={{ float: "right", marginTop: "1rem" }}
            />
            <EditCampaignNumber
                onSubmit={(values) => handleSubmit(values)}
                onCancel={() => setItem(false)}
                item={item}
            />
            <CampaignNumberFilter
                resetField={resetFilter}
                visible={filterVisible}
                setVisible={setFilterVisible}
                id={id}
                page={page}
                setDataSource={setDataSource}
                route={route}
                setRouteString={setRouteString}
            />
        </>
    );
};

const CampaignNumberFilter = ({
    visible,
    setVisible,
    resetField,
    setDataSource,
    id,
    page,
    setRouteString,
    route,
}) => {
    const [dateRange, setDateRange] = useState([]);
    const [rangeString, setRangeString] = useState("");
    const [form] = Form.useForm();

    useEffect(() => form.resetFields(), [resetField]);

    return (
        <Modal
            title="Filter"
            onCancel={() => {
                setVisible(false);
            }}
            visible={visible}
            onOk={async () => {
                try {
                    const values = await form.validateFields();
                    const result = await apiClient.post(
                        `/api/${id}/filterCampaignNumber?page=${page}`,
                        dateRange.length > 0 ? { ...values, range: dateRange } : values
                    );
                    route = `${process.env.REACT_APP_baseURL}/api/${id}/campaignNumberExport?`;
                    if (dateRange) {
                        route = `${route}start=${dateRange[0]}&end=${dateRange[1]}&`;
                    }
                    if (values.disposition) {
                        route = `${route}disposition=${values.disposition}&`;
                    }
                    if (values.number) {
                        route = `${route}number=${values.number}`;
                    }
                    setRouteString(route);
                    setDataSource(result?.data);
                    setVisible(false);
                    form.resetFields();
                } catch (e) {
                    console.log(e);
                }
            }}
        >
            <Form form={form} layout="vertical">
                <Form.Item name="range" label="Date">
                    <DatePicker.RangePicker
                        style={{ width: "100%" }}
                        onChange={(_, dateString) => {
                            setDateRange(dateString);
                        }}
                    />
                </Form.Item>
                <Form.Item name="disposition" label="Disposition">
                    <Select>
                        <Select.Option value="ANSWERED">Answered</Select.Option>
                        <Select.Option value="NO ANSWER">Not Answered</Select.Option>
                    </Select>
                </Form.Item>
                <Form.Item
                    name="number"
                    label="Number"
                    rules={[
                        {
                            pattern: new RegExp(/[0-9]/),
                            message: "Field accepts numbers only.",
                        },
                    ]}
                >
                    <Input placeholder="Start without the initial 0. eg. 333-1234567" />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default CampaignNumbers;
