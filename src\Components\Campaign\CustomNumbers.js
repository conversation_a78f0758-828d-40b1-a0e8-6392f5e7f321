import {useEffect, useState} from "react";
import {useDispatch, useSelector} from "react-redux";
import {useParams} from "react-router";
import {openNotificationWithIcon} from "../../Shared/notification";
import {deleteCustomNumbers, getCustomNumbers, patchCustomNumbers} from "../../Actions/CustomNumberActions";
import {Button, Space, Table, Tag} from "antd";
import {DeleteOutlined, EditOutlined} from "@ant-design/icons";
import {deleteCampaignNumbers} from "../../Actions/CampaignNumberActions";
import {EditCustomNumber} from "./EditCustomNumber";

export const CustomNumbers = () => {
    const [dataSource, setDataSource] = useState([])
    const dispatch = useDispatch()
    const state = useSelector(state => state.CustomNumberReducer)
    const [item, setItem] = useState(false)

    let { id } = useParams()

    useEffect(() => {
        if(id)
            dispatch(getCustomNumbers(id))
    }, [id])

    useEffect(() => {
        state.numbers && setDataSource(state.numbers)
    }, [state.numbers])

    useEffect(() => {
        state.message && openNotificationWithIcon('success', state.message)
    }, [state.message]);

    useEffect(() => {
        state.errMess && openNotificationWithIcon('error', state.errMess)
    }, [state.errMess]);

    const handleSubmit = values => {
        setItem(false)
        dispatch(patchCustomNumbers(id, item.id, values))
    }

    const columns = [
        {
            title: 'id',
            dataIndex: 'id',
            key: 'id'
        },
        {
            title: 'Retailer Name',
            dataIndex: 'retailer_name',
            key: 'retailer_name'
        },
        {
            title: 'Shop Name',
            dataIndex: 'shop_name',
            key: 'shop_name'
        },
        {
            title: 'Agent ID',
            dataIndex: 'agent_id',
            key: 'agent_id'
        },
        {
            title: 'Inactive days',
            dataIndex: 'inactive_days',
            key: 'inactive_days'
        },
        {
            title: 'Target',
            dataIndex: 'target',
            key: 'target'
        },
        {
            title: 'GMV',
            dataIndex: 'gmv',
            key: 'gmv'
        },
        {
            title: 'Number of Unique SKUs sold',
            dataIndex: 'number_of_unique_sku_sold',
            key: 'number_of_unique_sku_sold'
        },
        {
            title: 'Attempts',
            dataIndex: 'attempts',
            key: 'attempts'
        },
        {
            title: 'Customer Phone',
            dataIndex: 'customer_phone',
            key: 'customer_phone'
        },
        {
            title: 'Status',
            dataIndex: 'status',
            key: 'status',
            render: text => text === 0 ? <Tag color="red">UNCALLED</Tag> : <Tag color="green">CALLED</Tag>
        },
        {
            title: 'File',
            dataIndex: 'file',
            key: 'file'
        },
        {
            title: 'Actions',
            key: 'actions',
            render: (text, record) => (
                <Space size="middle">
                    <Button onClick={() => setItem(record)} icon={<EditOutlined />}>Edit</Button>
                    <Button onClick={() => dispatch(deleteCustomNumbers(id, record.id))} type="primary" danger icon={<DeleteOutlined />}>Delete</Button>
                </Space>
            )
        }
    ]

    return(
        <>
            <Table loading={state.isLoading} dataSource={dataSource} columns={columns} scroll={{x:1100}} />
            <EditCustomNumber onSubmit={values => handleSubmit(values)} onCancel={() => setItem(false)} item={item} />
        </>
    )
}