import { useDispatch, useSelector } from "react-redux";
import {
    Button,
    Form,
    notification,
    Space,
    Spin,
    Modal,
    Input,
    Table,
    Tooltip,
} from "antd";
import {
    DeleteOutlined,
    EditTwoTone,
    EditOutlined,
    IdcardTwoTone,
    PlusCircleOutlined,
    UserOutlined,
} from "@ant-design/icons";
import EditAgent from "../Components/Agent/EditAgent";
import AddAgent from "../Components/Agent/AddAgent";
import { useState, useEffect, useMemo, useRef } from "react";
import {
    createAgent,
    deleteAgent,
    getAgent,
    updateAgent,
} from "../Actions/AgentActions";
import AssignRole from "../Components/Agent/AssignRole";
import { assignRoleToUser } from "../Actions/RoleActions";
import Text from "antd/es/typography/Text";
import apiClient from "../Shared/apiClient";
import { openNotificationWithIcon } from "../Shared/notification";

const { Column } = Table;

const Agent = () => {
    const Agent = useSelector((state) => state.AgentReducer);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRecord, setSelectedRecord] = useState(null);
    const [editVisible, setEditVisible] = useState(false);
    const [createVisible, setCreateVisible] = useState(false);
    const [assignRole, setAssignRole] = useState(false);
    const [passwordModal, setPasswordModal] = useState(false);
    const [id, setId] = useState(false);
    const [userLimit, setUserLimit] = useState()


    const [role, setRole] = useState(sessionStorage.getItem('username'));
    console.log("role", role)

    const dispatch = useDispatch();
    const [form] = Form.useForm();

    // const openNotificationWithIcon = (type, message) => {
    //     notification[type]({
    //         message: type === "error" ? "Error" : "Success",
    //         description: message,
    //     });
    // };
    const handleLimit = async () => {
        console.log("limit", userLimit)
        const response = await apiClient.patch('api/user-limit', { agent_limit: userLimit })
        if (response?.data?.status == 200 && response.data) {
            console.log("limit response", response.data)
            openNotificationWithIcon('success', response?.data?.message)
        }
        else {
            console.log("Error", response)
            // openNotificationWithIcon('error', response?.message)
        }
        getUserLimit()
    }

    const getUserLimit = () => {
        apiClient.get('api/user-limit').then((r) => {
            console.log("res", r.data[0].agent_limit)
            setUserLimit(r.data[0].agent_limit)

        }).catch((e) => {
            console.log("Error", e)
        })
    }



    useEffect(() => {
        getUserLimit()
    }, [])

    useEffect(() => {
        if (createVisible) {
            form.resetFields();
        }
    }, [createVisible]);

    useEffect(() => {
        if (Agent.message) {
            dispatch(getAgent("?type=all"));
            form.resetFields();
            setCreateVisible(false);
            openNotificationWithIcon("success", Agent.message);
        }
    }, [Agent.message]);

    useEffect(() => {
        dispatch(getAgent("?type=all"));
    }, []);

    const prevErrorRef = useRef(null)

    useEffect(() => {

        if (Agent.errMess && Agent.errMess != prevErrorRef.current) {

            if (Agent.errMess?.data?.errors && Object.keys(Agent.errMess?.data?.errors).length > 0) {
                Object.keys(Agent.errMess?.data?.errors).map(key => openNotificationWithIcon('error', Agent.errMess?.data?.errors[key]))
            } else {
                openNotificationWithIcon('error', Agent.errMess?.message)
            }
            setSelectedRowKeys([])
            prevErrorRef.current = Agent.errMess
        }
        // console.log("Agent perm", Agent.errMess?.data?.errors)

    }, [Agent.errMess]);




    const onChangePassword = (values) => {
        apiClient
            .post(`/api/${id}/agent/password/reset`, values)
            .then((res) => {
                console.log('resp', res)
                openNotificationWithIcon("success", res.data.message);
                form.resetFields();
                setPasswordModal(!passwordModal);
            })
            .catch((err) => {
                openNotificationWithIcon("error", err.response?.data?.message);
                form.resetFields();
                setPasswordModal(!passwordModal);
            });
    };

    const rowSelection = {
        selectedRowKeys,
        onChange: onSelectChange,
        type: "radio",
    };

    function onSelectChange(keys) {
        setSelectedRecord(
            Agent.users.find((value, index, obj) => value.id === keys[0])
        );
        setSelectedRowKeys(keys);
    }

    function onCreate(values) {
        dispatch(createAgent(values));
        setEditVisible(false);
        setSelectedRowKeys([]);
    }

    function onUpdate(values) {
        // Remove the 'queue' property if the agent type is Outbound.
        if (values.type === "Outbound") {
            delete values.queue;
        }
        dispatch(updateAgent(selectedRecord.id, values));
        setSelectedRowKeys([]);
        setEditVisible(false);
    }

    // function onUpdate(values) {
    //     dispatch(updateAgent(selectedRecord.id, values));
    //     setSelectedRowKeys([]);
    //     setEditVisible(false);
    // }

    function onDelete() {
        dispatch(deleteAgent(selectedRecord.id));
    }

    function assignRoleUser(values) {
        dispatch(assignRoleToUser(selectedRecord.id, values));
        // setCreateVisible(false)
    }

    const hasSelected = selectedRowKeys.length > 0;
    const editable = selectedRowKeys.length === 1;

    return (
        <>
            <Spin spinning={Agent.isLoading}>
                <EditAgent
                    setVisible={setEditVisible}
                    onCancel={() => setEditVisible(false)}
                    visible={editVisible}
                    onCreate={onUpdate}
                    record={selectedRecord}
                />
                <AddAgent
                    form={form}
                    isLoading={Agent.isLoading}
                    setVisible={setCreateVisible}
                    onCreate={onCreate}
                    visible={createVisible}
                />
                <AssignRole
                    record={selectedRecord}
                    isLoading={Agent.isLoading}
                    setVisible={setAssignRole}
                    onCreate={assignRoleUser}
                    visible={assignRole}
                />

                <Button
                    onClick={() => setCreateVisible(true)}
                    type="primary"
                    icon={<PlusCircleOutlined />}
                >
                    Add New
                </Button>
                <div style={{ float: "right" }}>
                    <div style={{ marginBottom: 16 }}>
                        <Space>
                            {/* <Button
                                onClick={() => setAssignRole(true)}
                                icon={<IdcardTwoTone />}
                                disabled={!editable}
                            >
                                Assign Role
                            </Button> */}

                            {role == "Superadministrator" &&
                                <>
                                    <Input
                                        placeholder="user limit i.e: 5"
                                        // defaultValue={userLimit}
                                        value={userLimit}
                                        onChange={(e) => setUserLimit(e.target.value)}
                                    />

                                    <Button

                                        icon={<UserOutlined />}
                                        onClick={handleLimit}
                                    >
                                        Set Limit
                                    </Button>
                                </>
                            }


                            <Button
                                onClick={() => setEditVisible(true)}
                                icon={<EditTwoTone />}
                                disabled={!editable}
                            >
                                Edit
                            </Button>
                            <Button
                                onClick={onDelete}
                                icon={<DeleteOutlined />}
                                type="danger"
                                disabled={!hasSelected}
                            >
                                Delete
                            </Button>
                        </Space>
                        <span style={{ marginLeft: 8 }}>
                            {hasSelected ? `Selected ${selectedRowKeys.length} items` : ""}
                        </span>
                    </div>
                </div>
                <Table
                    size="small"
                    bordered={true}
                    scroll={{ x: 1300 }}
                    rowKey="id"
                    // rowKey={(record, index) => console.log("cc", index)}
                    dataSource={Agent.users}
                    rowSelection={rowSelection}
                >
                    <Column title="ID" dataIndex="id" key="key" />
                    <Column title="Name" dataIndex="name" key="name" />
                    <Column title="User Name" dataIndex="username" key="username" />
                    <Column title="E-Mail" dataIndex="email" key="email" />
                    <Column title="Type" dataIndex="type" key="type" />
                    <Column
                        title="Queue"
                        key="queue"
                        render={(text, record) =>
                            record.type === "Inbound" || record.type === "Blended"
                                ? record.queue
                                : null
                        }
                    />
                    {/* <Column
                        title="Queue"
                        key="queue"
                        render={(text, record) => record.type === "Inbound" ? record.queue : ""}
                    /> */}
                    {/* <Column title="Queue" dataIndex="queue" key="type" /> */}
                    <Column
                        title="Auth Username"
                        dataIndex="auth_username"
                        key="auth_username"
                    />
                    <Column
                        title="Auth Password"
                        dataIndex="auth_password"
                        key="auth_password"
                    />
                    <Column title="Created At" dataIndex="created_at" key="created_at" />
                    <Column title="Updated At" dataIndex="updated_at" key="updated_at" />
                    <Column
                        title="Action"
                        dataIndex="ation"
                        key="action"
                        render={(_, elm, index) => (
                            <div
                                key={index}
                                className="text-right d-flex justify-content-start"
                            >
                                <Tooltip title="Change Password">
                                    <Button
                                        type="primary"
                                        className="mr-2"
                                        icon={<EditOutlined />}
                                        //   disabled={!editable}
                                        size="small"
                                        onClick={() => {
                                            setId(elm?.id);
                                            setPasswordModal(true);
                                        }}
                                    />
                                </Tooltip>
                            </div>
                        )}
                    />
                </Table>
            </Spin>
            <ChangePasswordModal
                isModalOpen={passwordModal}
                handleOk={onChangePassword}
                form={form}
                onCancel={() => {
                    setPasswordModal(!passwordModal);
                    form.resetFields();
                }}
            />
        </>
    );
};

const ChangePasswordModal = ({ isModalOpen, handleOk, onCancel, form }) => {
    return (
        <Modal
            title="Change Password"
            visible={isModalOpen}
            onOk={() => {
                form.validateFields()
                    .then((values) => handleOk(values)).catch(error => console.log(error));
            }}
            onCancel={onCancel}>
            <Form size="large" form={form}>
                <Form.Item
                    name="password"
                    rules={[
                        {
                            required: true,
                            message: "Please input your password!",
                        },
                    ]}
                    hasFeedback
                >
                    <Input.Password placeholder="Enter New Password." />
                </Form.Item>

                <Form.Item
                    name="password_confirmation"
                    dependencies={["password"]}
                    hasFeedback
                    rules={[
                        {
                            required: true,
                            message: "Please confirm your password!",
                        },
                        ({ getFieldValue }) => ({
                            validator(_, value) {
                                if (!value || getFieldValue("password") === value) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(
                                    new Error("The two passwords that you entered do not match!")
                                );
                            },
                        }),
                    ]}
                >
                    <Input.Password placeholder="Enter Confirm Password." />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default Agent;
