import * as ActionTypes from "../Constants/InboundDispositionConstants"
import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";

export const getInboundDisposition = () => dispatch => {
    dispatch(inboundDispositionLoading())
    apiClient.post(`/api/report/inbound-disposition`).then(r => dispatch(inboundDispositionSuccess(r.data))).catch(e => dispatch(inboundDispositionFailed(handleError(e))))
}

export const getFilteredInboundDisposition = data => dispatch => {
    dispatch(inboundDispositionLoading())
    apiClient.post(`/api/report/inbound-disposition-filtered`, data).then(r => dispatch(inboundDispositionSuccess(r.data))).catch(e => dispatch(inboundDispositionFailed(handleError(e))))
}
export const inbounddispositionReset = () => dispatch => {

    dispatch(inboundDispositionReset());
}

const inboundDispositionReset = () => (
    {
        type: ActionTypes.INBOUND_DISPOSITION_RESET
    }
)

const inboundDispositionLoading = () => ({
    type: ActionTypes.INBOUND_DISPOSITION_LOADING
})

const inboundDispositionSuccess = data => ({
    type: ActionTypes.INBOUND_DISPOSITION_SUCCESS,
    payload: data
})

const inboundDispositionFailed = err => ({
    type: ActionTypes.INBOUND_DISPOSITION_FAILED,
    payload: err
})

