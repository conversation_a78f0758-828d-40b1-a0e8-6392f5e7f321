import * as ActionTypes from '../Constants/EmailSettingConstants'
import apiClient from "../Shared/apiClient";
import { EMAIL_SETTINGS, UPATE_EMAIL_SETTING ,UPATE_EMAIL_REPLAY_SETTING} from "../Endpoints/EmailSettingRoutes";
import { logoutUser } from "../Actions/UserActions";

export const getEmailSetting = () => dispatch => {
    dispatch(Loading())
    apiClient.get(EMAIL_SETTINGS).then(response => {
        dispatch(settingSuccess(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(settingFailed(error.response))
        }
        else
            dispatch(settingFailed(error.message))
    })
}

export const updateEmailSetting = (id, data) => dispatch => {
    dispatch(Loading())
    // apiClient.post(`${UPATE_EMAIL_SETTING}/${id}`, data).then(response => {
    apiClient.post(`${UPATE_EMAIL_SETTING}`, data).then(response => {
        dispatch(settingUpdate(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(settingFailed(error.response))
        }
        else
            dispatch(settingFailed(error.message))
    })
}

export const updateEmailReplaySetting = (id, data) => dispatch => {
    dispatch(Loading())
    apiClient.post(`${UPATE_EMAIL_REPLAY_SETTING}`, data).then(response => {
        dispatch(settingUpdate(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(settingFailed(error.response))
        }
        else
            dispatch(settingFailed(error.message))
    })
}


export const Loading = () => ({
    type: ActionTypes.EMAIL_SETTING_LOADING
})

export const settingSuccess = setting => ({
    type: ActionTypes.EMAIL_SETTING_SUCCESS,
    payload: setting
})

export const settingUpdate = message => ({
    type: ActionTypes.EMAIL_SETTING_UPDATE,
    payload: message
})

export const settingFailed = error => ({
    type: ActionTypes.EMAIL_SETTING_FAILED,
    payload: error
})
