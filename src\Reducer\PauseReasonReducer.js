import * as ActionTypes from '../Constants/PauseReasonConstant'
const initElement =
{
    isLoading: false,
    errMess: null,
    pauseReason:[],
    message: null
}

export const PauseResonReducer = (state = initElement, action) =>
{
    switch (action.type)
    {
        default:
            return {...state}
        case ActionTypes.PAUESEREASON_FAILED:
            return {...state, isLoading: false, errMess: action.payload}
        case ActionTypes.PAUSEREASON_SUCCESS:
            return {...state, isLoading: false, pauseReason: action.payload,message: null, errMess: null}
        case ActionTypes.PAUSEREASON_CREATE_SUCCESS:
            return {...state, isLoading: false, message: action.payload, errMess: null}
        case ActionTypes.PAUSEREASON_UPDATE_SUCCESS:
            return {...state, isLoading: false, message: action.payload, errMess: null}
        case ActionTypes.PAUSEREASON_DELETE_SUCCESS:
            return {...state, isLoading: false, message: action.payload, errMess: null}
        case ActionTypes.PAUESEREASON_LOADING:
            return {...state, isLoading: true}
    }
}