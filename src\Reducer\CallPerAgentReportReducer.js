const initialState = {
    data: [],
    isLoading: false,
    errMess: ''
}

export const CallPerAgentReportReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case "CALL_AGENT_REPORT_LOADING":
            return { ...state, isLoading: true }
        case "CALL_AGENT_REPORT_SUCCESS":
            return { ...state, isLoading: false, data: action.payload, errMess: '' }
        case "CALL_AGENT_REPORT_FAILED":
            return { ...state, isLoading: false, errMess: action.payload }
        case "CALL_AGENT_REPORT_RESET":
            return { ...state, data: [] }
    }
}