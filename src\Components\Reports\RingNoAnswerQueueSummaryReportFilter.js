import { useDispatch, useSelector } from "react-redux";
import { Button, DatePicker, Descriptions, Form, Input, Modal, Select, Space, Spin, Table } from "antd";
import React, { useState, useEffect } from 'react'
import { getQueues } from "../../Actions/QueueActions";
import { getAgent } from "../../Actions/AgentActions";

const RingNoAnswerQueueSummaryReportFilter = ({ visible, setVisible, onCreate, isLoading }) => {
    const [form] = Form.useForm()
    const QueueState = useSelector(state => state.QueueReducer)
    // const agentState = useSelector(state => state.AgentReducer)
    const [showQueue, setShowQueue] = useState(false)
    const dispatch = useDispatch()

    useEffect(() => {
        if (visible) {
            dispatch(getQueues())
            // dispatch(getAgent())
            setShowQueue(true)
        }
    }, [visible])

    return (
        // <Spin spinning={QueueState.isLoading || isLoading}>
        <Modal
            visible={visible}
            onCancel={() => {
                form.resetFields()
                setVisible(false)
            }}
            onOk={() => {
                form
                    .validateFields()
                    .then((values) => {
                        console.log("validated")
                        onCreate(values);
                        form.resetFields();
                    })
                    .catch((info) => {
                        console.log('Validate Failed:', info);
                    })
            }}
        >
            <Form
                form={form}
                layout="vertical"
                name="form_in_modal"
            >
                <Form.Item
                    name="time"
                    label="Time"
                >
                    <DatePicker.RangePicker showTime style={{ width: '100%' }} />
                </Form.Item>

                {/*<Form.Item name="agent" label="Agent">*/}
                {/*    <Select>*/}
                {/*        {agentState.users && agentState.users.map((value, index) => <Select.Option value={value.name} key={value.id}>*/}
                {/*            {value.name}*/}
                {/*        </Select.Option>)}*/}
                {/*    </Select>*/}
                {/*</Form.Item>*/}

                {showQueue && <Form.Item
                    name="queuename"
                    label="Queue"
                    rules={[
                        {
                            message: 'Please select the queue',
                        },
                    ]}
                >
                    <Select>
                        {QueueState.queues.map((value) => (
                            <Select.Option value={value.name} key={value.name}>{value.name}</Select.Option>
                        ))}
                    </Select>
                </Form.Item>}

            </Form>

        </Modal>
        // </Spin>
    );
}

export default RingNoAnswerQueueSummaryReportFilter