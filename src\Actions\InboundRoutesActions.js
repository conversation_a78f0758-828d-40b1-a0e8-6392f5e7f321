import * as ActionTypes from '../Constants/InboundRoutesConstant'
import apiClient from "../Shared/apiClient";
import { INBOUND } from "../Endpoints/InboundRoutes";
import { logoutUser } from "./UserActions";

export const getInbound = () => dispatch => {
    dispatch(loading())
    apiClient.get(INBOUND).then(response => {
        dispatch(showSuccess(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(inboundFailed(error.response))
        }
        else
            dispatch(inboundFailed(error.message))
    })
}

export const createInbound = obj => dispatch => {
    dispatch(loading())
    apiClient.post(INBOUND, obj).then(response => {
        dispatch(createSuccess(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(inboundFailed(error.response))
        }
        else
            dispatch(inboundFailed(error.message))
    })
}

export const updateInbound = (id, obj) => dispatch => {
    dispatch(loading())
    apiClient.put(`${INBOUND}/${id}`, obj).then(response => {
        dispatch(updateSuccess(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(inboundFailed(error.response))
        }
        else
            dispatch(inboundFailed(error.message))
    })
}

export const deleteInbound = id => dispatch => {
    dispatch(loading())
    apiClient.delete(`${INBOUND}/${id}`).then(response => {
        dispatch(deleteSuccess(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(inboundFailed(error.response))
        }
        else
            dispatch(inboundFailed(error.message))
    })
}

export const loading = () =>
({
    type: ActionTypes.INBOUND_LOADING
})

export const inboundFailed = error =>
({
    type: ActionTypes.INBOUND_FAILED, payload: error
})

export const createSuccess = data =>
({
    type: ActionTypes.CREATE_SUCCESS, payload: data
})

export const updateSuccess = data =>
({
    type: ActionTypes.UPDATE_SUCCESS, payload: data
})

export const deleteSuccess = data =>
({
    type: ActionTypes.DELETE_SUCCESS, payload: data
})

export const showSuccess = data =>
({
    type: ActionTypes.INBOUND_SUCCESS, payload: data
})