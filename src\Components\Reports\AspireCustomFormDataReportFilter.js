import { DatePicker, Form, Input, Modal, Select, Spin } from "antd";
import { useEffect, useState } from "react";
import apiClient from "../../Shared/apiClient";


export const AspireCustomFormDataReportFilter = ({ visible, setCallStatus, setFilterCheck, callStatus, form_id, setVisible, onCreate, isLoading, agents, setAgents, formId, setFormId }) => {
    const [form] = Form.useForm()
    const [queues, setQueues] = useState([])

    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data))
    }, [])

    useEffect(() => {

        if (visible == true) {
            apiClient.get('/api/report/aspireCustomFormDataFormFilterData').then((res) => {
                setFormId(res.data.formIds)
                setCallStatus(res.data.callStatus)
                setAgents(res.data.agents)
            }).catch((err) => {
                console.log(err)
            })
        }

    }, [visible])

    return (
        <Modal
            visible={visible}

            onCancel={() => {
                form.resetFields()
                setVisible(false)
            }}
            onOk={() => {
                form
                    .validateFields()
                    .then((values) => {
                        onCreate(values);
                        setFilterCheck(false)
                        form.resetFields();
                    })
                    .catch((info) => {
                        console.log('Validate Failed:', info);
                    })

                setVisible(false);
            }}
        >
            <Spin spinning={isLoading}>
                <Form

                    form={form}
                    layout="vertical"
                    name="form_in_modal"
                >
                    <Form.Item
                        name="form_id"
                        label="Form"
                        rules={[{ required: true, message: 'Please select a Form' }]}
                    >
                        <Select>
                            {form_id.map((value, index) => (
                                <Select.Option key={index} value={value.id}>
                                    {value.name}
                                </Select.Option>
                            ))}
                        </Select>
                    </Form.Item>
                    <Form.Item
                        name="start"
                        label="Start"
                    >
                        <DatePicker showTime style={{ width: '100%' }} />
                    </Form.Item>
                    <Form.Item
                        name="end"
                        label="End"
                    >
                        <DatePicker showTime style={{ width: '100%' }} />
                    </Form.Item>
                    {/* 
                    <Form.Item name="auth_username" label="Agent Name">
                        <Select>
                            {agents.map((value, index) => (
                                    <Select.Option key={index} value={value.auth_username}>
                                        {value.username}
                                    </Select.Option>
                                ))}
                        </Select>
                    </Form.Item> */}
                    <Form.Item name="callStatus" label="Call Status">
                        <Select>
                            {callStatus.map((value, index) => (
                                <Select.Option key={index} value={value.disposition}>
                                    {value.disposition}
                                </Select.Option>
                            ))}
                        </Select>
                    </Form.Item>
                    <Form.Item
                        name="source"
                        label="Source"
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        name="destination"
                        label="Destination"
                    >
                        <Input />
                    </Form.Item>

                    <Form.Item colon={false} name="queue" label="Queue" >
                        <Select placeholder="Select Queue" >
                            {queues && queues.map((queue, index) => <Select.Option key={index} value={queue.name}>
                                {queue.name}
                            </Select.Option>)}
                        </Select>
                    </Form.Item>

                </Form>
            </Spin>

        </Modal>
    )
}