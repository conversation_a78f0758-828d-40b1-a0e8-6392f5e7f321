import * as ActionTypes from '../Constants/RoleContants'
import apiClient from "../Shared/apiClient";
import { ASSIGN_PERMSSION, ASSIGN_ROLE, GER_PERMISSION_BY_ROLE, GET_ROLE_BY_USER, ROLE } from "../Endpoints/RoleRoutes";
import { logoutUser } from "./UserActions";
import { openNotificationWithIcon } from '../Shared/notification';

export const getRole = () => dispatch => {
    dispatch(loading())
    apiClient.get(ROLE).then(response => {
        dispatch(success(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(failedRole(error.response))
        }
        else
            dispatch(failedRole(error.message))
    })
}

export const allowModule = () => dispatch => {

    dispatch(loading())
    apiClient.get('api/allowed_modules').then((r) => {
        console.log("response", r.data)
        dispatch(modulesSuccess(r.data))
    }).catch((e) => {
        console.log("allowed modules error", e)
    })

}

export const createRole = data => dispatch => {
    dispatch(loading())
    apiClient.post(ROLE, data).then(response => {
        dispatch(createSuccess(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(failedRole(error.response))
        }
        else
            dispatch(failedRole(error.message))
    })
}

export const deleteRole = id => dispatch => {
    dispatch(loading())
    apiClient.delete(`${ROLE}/${id}`).then(response => {
        dispatch(deleteSuccess(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(failedRole(error.response))
        }
        else
            dispatch(failedRole(error.message))
    })
}

export const updateRole = (data, id) => dispatch => {
    dispatch(loading())
    apiClient.put(`${ROLE}/${id}`, data).then(response => {
        dispatch(updateSuccess(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(failedRole(error.response))
        }
        else
            dispatch(failedRole(error.message))
    })
}

export const assignPermission = (role, permissions) => dispatch => {
    dispatch(loading())
    apiClient.post(ASSIGN_PERMSSION, { role: role, permission: permissions }).then(response => {
        dispatch(assignedPermission(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(failedRole(error.response))
        }
        else
            dispatch(failedRole(error.message))
    })
}

export const assignRoleToUser = (userID, roles) => dispatch => {
    dispatch(loading())
    apiClient.post(ASSIGN_ROLE, { userID: userID, roles: roles }).then(response => {
        dispatch(assignRoleUser(response.data))
        if (response.data) {
            openNotificationWithIcon('success', response?.data)
        }
        // console.log(response.data)
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(failedRole(error.response))
        }
        else
            dispatch(failedRole(error.message))
    })
}

export const getPermissionByRole = (roleID) => dispatch => {
    dispatch(loading())
    apiClient.post(GER_PERMISSION_BY_ROLE, { roleId: roleID }).then(response => {
        dispatch(permissionByRole(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(failedRole(error.response))
        }
        else
            dispatch(failedRole(error.message))
    })
}

export const getRoleFromUser = userID => dispatch => {
    dispatch(loading())
    apiClient.post(GET_ROLE_BY_USER, { userID }).then(response => {
        dispatch(getRoleByUser(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(failedRole(error.response))
        }
        else
            dispatch(failedRole(error.message))
    })
}

export const loading = () => {
    return { type: ActionTypes.ROLE_LOADING }
}

export const success = data => {
    return { type: ActionTypes.ROLE_SUCCESS, payload: data }
}

export const modulesSuccess = data => {
    return { type: ActionTypes.MODULES_SUCCESS, payload: data }
}

export const createSuccess = data => {
    return { type: ActionTypes.ROLE_CREATE_SUCCESS, payload: data }
}

export const updateSuccess = data => {
    return { type: ActionTypes.ROLE_UPDATE, payload: data }
}

export const deleteSuccess = data => {
    return { type: ActionTypes.ROLE_DELETE, payload: data }
}

export const failedRole = error => {
    return { type: ActionTypes.ROLE_FAILED, payload: error }
}

export const assignedPermission = message => {
    return { type: ActionTypes.PERMISSION_ASSIGN, payload: message }
}

export const permissionByRole = message => {
    return { type: ActionTypes.GET_PERMISSION_BY_ROLE, payload: message }
}

export const assignRoleUser = message => {
    return { type: ActionTypes.ASSIGN_ROLE_TO_USER, payload: message }
}

export const getRoleByUser = data => {
    return { type: ActionTypes.GET_ROLE_FROM_USERS, payload: data }
}
