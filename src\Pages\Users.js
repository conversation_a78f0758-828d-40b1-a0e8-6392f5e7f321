import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";
import {
    createUser,
    deleteUser,
    getUsers,
    updateUser,
} from "../Actions/UsersActions";
import {
    Table,
    Space,
    Spin,
    Button,
    notification,
    Form,
    Tooltip,
    Modal,
    Input,
} from "antd";
import {
    DeleteOutlined,
    EditTwoTone,
    EditOutlined,
    IdcardTwoTone,
    PlusCircleOutlined,
} from "@ant-design/icons";
import EditUser from "../Components/Users/<USER>";
import AddUser from "../Components/Users/<USER>";
import AssignRole from "../Components/Users/<USER>";
import { assignRoleToUser } from "../Actions/RoleActions";
import Text from "antd/es/typography/Text";
import { isArrays } from "react-csv/src/core";
import apiClient from "../Shared/apiClient";
import { openNotificationWithIcon } from "../Shared/notification";
const { Column } = Table;

const Users = () => {
    const Users = useSelector((state) => state.UsersReducer);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRecord, setSelectedRecord] = useState(null);
    const [editVisible, setEditVisible] = useState(false);
    const [createVisible, setCreateVisible] = useState(false);
    const [assignRole, setAssignRole] = useState(false);
    const [passwordModal, setPasswordModal] = useState(false);
    const [id, setId] = useState(false);


    const dispatch = useDispatch();
    const [form] = Form.useForm();


    // const openNotificationWithIcon = (type, message) => {
    //     notification[type]({
    //         message: type === "error" ? "Error" : "Success",
    //         description: message,
    //     });
    // };

    useEffect(() => {
        if (createVisible) {
            form.resetFields();
        }
    }, [createVisible, form]);

    useEffect(() => {
        if (Users.message) {
            form.resetFields();
            setCreateVisible(false);
            openNotificationWithIcon("success", Users.message);
            dispatch(getUsers());
        }
    }, [Users.message]);

    useEffect(() => {
        dispatch(getUsers());
    }, []);

    useEffect(() => {
        console.log("check", Users)
        if (Users.errMess) {

            if (Users.errMess?.data?.errors && Object.keys(Users.errMess?.data?.errors).length > 0) {
                Object.keys(Users.errMess?.data?.errors).map(key => openNotificationWithIcon('error', Users.errMess?.data?.errors[key]))
            }
            // else {
            //     const err = Users.errMess?.data
            //     if (err?.includes("for key 'users_email_unique'")) {
            //         openNotificationWithIcon('error', "This email is already taken!")
            //     } else openNotificationWithIcon('error', Users.errMess?.data)
            // }
            else {
                if (Users.errMess.length > 0) {
                    openNotificationWithIcon("error", Users?.errMess)
                }
                else {

                    openNotificationWithIcon("error", Users?.errMess?.data?.message)
                }
            }

            setSelectedRowKeys([])
        }

    }, [Users.errMess]);

    const rowSelection = {
        selectedRowKeys,
        onChange: onSelectChange,
        type: "radio",
    };

    function onSelectChange(keys) {
        setSelectedRecord(
            Users.users.find((value, index, obj) => value.id === keys[0])
        );
        setSelectedRowKeys(keys);
    }

    function onCreate(values) {
        dispatch(createUser(values));
    }

    function onUpdate(values) {
        dispatch(updateUser(selectedRecord.id, values));
        setEditVisible(false);
    }

    function onDelete() {
        dispatch(deleteUser(selectedRecord.id));
    }

    function assignRoleUser(values) {
        dispatch(assignRoleToUser(selectedRecord.id, values));
        // setCreateVisible(false)
        console.log(selectedRecord.id, values);
    }

    //   const handlePasswordModal = () => {
    //     setPasswordModal(true);
    //   };

    const onChangePassword = (values) => {
        apiClient
            .post(`/api/${id}/agent/password/reset`, values)
            .then((res) => {
                openNotificationWithIcon("success", res.data?.message);
                form.resetFields();
                setPasswordModal(!passwordModal);
            })
            .catch((err) => {
                openNotificationWithIcon("error", err.response?.data?.message);
                form.resetFields();
                setPasswordModal(!passwordModal);
            });
    };

    const hasSelected = selectedRowKeys.length > 0;
    const editable = selectedRowKeys.length === 1;

    return (
        <>
            <Spin spinning={Users.isLoading}>
                <EditUser
                    setVisible={setEditVisible}
                    onCancel={() => setEditVisible(false)}
                    visible={editVisible}
                    onCreate={onUpdate}
                    record={selectedRecord}
                />
                <AddUser
                    form={form}
                    isLoading={Users.isLoading}
                    setVisible={setCreateVisible}
                    onCreate={onCreate}
                    visible={createVisible}
                />
                <AssignRole
                    record={selectedRecord}
                    isLoading={Users.isLoading}
                    setVisible={setAssignRole}
                    onCreate={assignRoleUser}
                    visible={assignRole}
                />

                <Button
                    onClick={() => setCreateVisible(true)}
                    type="primary"
                    icon={<PlusCircleOutlined />}
                >
                    Add New
                </Button>
                <div style={{ float: "right" }}>
                    <div style={{ marginBottom: 16 }}>
                        <Space>

                            <Button
                                onClick={() => setAssignRole(true)}
                                icon={<IdcardTwoTone />}
                                disabled={!editable}
                            >
                                Assign Role
                            </Button>
                            <Button
                                onClick={() => setEditVisible(true)}
                                icon={<EditTwoTone />}
                                disabled={!editable}
                            >
                                Edit
                            </Button>
                            <Button
                                onClick={onDelete}
                                icon={<DeleteOutlined />}
                                type="danger"
                                disabled={!hasSelected}
                            >
                                Delete
                            </Button>
                        </Space>
                        <span style={{ marginLeft: 8 }}>
                            {hasSelected ? `Selected ${selectedRowKeys.length} items` : ""}
                        </span>
                    </div>
                </div>
                <Table
                    size="small"
                    bordered={true}
                    scroll={{ x: 1300 }}
                    rowKey="id"
                    dataSource={Users?.users}
                    rowSelection={rowSelection}>
                    <Column title="ID" dataIndex="id" key="key" />
                    <Column title="Name" dataIndex="name" key="name" />
                    <Column title="User Name" dataIndex="username" key="username" />
                    <Column title="E-Mail" dataIndex="email" key="email" />
                    {/*<Column title="Type" dataIndex="type" key="type" />*/}
                    {/*<Column title="Queue" dataIndex="queue" key="type" />*/}
                    {/*<Column title="Auth Username" dataIndex="auth_username" key="auth_username" />*/}
                    <Column title="Created At" dataIndex="created_at" key="created_at" />
                    <Column title="Updated At" dataIndex="updated_at" key="updated_at" />
                    <Column
                        title="Action"
                        dataIndex="ation"
                        key="action"
                        render={(_, elm, index) => (
                            <div
                                key={index}
                                className="text-right d-flex justify-content-start"
                            >
                                <Tooltip title="Change Password">
                                    <Button
                                        type="primary"
                                        className="mr-2"
                                        icon={<EditOutlined />}
                                        //   disabled={!editable}
                                        size="small"
                                        onClick={() => {
                                            setId(elm?.id);
                                            setPasswordModal(true);
                                        }}
                                    />
                                </Tooltip>
                            </div>
                        )}
                    />
                </Table>
            </Spin>
            <ChangePasswordModal
                isModalOpen={passwordModal}
                handleOk={onChangePassword}
                form={form}
                onCancel={() => {
                    setPasswordModal(!passwordModal);
                    form.resetFields();
                }}
            />
        </>
    );
};

const ChangePasswordModal = ({ isModalOpen, handleOk, onCancel, form }) => {
    return (
        <Modal
            title="Change Password"
            visible={isModalOpen}
            onOk={() => {
                form.validateFields().then((values) => {
                    handleOk(values);
                });
            }}
            onCancel={onCancel}
        >
            <Form size="large" form={form}>
                <Form.Item
                    name="password"
                    rules={[
                        {
                            required: true,
                            message: "Please input your password!",
                        },
                    ]}
                    hasFeedback
                >
                    <Input.Password placeholder="Enter New Password." />
                </Form.Item>

                <Form.Item
                    name="password_confirmation"
                    dependencies={["password"]}
                    hasFeedback
                    rules={[
                        {
                            required: true,
                            message: "Please confirm your password!",
                        },
                        ({ getFieldValue }) => ({
                            validator(_, value) {
                                if (!value || getFieldValue("password") === value) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(
                                    new Error("The two passwords that you entered do not match!")
                                );
                            },
                        }),
                    ]}
                >
                    <Input.Password placeholder="Enter Confirm Password." />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default Users;
