import { Form, Input, Modal, Select, Spin } from "antd";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import apiClient from "../../Shared/apiClient";

const AddUser = ({ form, isLoading, visible, setVisible, onCreate }) => {
    const QueueState = useSelector(state => state.QueueReducer)
    const [queues, setQueues] = useState([])
    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data))
    }, [])
    const [queue, setQueueOption] = useState("");

    var handleQueue = (queue) => {
        console.log(queue, 'queueoption');
        setQueueOption(queue);
    };
    return (

        <Modal
            visible={visible}
            title={'Add User'}
            okText="Submit"
            cancelText="Cancel"
            closable={true}
            onCancel={() => {
                //form.resetFields()
                setVisible(false)
            }}
            onOk={() => {
                form
                    .validateFields()
                    .then((values) => {
                        //form.resetFields()
                        onCreate(values)
                    })
                    .catch((info) => {
                        console.log('Validate Failed:', info);
                    })
            }}
        >
            <Spin spinning={QueueState.isLoading || isLoading}>
                <Form
                    form={form}
                    layout="vertical"
                    name="form_in_modal"
                >
                    <Form.Item
                        name="name"
                        label="Name"
                        rules={[
                            {
                                required: true,
                                message: 'Please input the name',
                            },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        name="username"
                        label="Username"
                        rules={[
                            {
                                required: true,
                                message: 'Please input the username',
                            },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        name="email"
                        label="E-Mail"
                        rules={[
                            {
                                required: true,
                                message: 'Please input the email address',
                            },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        name="password"
                        label="Password"
                        rules={[
                            {
                                required: true,
                                message: 'Please input the password',
                            },
                        ]}
                    >
                        <Input.Password />
                    </Form.Item>
                    <Form.Item
                        name="password_confirmation"
                        label="Confirm Password"
                        rules={[
                            {
                                required: true,
                                message: 'Please input confirm password',
                            },
                        ]}
                    >
                        <Input.Password />
                    </Form.Item>
                    {/* <Form.Item name="queues" label="Queue" >
                        <Select mode="multiple" onChange={handleQueue} placeholder="Select  queues">
                            {queues &&
                                queues.map((value, index) => (
                                    <Select.Option key={index} value={value.name}>
                                        {value.name}
                                    </Select.Option>
                                ))}
                        </Select>
                    </Form.Item> */}
                </Form>
            </Spin>
        </Modal>
    )
}

export default AddUser