import * as ActionTypes from "../Constants/OutboundActivityConstants"
import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";

export const getOutboundActivityReport = () => dispatch => {
    dispatch(outboundActivityLoading())
    apiClient.post(`/api/report/outbound-activity`).then(r => dispatch(outboundActivitySuccess(r.data))).catch(e => dispatch(outboundActivityFailed(handleError(e))))
}

export const getOutboundActivityFiltered = data => dispatch => {
    dispatch(outboundActivityLoading())
    apiClient.post(`/api/report/outbound-activity-filtered`, data).then(r => dispatch(outboundActivitySuccess(r.data))).catch(e => dispatch(outboundActivityFailed(handleError(e))))
}
export const outboundActivityReset = () => dispatch => {
    dispatch(outboundReset())

}

const outboundActivityLoading = () => ({
    type: ActionTypes.OUTBOUND_ACTIVITY_LOADING
})
const outboundActivitySuccess = data => ({
    type: ActionTypes.OUTBOUND_ACTIVITY_SUCCESS,
    payload: data
})
const outboundActivityFailed = err => ({
    type: ActionTypes.OUTBOUND_ACTIVITY_FAILED,
    payload: err
})
const outboundReset = () => ({
    type: ActionTypes.OUTBOUND_ACTIVITY_RESET
})