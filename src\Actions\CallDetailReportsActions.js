import * as ActionsType from '../Constants/CallDetailReportConstant'
import apiClient from "../Shared/apiClient";
import { CDR_MINUTES_OF_MEETING, CDR_Report, CDR_Report_Filter, CDR_Reports } from "../Endpoints/CallDetailReportRoutes";
import { logoutUser } from "./UserActions";
import { REPORT_SUCCESS } from "../Constants/CallDetailReportConstant";
import { handleError } from "../Shared/handleError";

export const getAllReport = () => (dispatch) => {
    dispatch(loading())
    apiClient.get(CDR_Report).then(response => {
        dispatch(getReport(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response))
        }
        else
            dispatch(reportFailed(error.message))
    })
}

export const MinutesOfMeetingReport = () => dispatch => {
    dispatch(loading())
    apiClient(CDR_MINUTES_OF_MEETING).then(response => dispatch(reportSuccess(response.data))).catch(error => dispatch(reportFailed(handleError(error))))
}

export const getMinutesOfMeetingFilteredReport = values => dispatch => {
    dispatch(loading())
    apiClient.post(`${CDR_MINUTES_OF_MEETING}/filtered`, values).then(response => dispatch(reportSuccess(response.data))).catch(error => dispatch(reportFailed(handleError(error))))
}

export const getAllReportsPaginate = (page, pageSize = 10, data) => dispatch => {
    dispatch(loading())
    apiClient(CDR_Reports + "?page=" + page + "&pageSize=" + pageSize, data).then(response => {
        dispatch(getAllReportPaginate(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response))
        }
        else
            dispatch(reportFailed(error.message))
    })
}

export const CdrReportFilter = data => (dispatch) => {
    dispatch(loading())
    apiClient.post(CDR_Report_Filter + "?page=" + data?.pagination?.current ?? '1' + "&pageSize=" + data?.pagination?.pageSize ?? '10', data).then(response => {
        dispatch(getReport(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response))
        }
        else
            dispatch(reportFailed(error.message))
    })
}

export const loading = () =>
({
    type: ActionsType.REPORT_LOADING
})

export const getReport = data =>
({
    type: ActionsType.GET_REPORT,
    payload: data
})

export const reportSuccess = data => ({
    type: ActionsType.REPORT_SUCCESS,
    payload: data
})

export const reportFailed = error =>
({
    type: ActionsType.REPORT_FAILED,
    payload: error
})

export const getAllReportPaginate = data =>
({
    type: ActionsType.GET_ALL_REPORT,
    payload: data
})
