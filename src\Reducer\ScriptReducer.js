import * as ActionTypes from "../Constants/ScriptConstants"

const initialState = {
    scripts: [],
    isLoading: false,
    message: false,
    errMess: false
}

export const ScriptReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.SCRIPT_LOADING:
            return {...state, isLoading: true, message: false, errMess: false}
        case ActionTypes.SCRIPT_FAILED:
            return {...state, isLoading: false, errMess: action.payload, message: false}
        case ActionTypes.SCRIPT_SUCCESS:
            return {...state, isLoading: false, message: action.payload, errMess: false}
        case ActionTypes.SCRIPTS_SUCCESS:
            return {...state, isLoading: false, message: false, errMess: false, scripts: action.payload}
    }
}