import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, Card, DatePicker, Form, Input, Space, Select, Table, Modal } from "antd";
import apiClient from "../../Shared/apiClient";
import moment from "moment";
import { CSVLink } from "react-csv";
import {
    FilterOutlined,
    ReloadOutlined,
    SearchOutlined
} from "@ant-design/icons";
import Highlighter from "react-highlight-words";
import { useDispatch } from "react-redux";
const dateFormat = "YYYY-MM-DD";

const exportHeaders = [
    { label: 'From', key: 'from' },
    { label: 'To', key: 'to' },
    { label: 'Active Users', key: 'agentLogin' },
    { label: 'Break Users', key: 'agentBreak' },
    { label: 'Total Calls', key: 'totalInboundCalls' },
    { label: 'Abandoned Calls', key: 'totalAbandonCalls' },
    { label: 'Abandoned Calls Percentage', key: 'acr' },
]

const HourlyAbandonReport = () => {
    const [form] = Form.useForm();
    const [data, setData] = useState([]);
    const [date, setDate] = useState();
    const [loading, setLoading] = useState(false);
    const [fetchReportCheck, setFetchReportCheck] = useState(true);
    const [queues, setQueues] = useState([])
    const [selectQueue, setSelectQueue] = useState('')
    const [showFilter, setShowFilter] = useState(false)
    const [resetFilter, setResetFilter] = useState(false)

    const onDateChange = (date, dateString) => {
        setDate(moment(dateString));
        setFetchReportCheck(false);
    };


    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const searchInput = useRef();

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }

    const columns = [
        {
            title: "From",
            dataIndex: "from",
            key: "from",
            ...getColumnSearchProps('from'),
            width: 150,
        },
        {
            title: "To",
            dataIndex: "to",
            key: "to",
            ...getColumnSearchProps('to'),
            width: 150,
        },
        {
            title: "Active Users",
            dataIndex: "agentLogin",
            key: "agentLogin",
            // ...getColumnSearchProps('agentLogin'),
            width: 150,
        },
        {
            title: "Break Users",
            dataIndex: "agentBreak",
            key: "agentBreak",
            // ...getColumnSearchProps('agentBreak'),
            width: 150,
        },
        {
            title: "Total Calls",
            dataIndex: "totalInboundCalls",
            key: "totalInboundCalls",
            // ...getColumnSearchProps('totalInbounCalls'),
            width: 150,
        },
        {
            title: "Abandoned Calls",
            dataIndex: "totalAbandonCalls",
            key: "totalAbandonCalls",
            // ...getColumnSearchProps('totalAbandonCalls'),
            width: 150,
        },
        {
            title: "Abandoned Calls Percentage",
            dataIndex: "acr",
            key: "acr",
            ...getColumnSearchProps('acr'),
            width: 150,
        },
    ];


    useEffect(() => {
        apiClient.get('api/queue').then((res) => res.data && setQueues(res.data))
    }, [])


    const fetchReport = () => {
        setLoading(true);
        apiClient
            .post(`/api/report/getHourlyAbandonReport`, {
                date: date?._i,
                queue: selectQueue
            })
            .then((resp) => {
                setData(resp.data);
                setLoading(false);
                setFetchReportCheck(true);
                setSelectQueue(null)
                form.resetFields();
            })
            .catch((err) => {
                setLoading(false);
                console.log(err.message);
            });
    };

    const resetFormFilter = () => {
        // dispatch(getTrunkPerHourReport())
        // dispatch(trunkPerHourReset())
        // console.log(new Date().toLocaleString() + '')
        setData([])
        form.resetFields()
        setResetFilter(true)
    }

    return (
        <>
            <Card

                style={{ background: "#fff", borderRadius: "2px", padding: 10 }}

            >
                <Table
                    title={data =>
                        <>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                                {`Hourly Abandoned Report`}
                                <Space>
                                    <Button onClick={() => resetFormFilter()} type="danger" icon={<ReloadOutlined />}>Reset Filter</Button>
                                    <Button onClick={() => setShowFilter(true)} icon={<FilterOutlined />}>Filter</Button>
                                    <CSVLink filename="Hourly Abandoned Report.csv" data={data} headers={exportHeaders}>
                                        <Button disabled={data.length === 0}>Export Report</Button>
                                    </CSVLink>
                                </Space>

                            </div>
                        </>
                    }
                    columns={columns}
                    dataSource={data}
                    pagination={false}
                    loading={loading}
                    bordered
                    size="default"
                    rowKey="to"
                    scroll={{
                        // x: "calc(700px + 50%)",
                        y: 240,
                    }}
                />
            </Card>
            <HourlyAbandonedFilter
                form={form}
                resetFilter={resetFilter}
                visible={showFilter}
                date={date}
                fetchReport={fetchReport}
                // onTimeChange={onTimeChange}
                onDateChange={onDateChange}
                selectQueue={selectQueue}
                setSelectQueue={setSelectQueue}
                // onSearch={onSearch}
                // onChange={onChange}

                // selectedQueue={selectedQueue}
                setVisible={setShowFilter} />
        </>
    );
};

export const HourlyAbandonedFilter = ({ form, fetchReport, selectQueue, setSelectQueue, onChange, visible, setVisible, setSelectedQueue, date, onDateChange }) => {


    const dispatch = useDispatch()
    const [filterDate, setFilterDate] = useState()
    // useEffect(() => form.resetFields(), [resetFilter])
    const [queues, setQueues] = useState([])

    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data))
    }, [])

    const handleQueue = (value) => {
        console.log("selected value", value);
        setSelectedQueue(value)

    }
    // useEffect(() => {
    //     console.log("selected queue ", selectedQueue)
    // }, [selectedQueue])

    // console.log("selected queue ", selectedQueue)

    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            okText="Submit"
            onOk={() => form.validateFields()
                .then(value => {
                    // dispatch(getTrunkPerHourReport({ date: filterDate, queue: queue }))
                    fetchReport()
                    setVisible(false)
                })
            }
            // okButtonProps={{
            //     loading: btnLoading,
            //     icon: <SaveOutlined />
            // }}
            title="Hourly Abandoned Filter">
            {/* <Spin spinning={btnLoading} indicator={<SyncOutlined spin />}> */}
            <Form
                form={form}
                layout="vertical"
            >
                <Form.Item name={"picker"}>
                    <DatePicker
                        format={dateFormat}
                        onChange={onDateChange}
                    />
                </Form.Item>
                <Form.Item name={"queue"}>
                    <Select
                        showSearch
                        placeholder="Select Queue"
                        style={{ width: '170px' }}
                        onChange={(e) => setSelectQueue(e)}
                    >
                        {queues.map((queue, index) => (
                            <Select.Option key={index} value={queue.name}>{queue.name}</Select.Option>
                        ))}
                    </Select>
                </Form.Item>
            </Form>
            {/* </Spin> */}
        </Modal>
    )
}


export default HourlyAbandonReport;