import { Checkbox, Divider, Form, Modal, Spin, message } from "antd";
import { useEffect, useState } from "react";
import apiClient from "../../Shared/apiClient";

const AssignQueue = ({ queues, record, isLoading, visible, setVisible, onCreate }) => {
    const [form] = Form.useForm();
    const [checkAll, setCheckAll] = useState(false);
    const [plainOptions, setPlainOptions] = useState([]);
    const [indeterminate, setIndeterminate] = useState(false);

    useEffect(() => {
        if (queues) {
            const options = queues.map((queue) => queue.name);
            setPlainOptions(options);
        }
    }, [queues]);

    useEffect(() => {
        if (visible && record?.id) {
            apiClient.post('/api/get-queue-role', { role: record?.id })
                .then(res => {
                    const selectedQueueIds = res.data?.data.map(queue => queue.queue_name);
                    form.setFieldsValue({ queue: selectedQueueIds });

                    setCheckAll(selectedQueueIds.length === plainOptions.length);
                    setIndeterminate(selectedQueueIds.length > 0 && selectedQueueIds.length < plainOptions.length);
                })
                .catch((err) => {
                    console.log(err.response);
                });
        }

        return () => {
            form.resetFields();
        };
    }, [visible, record, queues]);

    const onChange = (list) => {
        form.setFieldsValue({ queue: list });
        setCheckAll(list.length === plainOptions.length);
        setIndeterminate(list.length > 0 && list.length < plainOptions.length);
    };

    const onCheckAllChange = (e) => {
        const checked = e.target.checked;
        const newCheckedList = checked ? plainOptions : [];
        form.setFieldsValue({ queue: newCheckedList });
        setCheckAll(checked);
        setIndeterminate(false);
    };

    const handleOk = () => {
        form.validateFields()
            .then((values) => {
                const data = {
                    role: record?.id,
                    queue: values.queue,
                };
                onCreate(data);
            })
            .catch((errorInfo) => {
                // message.warning("Please select at least one queue.");
            });
    };

    return (
        <Modal
            visible={visible}
            title="Assign Queue"
            okText="Submit"
            cancelText="Cancel"
            onCancel={() => setVisible(false)}
            onOk={handleOk}
        >
            <Spin spinning={isLoading}>
                <Form form={form} layout="vertical">
                    <Checkbox
                        indeterminate={indeterminate}
                        onChange={onCheckAllChange}
                        checked={checkAll}
                    >
                        Check all
                    </Checkbox>
                    <Divider />
                    <Form.Item
                        name="queue"
                    // rules={[{ required: true, message: 'Please select at least one queue.' }]}
                    >
                        <Checkbox.Group
                            options={plainOptions}
                            onChange={onChange}
                        />
                    </Form.Item>
                </Form>
            </Spin>
        </Modal>
    );
};

export default AssignQueue;
