import { DatePicker, Form, Input, Modal, Spin } from "antd";


export const FormDataFilter = ({ visible, setVisible, onCreate, isLoading = false }) => {
    const [form] = Form.useForm()

    return (
        <Modal
            visible={visible}
            onCancel={() => {
                form.resetFields()
                setVisible(false)
            }}
            onOk={() => {
                form
                    .validateFields()
                    .then((values) => {
                        console.log("validated")
                        onCreate(values);
                        form.resetFields();
                    })
                    .catch((info) => {
                        console.log('Validate Failed:', info);
                    })
            }}
        >
            <Spin spinning={isLoading}>
                <Form
                    form={form}
                    layout="vertical"
                    name="form_in_modal"
                >
                    <Form.Item
                        name="time"
                        label="Time"
                    >
                        <DatePicker.RangePicker showTime />
                    </Form.Item>

                    <Form.Item
                        name="source"
                        label="Source"
                    >
                        <Input />
                    </Form.Item>

                    <Form.Item
                        name="destination"
                        label="Destination"
                    >
                        <Input />
                    </Form.Item>

                    <Form.Item
                        name="data"
                        label="Data"
                        help="Search in data"
                    >
                        <Input />
                    </Form.Item>

                </Form>
            </Spin>

        </Modal>
    )
}