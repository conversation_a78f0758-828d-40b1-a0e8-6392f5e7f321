import { DatePicker, Form, Input, Modal, Switch } from "antd";
import React, { useEffect } from "react";
import moment from "moment";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";

const EditCampaign = (props) => {
    const [form] = Form.useForm();

    // When the modal becomes visible, initialize the form with the campaign values
    useEffect(() => {
        if (props.showEditCampaign) {
            form.setFieldsValue({
                name: props.item.name,
                start_time: props.item.start_time ? moment(props.item.start_time) : null,
                end_time: props.item.end_time ? moment(props.item.end_time) : null,
                status: props.item.status,
            });
        }
    }, [props.showEditCampaign, props.item, form]);

    return (
        <Modal
            open={props.showEditCampaign}
            title={`Edit campaign: ${props.item.name}`}
            okText="Update"
            cancelText="Cancel"
            onCancel={() => {
                form.resetFields();
                props.onCancel();
            }}
            onOk={() => {
                form
                    .validateFields()
                    .then((values) => {
                        // Since this is EditCampaign, we only call onUpdate
                        const id = props.item.id;
                        props.onUpdate({ ...values, id });
                        form.resetFields();
                    })
                    .catch((info) => {
                        console.log("Validate Failed:", info);
                    });
            }}
        >
            <Form form={form} layout="vertical" name="form_in_modal">
                <Form.Item
                    name="name"
                    label="Name"
                    rules={[
                        {
                            required: true,
                            message: "Please input the title of the campaign.",
                        },
                    ]}
                >
                    <Input />
                </Form.Item>

                <Form.Item
                    label="Start Time"
                    name="start_time"
                    rules={[
                        {
                            required: true,
                            message: "Please input the start time of the campaign.",
                        },
                    ]}
                >
                    <DatePicker showTime />
                </Form.Item>

                <Form.Item
                    label="End Time"
                    name="end_time"
                    rules={[
                        {
                            required: true,
                            message: "Please input the end time of the campaign.",
                        },
                        ({ getFieldValue }) => ({
                            validator(_, value) {
                                const startTime = getFieldValue("start_time");
                                if (!value || !startTime || value.isAfter(startTime)) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(
                                    new Error("End time must be after the start time.")
                                );
                            },
                        }),
                    ]}
                >
                    <DatePicker showTime />
                </Form.Item>

                <Form.Item name="status" label="Activate" valuePropName="checked">
                    <Switch
                        checkedChildren={<CheckOutlined />}
                        unCheckedChildren={<CloseOutlined />}
                    />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default EditCampaign;


// import { DatePicker, Form, Input, Modal, Switch } from "antd";
// import React, { useEffect } from "react";
// import moment from "moment";
// import { CheckOutlined, CloseOutlined } from "@ant-design/icons";

// const EditCampaign = (props) => {
//     const [form] = Form.useForm();

//     useEffect(() => {
//         form.setFieldsValue({
//             name: props.item.name,
//             start_time: props.item.start_time ? moment(props.item.start_time) : null,
//             end_time: props.item.end_time ? moment(props.item.end_time) : null,
//         });
//     }, [props.showEditCampaign]);

//     useEffect(() => {
//         form.resetFields();
//     }, [props.showAddCampaign]);

//     return (
//         <Modal
//             open={props.showAddCampaign || props.showEditCampaign}
//             title={
//                 props.showEditCampaign
//                     ? `Edit campaign: ${props.item.name}`
//                     : "Create a new campaign"
//             }
//             okText="Update"
//             cancelText="Cancel"
//             onCancel={props.onCancel}
//             onOk={() => {
//                 form
//                     .validateFields()
//                     .then((values) => {
//                         if (props.showEditCampaign) {
//                             const id = props.item.id;
//                             props.onUpdate({ ...values, id });
//                         } else {
//                             props.onCreate(values);
//                         }
//                     })
//                     .catch((info) => {
//                         console.log("Validate Failed:", info);
//                     });
//             }}
//         >
//             <Form form={form} layout="vertical" name="form_in_modal">
//                 <Form.Item
//                     name="name"
//                     label="Name"
//                     rules={[
//                         {
//                             required: true,
//                             message: "Please input the title of the campaign.",
//                         },
//                     ]}
//                 >
//                     <Input />
//                 </Form.Item>

//                 <Form.Item
//                     label="Start Time"
//                     name="start_time"
//                     rules={[
//                         {
//                             required: true,
//                             message: "Please input the start time of the campaign.",
//                         },
//                     ]}
//                 >
//                     <DatePicker showTime />
//                 </Form.Item>

//                 <Form.Item
//                     label="End Time"
//                     name="end_time"
//                     rules={[
//                         {
//                             required: true,
//                             message: "Please input the end time of the campaign.",
//                         },
//                         ({ getFieldValue }) => ({
//                             validator(_, value) {
//                                 const startTime = getFieldValue("start_time");
//                                 if (!value || !startTime || value.isAfter(startTime)) {
//                                     return Promise.resolve();
//                                 }
//                                 return Promise.reject(
//                                     new Error("End time must be after the start time.")
//                                 );
//                             },
//                         }),
//                     ]}
//                 >
//                     <DatePicker showTime />
//                 </Form.Item>

//                 <Form.Item name="status" label="Activate" valuePropName="checked">
//                     <Switch
//                         checkedChildren={<CheckOutlined />}
//                         unCheckedChildren={<CloseOutlined />}
//                     />
//                 </Form.Item>
//             </Form>
//         </Modal>
//     );
// };

// export default EditCampaign;