import { useDispatch, useSelector } from "react-redux";
import { DatePicker, Form, Input, Modal, Select, Spin } from "antd";
import { useEffect, useState } from "react"
import { getQueues } from "../../Actions/QueueActions";
import apiClient from "../../Shared/apiClient";

export const InboundAgentSummaryFilter = ({ visible, setVisible, onCreate, isLoading }) => {
    const [form] = Form.useForm()
    // const QueueState = useSelector(state => state.QueueReducer)
    // const [showQueue, setShowQueue] = useState(false)
    const dispatch = useDispatch()
    const [queues, setQueues] = useState([])
    useEffect(() => {
        if (visible) {
            // dispatch(getQueues())
            // setShowQueue(true)
        }
    }, [visible])
    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data))
    }, [])
    return (
        <Spin spinning={false}>
            <Modal
                visible={visible}
                onCancel={() => {
                    form.resetFields()
                    setVisible(false)
                }}
                onOk={() => {
                    form
                        .validateFields()
                        .then((values) => {
                            console.log("validated")
                            onCreate(values);
                            form.resetFields();
                        })
                        .catch((info) => {
                            console.log('Validate Failed:', info);
                        })
                }}
            >
                <Form
                    form={form}
                    layout="vertical"
                    name="form_in_modal"
                >
                    <Form.Item
                        name="time"
                        label="Time"
                    >
                        <DatePicker.RangePicker showTime />
                    </Form.Item>
                    <Form.Item colon={false} name="queue" label="Queue" >
                        <Select placeholder="Select Queue" style={{ width: '50%' }}>
                            {queues && queues.map((queue, index) => <Select.Option key={index} value={queue.name}>
                                {queue.name}
                            </Select.Option>)}
                        </Select>
                    </Form.Item>
                </Form>

            </Modal>
        </Spin>
    );
}

export default InboundAgentSummaryFilter;