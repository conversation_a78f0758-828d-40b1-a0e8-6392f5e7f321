import * as ActionTypes from "../Constants/CampaignConstants"
import {CAMPAIGN_MONITOR_SUCCESS} from "../Constants/CampaignConstants";

const initial = {
    campaigns: [],
    campaignMonitor: [],
    campaignMonitoring: [],
    errMess: null,
    isLoading: false,
    message: null
}

export const CampaignReducer = (state = initial, action) => {
    switch (action.type) {
        case ActionTypes.ALL_CAMPAIGNS:
            return { ...state, isLoading: false, campaigns: action.payload, errMess: null, message: null }
        case ActionTypes.CAMPAIGN_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.CAMPAIGN_SUCCESS:
            return { ...state, isLoading: false, message: action.payload, errMess: null }
        case ActionTypes.CAMPAIGN_FAILED:
            return { ...state, isLoading: false, errMess: action.payload, message: null }
        case ActionTypes.CAMPAIGN_MONITOR_SUCCESS:
            return { ...state, isLoading: false, campaignMonitor: action.payload, errMess: null, message: null }
        case ActionTypes.CAMPAIGN_MONITORING_SUCCESS:
            return {...state, isLoading: false, campaignMonitoring: action.payload, message: null, errMess: null}
        default:
            return state
    }
}