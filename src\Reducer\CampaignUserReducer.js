import * as ActionTypes from "../Constants/CampaignUserConstants"

const initialState = {
    campaignUsers: [],
    isLoading: false,
    errMess: false,
    message: false
}

export const CampaignUserReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return { ...state }
        case ActionTypes.CAMPAIGN_USER_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.CAMPAIGN_USER_SUCCESS:
            return { ...state, isLoading: false, errMess: false, campaignUsers: action.payload }
        case ActionTypes.CAMPAIGN_USER_FAILED:
            return { ...state, isLoading: false, errMess: action.payload }
        case ActionTypes.CAMPAIGN_USER_SUCCESS_MESSAGE:
            return { ...state, isLoading: false, message: action.payload }
    }
}