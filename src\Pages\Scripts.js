import { useState, useEffect } from "react"
import { Button, Card, Form, Input, Modal, Select, Space, Spin, Switch, Table } from "antd";
import { DeleteOutlined, EditOutlined, PlusCircleOutlined, SyncOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { deleteScript, getScripts, patchScript, postScript } from "../Actions/ScriptActions";
import ReactQuill from "react-quill"
import 'react-quill/dist/quill.snow.css'
import { openNotificationWithIcon } from "../Shared/notification";
import { getQueues } from "../Actions/QueueActions";
import apiClient from "../Shared/apiClient";

export const Scripts = () => {

    const dispatch = useDispatch()
    const scriptState = useSelector(state => state.ScriptReducer)

    const [addScript, setAddScript] = useState(false)
    const [item, setItem] = useState(false)
    const [scriptId, setScriptId] = useState(null)
    const [status, setStatus] = useState(null)


    useEffect(() => {
        dispatch(getScripts())
    }, [])

    // useEffect(() => {
    //     if (scriptState.errMess)
    //         openNotificationWithIcon('error', scriptState.errMess)
    // }, [scriptState.errMess])

    useEffect(() => {
        if (scriptState.errMess) {

            const errorMessage = scriptState?.errMess?.message ||
                Object.values(scriptState?.errMess?.errors || {})[0][0];

            openNotificationWithIcon('error', errorMessage || "Error.");
        }
    }, [scriptState.errMess]);


    useEffect(() => {
        if (scriptState.message)
            openNotificationWithIcon('success', scriptState.message)
    }, [scriptState.message])

    useEffect(() => {
        if (scriptId && status !== null) {
            handleScript(status);
        }
    }, [scriptId, status]);

    const handleScript = (status) => {
        console.log("id", scriptId)
        apiClient.put(`api/scriptUpdate/${scriptId}`, { "status": status }).then((res) => {
            console.log("response", res.data)
            dispatch(getScripts())
        }).catch((e) => console.log("Error", e))
    }

    const handleActive = (check) => {
        setStatus(check);
    };

    // const onSelectChange = (newSelectedRowKeys,data) => {
    //     console.log('selectedRowKeys changed: ', newSelectedRowKeys);
    //     setSelectedRowKeys(newSelectedRowKeys);
    // };
    // const rowSelection = {
    //     selectedRowKeys,
    //     onChange: onSelectChange,
    // };
    // useEffect(()=>{

    // },[])
    const columns = [
        {
            title: 'Id',
            dataIndex: 'id',
            key: 'id'
        },
        {
            title: 'Name',
            dataIndex: 'name',
            key: 'name'
        },
        {
            title: 'Created By',
            dataIndex: 'created_by',
            key: 'created_by',
            render: (text, item) => item.owner.name
        },
        {
            title: 'Queue',
            dataIndex: 'queue_name',
            key: 'queue'
        },
        {
            title: 'Actions',
            key: 'actions',
            render: (text, item, index) => <Space>

                <Button onClick={() => setItem(item)} icon={<EditOutlined />} />
                <Button onClick={() => dispatch(deleteScript(item.id))} type="primary" danger icon={<DeleteOutlined />} />

            </Space>
        },
        {
            title: 'Status',
            key: 'status',
            render: (text, item, index) => <Space>

                <Switch checked={scriptState.scripts[index].status} onChange={(e) => {
                    setScriptId(item?.id)
                    handleActive(e)
                }}
                />
            </Space>
        }
        // onClick={() => setScriptId(item?.id)}
    ]

    const addProps = {
        addScript,
        setAddScript,
        item,
        setItem,
        loading: scriptState.isLoading
    }
    console.log("scriptState.scripts", scriptState.scripts)
    return (
        <Card
            title="Scripts"
            extra={<Button onClick={() => setAddScript(true)} icon={<PlusCircleOutlined />}>Add scripts</Button>}
        >
            <Spin indicator={<SyncOutlined spin />} spinning={scriptState.isLoading}>
                <Table dataSource={scriptState.scripts} rowKey={"id"} columns={columns} />
                <AddScript {...addProps} />
            </Spin>
        </Card>
    )
}

const AddScript = ({ addScript, setAddScript, item, setItem, loading }) => {

    const [form] = Form.useForm()
    const dispatch = useDispatch()
    const queueState = useSelector(state => state.QueueReducer)

    useEffect(() => dispatch(getQueues()), [])

    useEffect(() => form.setFieldsValue({ name: item.name, queue: item.queue_name, content: item.content }), [item])

    return (
        <Modal
            title={item ? `Edit script: ${item.name}` : 'Add script'}
            visible={addScript || item}
            onOk={() => form.validateFields()
                .then(values => item ? dispatch(patchScript({ ...values, id: item.id })) : dispatch(postScript(values)))
                .then(() => form.resetFields())
                .then(() => setAddScript(false))
                .then(() => setItem(false))
                // .catch(e => openNotificationWithIcon('error', e.message))
            }
            onCancel={() => {
                setAddScript(false)
                setItem(false)
            }}
            okText={item ? "Edit" : "Add"} 
        >
            <Spin spinning={loading}>
                <Form
                    form={form}
                    layout="vertical"
                >
                    <Form.Item
                        label="Name"
                        name="name"
                        rules={[{ required: true, message: "The name field is required." }]}
                    >
                        <Input />
                    </Form.Item>

                    <Form.Item
                        label="Queue"
                        name="queue"
                        rules={[{ required: true, message: "The queue field is required." }]}
                    >
                        <Select>
                            {queueState.queues && queueState.queues.map((value) => (
                                <Select.Option key={value.name}>{value.name}</Select.Option>
                            ))}
                        </Select>
                    </Form.Item>

                    <Form.Item
                        name="content"
                        label="Content"
                        rules={[{ required: true, message: "The content field is required." }]}
                    >
                        <TextEditor />
                    </Form.Item>

                </Form>
            </Spin>
        </Modal>
    )
}

const TextEditor = ({ value, onChange, placeholder }) => {

    const modules = {
        toolbar: [
            [{ header: [1, 2, false] }],
            ['bold', 'italic', 'underline', 'strike', 'blockquote'],
            [
                { list: 'ordered' },
                { list: 'bullet' },
                { indent: '-1' },
                { indent: '+1' },
            ],
            ['link', 'code'],
            ['clean'],
        ],
    }

    const formats = [
        'header',
        'bold',
        'italic',
        'underline',
        'strike',
        'blockquote',
        'list',
        'bullet',
        'indent',
        'link',
        'code',
    ]

    return (
        <>
            <ReactQuill
                theme="snow"
                value={value || ''}
                modules={modules}
                formats={formats}
                onChange={onChange}
                placeholder={placeholder}
            />
        </>
    )
}