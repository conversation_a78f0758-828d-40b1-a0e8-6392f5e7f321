import { Form, Input, Modal, Radio, Select, Spin, Switch } from "antd";
import { useEffect, useMemo, useState } from "react";
import { fetchAllQueues, getQueues } from "../../Actions/QueueActions";
import { useDispatch, useSelector } from "react-redux";

const EditAgent = ({ visible, onCreate, record, setVisible }) => {
    const [form] = Form.useForm();
    const [showAgentFields, setShowAgentFields] = useState(false);
    const [showQueue, setShowQueue] = useState(false);
    const [autoCallAnswerCheck, setAutoCallAnswerCheck] = useState(record?.auto_call_answer);
    const dispatch = useDispatch();
    const QueueState = useSelector((state) => state.QueueReducer);
    const [callBackCheck, setCallBackRequest] = useState(false);

    useEffect(() => {
        dispatch(fetchAllQueues());
    }, [dispatch]);

    useEffect(() => {
        if (record && visible) {
            // Initialize form values
            form.setFieldsValue(record);
            setShowAgentFields(true);

            // Set showQueue based on the current type in the record
            setShowQueue(record.type !== "Normal" && record.type !== "Outbound");

            // If type is Outbound, ensure queue is undefined
            if (record.type === "Outbound") {
                form.setFieldsValue({ queue: undefined });
            }
        }
    }, [record, visible, form]);

    const onSelectChange = (option) => {
        setShowAgentFields(true);
        if (option === "Outbound") {
            // Hide queue and clear its value when type is Outbound
            setShowQueue(false);
            form.setFieldsValue({ queue: undefined });
        } else {
            // For other types, show the queue field and fetch queues if needed
            dispatch(getQueues());
            setShowQueue(true);
        }
    };

    useEffect(() => {
        if (record?.view_callback == "1") {
            setCallBackRequest(true);
        } else {
            setCallBackRequest(false);
        }
    }, [record?.view_callback, visible]);


    return (
        <Modal
            visible={visible}
            title={`Edit: ${record?.name} <${record?.email}>`}
            okText="Update"
            cancelText="Cancel"
            closable={true}
            onCancel={() => {
                setVisible(false);
            }}
            onOk={() => {
                form
                    .validateFields()
                    .then((values) => {
                        let updatedValues = { ...values };

                        // Always clear the queue value if the type is Outbound
                        if (updatedValues.type === "Outbound") {
                            updatedValues.queue = ""; // Set to empty string to ensure it shows as empty in the table
                        }

                        form.resetFields();
                        onCreate({
                            ...updatedValues,
                            auto_call_answer: autoCallAnswerCheck === true ? 1 : 0,
                            hangup_enable: updatedValues?.hangup_enable === true ? 1 : 0,
                            view_callback: callBackCheck === true ? 1 : 0,
                        });
                    })
                    .catch((info) => {
                        console.log("Validate Failed:", info);
                    });
            }}
        >
            <Spin spinning={QueueState.isLoading}>
                <Form form={form} layout="vertical" name="form_in_modal">
                    <Form.Item
                        name="bitrix_id"
                        label="Bitrix Id"
                        rules={[
                            {
                                required: false,
                                message: 'Please input the bitrix id',
                            },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        name="name"
                        label="Name"
                        rules={[
                            {
                                required: true,
                                message: "Please input the name",
                            },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        name="username"
                        label="Username"
                        rules={[
                            {
                                required: true,
                                message: "Please input the username",
                            },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        name="email"
                        label="email"
                        rules={[
                            {
                                required: true,
                                message: "Please input email address",
                            },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        name="type"
                        label="Type"
                        rules={[
                            {
                                required: true,
                                message: "Please select the type",
                            },
                        ]}
                    >
                        <Select onChange={onSelectChange}>
                            <Select.Option value="Blended" key="Blended">
                                Blended
                            </Select.Option>
                            <Select.Option value="Inbound" key="Inbound">
                                Inbound
                            </Select.Option>
                            <Select.Option value="Outbound" key="Outbound">
                                Outbound
                            </Select.Option>
                        </Select>
                    </Form.Item>
                    <Form.Item
                        name="view_callback"
                        label="Callback Request"
                        initialValue={true}
                    >
                        <Switch
                            checked={callBackCheck}
                            onChange={(val) => setCallBackRequest(val)}
                        />
                    </Form.Item>

                    {showAgentFields && (
                        <>
                            <Form.Item
                                name="auth_username"
                                label="Auth Username"
                                rules={[
                                    { required: true, message: "Auth username is required" },
                                ]}
                            >
                                <Input />
                            </Form.Item>
                            <Form.Item
                                name="auth_password"
                                label="Auth Password"
                                rules={[
                                    { required: true, message: "Auth password is required" },
                                ]}
                            >
                                <Input />
                            </Form.Item>
                            {showQueue && (
                                <Form.Item
                                    name="queue"
                                    label="Queue"
                                    rules={[
                                        {
                                            required: true,
                                            message: "Please select the queue",
                                        },
                                    ]}
                                >
                                    <Select>
                                        {QueueState.allQueues.map((value) => (
                                            <Select.Option value={value.name} key={value.name}>
                                                {value.name}
                                            </Select.Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                            )}
                        </>
                    )}
                </Form>
            </Spin>
        </Modal>
    );
};

export default EditAgent;

// import { Form, Input, Modal, Radio, Select, Spin, Switch } from "antd";
// import { useEffect, useMemo, useState } from "react";
// import { getQueues } from "../../Actions/QueueActions";
// import { useDispatch, useSelector } from "react-redux";

// const EditAgent = ({ visible, onCreate, record, setVisible }) => {
//     const [form] = Form.useForm();
//     const [showAgentFields, setShowAgentFields] = useState(false);
//     const [showQueue, setShowQueue] = useState(false);
//     const [autoCallAnswerCheck, setAutoCallAnswerCheck] = useState(record?.auto_call_answer);
//     const dispatch = useDispatch();
//     const QueueState = useSelector((state) => state.QueueReducer);

//     useEffect(() => {
//         dispatch(getQueues());
//     }, [dispatch]);

//     useEffect(() => {
//         // Initialize form values and field visibility based on the record
//         form.setFieldsValue(record);
//         setShowAgentFields(true);
//         // Show queue only if type is not "Normal" and not "Outbound"
//         setShowQueue(record ? record.type !== "Normal" && record.type !== "Outbound" : false);
//     }, [record, visible, form]);

//     const onSelectChange = (option) => {
//         setShowAgentFields(true);
//         if (option === "Outbound") {
//             // Hide queue and clear its value when type is Outbound
//             setShowQueue(false);
//             form.setFieldsValue({ queue: undefined });
//         } else {
//             // For other types, show the queue field and fetch queues if needed
//             dispatch(getQueues());
//             setShowQueue(true);
//         }
//     };

//     return (
//         <Modal
//             visible={visible}
//             title={`Edit: ${record?.name} <${record?.email}>`}
//             okText="Update"
//             cancelText="Cancel"
//             closable={true}
//             onCancel={() => {
//                 setVisible(false);
//             }}
//             onOk={() => {
//                 form
//                     .validateFields()
//                     .then((values) => {
//                         // Clear the queue value if the type is Outbound
//                         if (values.type === "Outbound") {
//                             values.queue = undefined;
//                         }
//                         form.resetFields();
//                         onCreate({
//                             ...values,
//                             auto_call_answer: autoCallAnswerCheck === true ? 1 : 0,
//                             hangup_enable: values?.hangup_enable === true ? 1 : 0,
//                         });
//                     })
//                     .catch((info) => {
//                         console.log("Validate Failed:", info);
//                     });
//             }}
//         >
//             <Spin spinning={QueueState.isLoading}>
//                 <Form form={form} layout="vertical" name="form_in_modal">
//                     <Form.Item
//                         name="bitrix_id"
//                         label="Bitrix Id"
//                         rules={[
//                             {
//                                 required: false,
//                                 message: 'Please input the bitrix id',
//                             },
//                         ]}
//                     >
//                         <Input />
//                     </Form.Item>
//                     <Form.Item
//                         name="name"
//                         label="Name"
//                         rules={[
//                             {
//                                 required: true,
//                                 message: "Please input the name",
//                             },
//                         ]}
//                     >
//                         <Input />
//                     </Form.Item>
//                     <Form.Item
//                         name="username"
//                         label="Username"
//                         rules={[
//                             {
//                                 required: true,
//                                 message: "Please input the username",
//                             },
//                         ]}
//                     >
//                         <Input />
//                     </Form.Item>
//                     <Form.Item
//                         name="email"
//                         label="email"
//                         rules={[
//                             {
//                                 required: true,
//                                 message: "Please input email address",
//                             },
//                         ]}
//                     >
//                         <Input />
//                     </Form.Item>
//                     <Form.Item
//                         name="type"
//                         label="Type"
//                         rules={[
//                             {
//                                 required: true,
//                                 message: "Please select the type",
//                             },
//                         ]}
//                     >
//                         <Select onSelect={onSelectChange}>
//                             <Select.Option value="Blended" key="Blended">
//                                 Blended
//                             </Select.Option>
//                             <Select.Option value="Inbound" key="Inbound">
//                                 Inbound
//                             </Select.Option>
//                             <Select.Option value="Outbound" key="Outbound">
//                                 Outbound
//                             </Select.Option>
//                         </Select>
//                     </Form.Item>
//                     {showAgentFields && (
//                         <>
//                             <Form.Item
//                                 name="auth_username"
//                                 label="Auth Username"
//                                 rules={[
//                                     { required: true, message: "Auth username is required" },
//                                 ]}
//                             >
//                                 <Input />
//                             </Form.Item>
//                             <Form.Item
//                                 name="auth_password"
//                                 label="Auth Password"
//                                 rules={[
//                                     { required: true, message: "Auth password is required" },
//                                 ]}
//                             >
//                                 <Input />
//                             </Form.Item>
//                             {showQueue && (
//                                 <Form.Item
//                                     name="queue"
//                                     label="Queue"
//                                     rules={[
//                                         {
//                                             required: true,
//                                             message: "Please select the queue",
//                                         },
//                                     ]}
//                                 >
//                                     <Select>
//                                         {QueueState.queues.map((value) => (
//                                             <Select.Option value={value.name} key={value.name}>
//                                                 {value.name}
//                                             </Select.Option>
//                                         ))}
//                                     </Select>
//                                 </Form.Item>
//                             )}
//                         </>
//                     )}
//                 </Form>
//             </Spin>
//         </Modal>
//     );
// };

// export default EditAgent;