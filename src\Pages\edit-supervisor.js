import {createSupervisor, getDataByID, updateSupervisor} from "../Actions/SupervisorAction";
import {useHistory, useParams} from "react-router";
import {useDispatch, useSelector} from "react-redux";
import {useEffect, useState} from "react"
import {SupervisorReducer} from "../Reducer/SupervisorReducer";
import {Button, Card, Form, Input} from "antd";
import {openNotificationWithIcon} from "../Shared/notification";
import {handleError} from "../Shared/handleError";


export const EditSupervisor = () => {

    const {id} = useParams()
    const dispatch = useDispatch()
    const supervisor = useSelector(state => state.SupervisorReducer)
    const [form] = Form.useForm()
    let history = useHistory()
//old useEffect for bk
//on edit first click the data not being render 
    // useEffect(() => {
    //     if(supervisor.supervisorData){
    //         form.setFieldsValue(supervisor.supervisorData)
    //     }
    //     if(supervisor.message){
    //         openNotificationWithIcon('success', supervisor.message)
    //         history.push('/supervisor')
    //     }
    //     else if(supervisor.errMess) {
    //         openNotificationWithIcon('error', handleError(supervisor.errMess))
    //         console.log(handleError(supervisor.errMess))
    //         console.log(supervisor.errMess)
    //     }
    // },[supervisor])

    // useEffect(() => {
    //     dispatch(getDataByID(id))
    // },[])




    // Fetch supervisor data on mount
useEffect(() => {
    dispatch(getDataByID(id));
}, [dispatch, id]);


useEffect(() => {
    if (supervisor.supervisorData && Object.keys(supervisor.supervisorData).length > 0) {
        form.setFieldsValue(supervisor.supervisorData);
    }
}, [form, supervisor.supervisorData]);

// Handle success or error messages
useEffect(() => {
    if (supervisor.message) {
        // openNotificationWithIcon('success', supervisor.message);
        history.push('/supervisor');
    } else if (supervisor.errMess) {
        openNotificationWithIcon('error', handleError(supervisor.errMess));
        console.error(handleError(supervisor.errMess));
    }
}, [supervisor.message, supervisor.errMess]);    


    function onUpdate(values) {
        if(supervisor.supervisorData) {
            values = {...values, id: supervisor.supervisorData.id}
            dispatch(updateSupervisor(supervisor.supervisorData.id, values))
        }
    }


    return (<Card>
        <Form
            form={form}
            name="form_in_modal"
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 16 }}
            onFinish={onUpdate}
        >

            <Form.Item
                name="name"
                label="Name"
                rules={[
                    {
                        required: true,
                        message: 'Please input the name',
                    },
                ]}
            >
                <Input />
            </Form.Item>
            <Form.Item
                name="username"
                label="Username"
                rules={[
                    {
                        required: true,
                        message: 'Please input the username',
                    },
                ]}
            >
                <Input />
            </Form.Item>
            <Form.Item
                name="email"
                label="E-Mail"
                rules={[
                    {
                        required: true,
                        message: 'Please input the email address',
                    },
                ]}
            >
                <Input />
            </Form.Item>

            <Form.Item name="auth_username" label="Auth Username"  rules={[
                {
                    required: true,
                    message: 'Please input the auth username',
                },
            ]}>
                <Input/>
            </Form.Item>
            <Form.Item name="auth_password" label="Auth Password" rules={[
                {
                    required: true,
                    message: 'Please input the auth password',
                },
            ]}>
                <Input />
            </Form.Item>

            <Form.Item wrapperCol={{ offset: 4, span: 16 }}>
                <Button type="primary" htmlType="submit">
                    Update
                </Button>
            </Form.Item>
        </Form>
    </Card>)
}