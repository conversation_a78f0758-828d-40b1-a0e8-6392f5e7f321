import { useDispatch, useSelector } from "react-redux";
import { useEffect, useRef, useState } from "react";
import { openNotificationWithIcon } from "../../Shared/notification";
import { Button, Card, DatePicker, Form, Input, Modal, Select, Space, Spin, Table, Typography } from "antd";
import { DownloadOutlined, FilterOutlined, ReloadOutlined, SearchOutlined, SyncOutlined } from "@ant-design/icons";
import { CSVLink } from "react-csv";
import { abandonCallHourReset, getAbandonedCallHour } from "../../Actions/AbandonedAction";
import apiClient from "../../Shared/apiClient";
import Highlighter from "react-highlight-words";

export const AbandonedCallHour = () => {

    const abandonedReducer = useSelector(state => state.AbandonedReducer)
    const [hour, setHour] = useState(3)
    const [filterVisible, setFilterVisible] = useState(false);
    const [resetFilter, setResetFilter] = useState(false)
    const dispatch = useDispatch()
    const arr = Array.from({ length: 24 }, () => Math.floor(Math.random() * 40))
    const [queues, setQueues] = useState([]);

    useEffect(() => {

        apiClient.get('api/queue').then((res) => {
            setQueues(res.data);
            console.log("ff", res.data)
        })
    }, [filterVisible])

    useEffect(() => {
        if (abandonedReducer.errMess !== '') openNotificationWithIcon('error', abandonedReducer.errMess)
    }, [abandonedReducer.errMess])

    // useEffect(() => dispatch(getAbandonedCallHour()), [])

    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')

    const searchInput = useRef()

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }


    const columns = [
        {
            title: 'Date',
            dataIndex: 'date',
            key: 'date',
        },
        {
            title: 'Time',
            dataIndex: 'time',
            key: 'time'
        },
        {
            title: 'Number',
            dataIndex: 'src',
            key: 'src',
            ...getColumnSearchProps('src')
        },
        // {
        //     title: 'Duration',
        //     dataIndex: 'duration',
        //     key: 'duration'
        // }
    ]

    const { RangePicker } = DatePicker

    const onFinish = v => {
        console.log(v)
        dispatch(getAbandonedCallHour(v))
        setHour(v.hour)
    }

    const resetFormFilter = () => {
        setResetFilter(true);
        dispatch(abandonCallHourReset())
    }

    return (
        <>
            {/* <Card>
                <Form layout="vertical" onFinish={onFinish}>
                    <Form.Item
                        label="Select Hour (default selected 3 Hours data)"
                        name="hour"
                    >
                        <Select placeholder="Select Hour">
                            <Select.Option value="">Select Hour</Select.Option>
                            {arr && arr.map((v, index) => <Select.Option value={index + 1}>
                                {index + 1}
                            </Select.Option>)}
                        </Select>
                    </Form.Item>
                    <Form.Item>
                        <Button htmlType="submit">Submit</Button>
                    </Form.Item>
                </Form>
            </Card> */}
            <br />
            <Table
                title={() => <Typography.Text>
                    Abandoned {hour} Hours Report

                    <span style={{ float: 'right' }}>
                        <Space>
                            <Button
                                danger
                                type="primary"
                                icon={<ReloadOutlined />}
                                onClick={() => {
                                    resetFormFilter();
                                }}
                            >Reset Filter</Button>
                            <Button
                                icon={<FilterOutlined />}
                                onClick={() => {
                                    setFilterVisible(true);
                                }} >Filter</Button>
                            {/* <CSVLink data={abandonedReducer.abandonedPerHour} filename="abandonedPerHourReport.csv">
                            <Button icon={<CloudDownloadOutlined />}>Download</Button>
                        </CSVLink> */}
                            <CSVLink data={abandonedReducer?.abandonedPerHour} filename="abandonedPerHourReport.csv">
                                <Button
                                    disabled={abandonedReducer.abandonedPerHour.length == 0}
                                    type={"primary"}
                                    target="_blank"
                                    icon={<DownloadOutlined />}
                                >
                                    Download
                                </Button>
                            </CSVLink>
                        </Space>
                    </span>

                </Typography.Text>}
                dataSource={abandonedReducer.abandonedPerHour || []}
                columns={columns}
                scroll={{ x: 1100 }}
                bordered
                loading={{ spinning: abandonedReducer.isLoading, indicator: <SyncOutlined spin /> }}
            />

            <AbandonFilterHourCall arr={arr} resetFilter={resetFilter} queues={queues} setQueues={setQueues} visible={filterVisible} setHour={setHour} setVisible={setFilterVisible} />

        </>
    )
}

const AbandonFilterHourCall = ({ queues, setQueues, visible, setVisible, arr, resetFilter, setHour, buttonLoading }) => {

    const [form] = Form.useForm()
    const [pagination, setPagination] = useState({ current: 1, pageSize: 50 })

    const abandonedReducer = useSelector(state => state.AbandonedReducer)
    const dispatch = useDispatch();

    useEffect(() => {
        if (abandonedReducer.errMess !== '') openNotificationWithIcon('error', abandonedReducer.errMess)
    }, [abandonedReducer.errMess])



    useEffect(() => {
        if (resetFilter) {
            form.resetFields()
        }
    }, [resetFilter])

    const onFinish = v => {
        console.log(v)
        dispatch(getAbandonedCallHour(v))
        setHour(v.hour)
    }

    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            title="Abandon Filter"
            size="small"
            okText="Submit"
            onOk={() => {
                form.validateFields()
                    .then(data => onFinish(data))
                    .catch(e => console.log(e))

                setVisible(false)
            }
            }
        // oKButtonProps={{
        //     loading: buttonLoading
        // }}
        >
            <Form layout="vertical" form={form}>
                <Form.Item
                    label="Select Hour (default selected 3 Hours data)"
                    name="hour"
                >
                    <Select placeholder="Select Hour">
                        <Select.Option value="">Select Hour</Select.Option>
                        {arr && arr.map((v, index) => <Select.Option value={index + 1}>
                            {index + 1}
                        </Select.Option>)}
                    </Select>
                </Form.Item>
                <Form.Item
                    label="Queue"
                    name="queue"
                >
                    <Select placeholder="Queue Option">
                        <Select.Option>Select Queue</Select.Option>

                        {queues.map((elm, index) => {
                            return (
                                <Select.Option value={elm.name}>
                                    {elm.name}
                                </Select.Option>)

                        })}

                    </Select>
                </Form.Item>
                {/* <Form.Item>
                    <Button htmlType="submit">Submit</Button>
                </Form.Item> */}
            </Form>
        </Modal>
    )
}