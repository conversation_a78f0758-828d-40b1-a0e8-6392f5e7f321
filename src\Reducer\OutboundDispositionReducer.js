
import * as ActionTypes from "../Constants/OutboundDispositionConstants"

const initialState = {
    data: [],
    errMess: '',
    isLoading: false
}

export const OutboundDispositionReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.OUTBOUND_DISPOSITION_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.OUTBOUND_DISPOSITION_SUCCESS:
            return { ...state, isLoading: false, data: action.payload }
        case ActionTypes.OUTBOUND_DISPOSITION_FAILED:
            return { ...state, isLoading: false, errMess: action.payload }
        case ActionTypes.OUTBOUND_DISPOSITION_RESET:
            return { ...state, data: [] }
    }
}