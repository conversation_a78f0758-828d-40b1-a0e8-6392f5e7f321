import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, Card, DatePicker, Form, Space, Input, Select, Table, Modal } from "antd";
import apiClient from "../../Shared/apiClient";
import moment from "moment";
import { CSVLink } from "react-csv";
import {
    FilterOutlined,
    ReloadOutlined,
    SearchOutlined
} from "@ant-design/icons";
import Highlighter from "react-highlight-words";
import { useDispatch } from "react-redux";
const { RangePicker } = DatePicker;



const { Option } = Select;
const dateFormat = "YYYY-MM-DD";

let columns = []

const ServiceLevelReport = () => {
    const [form] = Form.useForm();
    const [data, setData] = useState([]);
    const [to, setTo] = useState();
    const [from, setFrom] = useState();
    const [selectedType, setselectedType] = useState();
    const [loading, setLoading] = useState(false);
    const [fetchReportCheck, setFetchReportCheck] = useState(true);
    const [showFilter, setShowFilter] = useState(false)
    const [resetFilter, setResetFilter] = useState(false)

    const onDateChange = (date, dateString) => {
        setFrom(moment(dateString[0]));
        setTo(moment(dateString[1]));
    };
    const resetFormFilter = () => {
        // dispatch(getTrunkPerHourReport())
        // dispatch(trunkPerHourReset())
        // console.log(new Date().toLocaleString() + '')
        setData([])
        form.resetFields()
        setResetFilter(true)
    }

    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const searchInput = useRef();

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }


    const onChange = (value) => {
        setselectedType(value);
        setFetchReportCheck(false);
        setData([]);
        if (value === "agent") {
            columns = [
                {
                    title: "Agent",
                    dataIndex: "agent",
                    key: "agent",
                    ...getColumnSearchProps('agent'),
                    width: 150,
                },
                {
                    title: "SLA",
                    dataIndex: "SLA",
                    key: "SLA",
                    ...getColumnSearchProps('SLA'),
                    width: 150,
                },
            ];
        } else {
            columns = [
                {
                    title: "Date",
                    dataIndex: "date",
                    key: "date",
                    ...getColumnSearchProps('date'),
                    width: 150,
                },
                {
                    title: "SLA",
                    dataIndex: "SLA",
                    key: "SLA",
                    ...getColumnSearchProps('SLA'),
                    width: 150,
                },
            ];
        }
    };

    const onSearch = (value) => {
        console.log("search:", value);
    };

    const fetchReport = () => {
        setLoading(true);
        const formValues = form.getFieldsValue(); 
        
            apiClient
            .post(`/api/report/getServiceLevelReport`, {
                from: from?._i,
                to: to?._i,
                type: selectedType,
                queue:formValues.queue
            })
            .then((resp) => {
                setData(resp.data);
                setLoading(false);
                setFetchReportCheck(true);
                form.resetFields();
            })
            .catch((err) => {
                setLoading(false);
                console.log(err.message);
            });
    };

    return (
        <>
            <Card

                style={{ background: "#fff", borderRadius: "2px", padding: 10 }}

            >
                <Table
                    title={data => <>

                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            {`Service Level Report`}
                            <Space>
                                <Button onClick={() => resetFormFilter()} type="danger" icon={<ReloadOutlined />}>Reset Filter</Button>
                                <Button onClick={() => setShowFilter(true)} icon={<FilterOutlined />}>Filter</Button>
                                <CSVLink filename="Service Level Report.csv" data={data}>
                                    <Button disabled={data.length === 0}>Export Report</Button>
                                </CSVLink>
                            </Space>

                        </div>

                    </>}
                    columns={columns}
                    dataSource={data}
                    pagination={false}
                    loading={loading}
                    bordered
                    size="default"
                />

                <ServiceLevelFilter
                    form={form}
                    resetFilter={resetFilter}
                    visible={showFilter}
                    fetchReport={fetchReport}
                    onChange={onChange}
                    onDateChange={onDateChange}
                    // selectedQueue={selectedQueue}
                    selectedType={selectedType}
                    onSearch={onSearch}
                    setselectedType={setselectedType}
                    setVisible={setShowFilter} />


            </Card>
        </>
    );
};

export const ServiceLevelFilter = ({ form, fetchReport, selectedType, onSearch, onDateChange, onChange, visible, setVisible, setSelectedQueue, date }) => {

    const [queues, setQueues] = useState([])

    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data))
    }, [])


    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            okText="Submit"
            onOk={() => form.validateFields()
                .then(value => {
                    // dispatch(getTrunkPerHourReport({ date: filterDate, queue: queue }))
                    fetchReport()
                    setVisible(false)
                })
            }
            // okButtonProps={{
            //     loading: btnLoading,
            //     icon: <SaveOutlined />
            // }}
            title="Service Level Report Filter"
        >
            {/* <Spin spinning={btnLoading} indicator={<SyncOutlined spin />}> */}
            <Form
                form={form}
                layout="vertical"
            >
                <Form.Item name={"picker"}>
                    <RangePicker
                        style={{ width: "100%" }}
                        format={dateFormat}
                        onChange={onDateChange}
                    />
                </Form.Item>
                <Form.Item name={"type"}>
                    <Select
                        showSearch
                        placeholder="Select a Type"
                        optionFilterProp="children"
                        value={selectedType}
                        onChange={onChange}
                        onSearch={onSearch}
                        filterOption={(input, option) =>
                            option.children.toLowerCase().includes(input.toLowerCase())
                        }
                        style={{ marginRight: "10px" }}
                    >
                        <Option value="agent">Agent</Option>
                        <Option value="date">Date</Option>
                    </Select>
                </Form.Item>
                <Form.Item colon={false} name="queue"  >
                    <Select placeholder="Select Queue" >
                        {queues && queues.map((queue, index) => <Select.Option key={index} value={queue.name}>
                            {queue.name}
                        </Select.Option>)}
                    </Select>
                </Form.Item>
            </Form>
            {/* </Spin> */}
        </Modal>
    )
}



export default ServiceLevelReport;