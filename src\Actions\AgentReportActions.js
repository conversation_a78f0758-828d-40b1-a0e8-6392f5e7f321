import * as ActionsType from '../Constants/AgentReportsContant'
import apiClient from "../Shared/apiClient";
import {
    AGENT_PAUSE_REASON,
    AGENT_PAUSE_REASON_FILTER,
    AGENT_REPORT,
    AGENT_CALLER_HANGUP as Call<PERSON><PERSON><PERSON><PERSON>,
    AGENT_CALLER_HANGUP_FILTER as Caller<PERSON>angupFilters,
    AGENT_RING_NO_ANSWER,
    AGENT_RING_NO_ANSWER_Filter,
    AGENT_EXIT_EMPTY_FILTER,
    AGENT_EXIT_EMPTY,
    AGENT_EXIT_WITH_TIMEOUT,
    AGENT_EXIT_WITH_TIMEOUT_FILTER,
    AGENT_CONFIGURED_LOAD_FILTER,
    AGENT_CONFIGURED_LOAD,
    AGENT_COMPLETE_CALLER,
    AGENT_COMPLETE_CALLER_FILTER,
    AGENT_COMPLETE_AGENT,
    AGENT_COMPLETE_AGENT_FILTER,
    AGENT_REPORT_RING_NO_ANSWER_QUEUE_SUMMARY,
    AGENT_REPORT_RING_NO_ANSWER_AGENT_WISE_SUMMARY
} from "../Endpoints/AgentReportsRoutes.js";
import { logoutUser } from "./UserActions";
import { CDR_Report_Filter } from "../Endpoints/CallDetailReportRoutes";
import { handleError } from '../Shared/handleError.js';
import { openNotificationWithIcon } from '../Shared/notification.js';

export const getAllLoginReport = (page = 1, pageSize = 10, data) => dispatch => {
    // update route in  AgentReportRoutes file
    dispatch(loading())
    apiClient.get(AGENT_REPORT + "?page=" + page + "&pageSize=" + pageSize, data).then(response => {
        dispatch(getReport(response.data.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response))
        }
        else
            dispatch(reportFailed(error.message))
    })

}

export const getRingNoAnswerQueueSummaryReport = data => dispatch => {
    // update route in  AgentReportRoutes file
    dispatch(loading())
    apiClient.post(AGENT_REPORT_RING_NO_ANSWER_QUEUE_SUMMARY, data).then(response => {
        dispatch(agentNoAnswerSummary(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response))
        }
        else
            dispatch(reportFailed(handleError(error.message)))
        openNotificationWithIcon('error', error.response?.data?.message)
    })
}

export const getRingNoAnswerAgentWiseSummaryReport = data => dispatch => {

    dispatch(loading())
    apiClient.post(AGENT_REPORT_RING_NO_ANSWER_AGENT_WISE_SUMMARY, data).then(response => {
        dispatch(agentNoAnswerSummary(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response))
        }
        else
            dispatch(reportFailed(error.message))
    })
}


export const filterReport = data => dispatch => {
    dispatch(loading())
    apiClient.post(AGENT_REPORT + "?page=" + data?.pagination?.current ?? '1' + "&pageSize=" + data?.pagination?.pageSize ?? '10', data).then(response => {
        dispatch(fileredReport(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response))
        }
        else
            dispatch(reportFailed(error.message))
    })
}

export const getPauseReasonReport = () => dispatch => {
    dispatch(loading())
    apiClient.post(AGENT_PAUSE_REASON).then(response => {
        dispatch(getPauseReason(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response))
        }
        else
            dispatch(reportFailed(error.message))
    })
}

export const getPauseReasonReportFilter = data => dispatch => {
    dispatch(loading())
    apiClient.post(AGENT_PAUSE_REASON_FILTER, data).then(response => {
        dispatch(getPauseReason(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response))
        }
        else
            dispatch(reportFailed(error.message))
    })
}

export const getCallerHangup = () => dispatch => {
    dispatch(loading())
    apiClient.get(CallerHangup).then(response => {
        dispatch(callerHangup(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response.message))
        }
        else
            dispatch(reportFailed(error.message))
    })
}

export const getCallerHangupFilter = data => dispatch => {
    dispatch(loading())
    apiClient.post(CallerHangupFilters, data).then(response => {
        dispatch(callerHangupFilter(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response.message))
        }
        else
            dispatch(reportFailed(error.message))
    })
}

export const getRingNoAnswer = (page = 1, pageSize = 10, data) => dispatch => {
    dispatch(loading())
    console.log('filter action', data);
    apiClient.post(AGENT_RING_NO_ANSWER + "?page=" + page + "&pageSize=" + pageSize, data).then(response => {
        console.log(response.data)
        dispatch(agentRingNoAnswer(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response.message))
        }
        else
            dispatch(reportFailed(error.message))
    })
}
export const getRingNoAsnwerFilter = data => dispatch => {
    dispatch(loading())
    apiClient.post(AGENT_RING_NO_ANSWER, data).then(response => {
        dispatch(agentRingNoAnswerFilter(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response.message))
        }
        else
            dispatch(reportFailed(error.message))
    })
}


export const getExitEmpty = () => dispatch => {
    dispatch(loading())
    apiClient.get(AGENT_EXIT_EMPTY).then(response => {
        dispatch(exitEmpty(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response.message))
        }
        else
            dispatch(reportFailed(error.message))
    })
}
export const getExitEmptyFilter = data => dispatch => {
    dispatch(loading())
    apiClient.post(AGENT_EXIT_EMPTY_FILTER, data).then(response => {
        dispatch(exitEmptyFilter(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response.message))
        }
        else
            dispatch(reportFailed(error.message))
    })
}

export const getExitWithTimeout = () => dispatch => {
    dispatch(loading())
    apiClient.get(AGENT_EXIT_WITH_TIMEOUT).then(response => {
        dispatch(exitTimeout(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response.message))
        }
        else
            dispatch(reportFailed(error.message))
    })
}
export const getExitWithTimeoutFilter = data => dispatch => {
    dispatch(loading())
    apiClient.post(AGENT_EXIT_WITH_TIMEOUT_FILTER, data).then(response => {
        dispatch(exitTimeoutFilter(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response.message))
        }
        else
            dispatch(reportFailed(error.message))
    })
}

export const getConfigureLoad = () => dispatch => {
    dispatch(loading())
    apiClient.get(AGENT_CONFIGURED_LOAD).then(response => {
        dispatch(configureLoad(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response.message))
        }
        else
            dispatch(reportFailed(error.message))
    })
}

export const getConfigureLoadFilter = data => dispatch => {
    dispatch(loading())
    apiClient.post(AGENT_CONFIGURED_LOAD_FILTER, data).then(response => {
        dispatch(configureLoadFilter(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response.message))
        }
        else
            dispatch(reportFailed(error.message))
    })
}

export const getCompleteCaller = () => dispatch => {
    dispatch(loading())
    apiClient.get(AGENT_COMPLETE_CALLER).then(response => {
        dispatch(completeCaller(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response.message))
        }
        else
            dispatch(reportFailed(error.message))
    })
}

export const getCompleteCallerFilter = data => dispatch => {
    dispatch(loading())
    apiClient.post(AGENT_COMPLETE_CALLER_FILTER, data).then(response => {
        dispatch(completeCallerFilter(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response.message))
        }
        else
            dispatch(reportFailed(error.message))
    })
}

export const getCompleteAgent = () => dispatch => {
    dispatch(loading())
    apiClient.get(AGENT_COMPLETE_AGENT).then(response => {
        dispatch(completeCaller(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response.message))
        }
        else
            dispatch(reportFailed(error.message))
    })
}

export const getCompleteAgentFilter = data => dispatch => {
    dispatch(loading())
    apiClient.post(AGENT_COMPLETE_AGENT_FILTER, data).then(response => {
        dispatch(completeCallerFilter(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(reportFailed(error.response.message))
        }
        else
            dispatch(reportFailed(error.message))
    })
}


export const completeAgent = data =>
({
    type: ActionsType.AGENT_COMPLETE_AGENT,
    payload: data
})

export const completeAgentFilter = data =>
({
    type: ActionsType.AGENT_COMPLETE_AGENT_FILTER,
    payload: data
})

export const completeCaller = data =>
({
    type: ActionsType.AGENT_COMPLETE_CALLER,
    payload: data
})

export const completeCallerFilter = data =>
({
    type: ActionsType.AGENT_COMPLETE_CALLER_FILTER,
    payload: data
})

export const configureLoad = data =>
({
    type: ActionsType.AGENT_CONFIGURED_LOAD,
    payload: data
})

export const configureLoadFilter = data =>
({
    type: ActionsType.AGENT_CONFIGURED_LAOD_FILTER,
    payload: data
})


export const exitTimeout = data =>
({
    type: ActionsType.AGENT_EXIT_WITH_TIMEOUT,
    payload: data
})

export const exitTimeoutFilter = data =>
({
    type: ActionsType.AGENT_EXIT_WITH_TIMEOUT_FILTER,
    payload: data
})

export const exitEmpty = data =>
({
    type: ActionsType.AGENT_EXIT_EMPTY,
    payload: data
})

export const exitEmptyFilter = data =>
({
    type: ActionsType.AGENT_EXIT_EMPTY_FILTER,
    payload: data
})

export const agentRingNoAnswer = data =>
({
    type: ActionsType.AGENT_RING_NO_ANSWER,
    payload: data
})


export const agentRingNoAnswerReset = data =>
({
    type: ActionsType.AGENT_RING_NO_ANSWER_RESET,
})

export const agentNoAnswerSummary = data =>
({
    type: ActionsType.AGENT_NO_ANSWER_SUMMARY,
    payload: data
})

export const agentRingNoAnswerFilter = data =>
({
    type: ActionsType.AGENT_RING_NO_ANSWER_FILTER,
    payload: data
})

export const callerHangup = data =>
({
    type: ActionsType.AGENT_CALLER_HANGUP,
    payload: data
})

export const callerHangupFilter = data =>
({
    type: ActionsType.AGENT_CALLER_HANGUP_FILTER,
    payload: data
})


export const loading = () =>
({
    type: ActionsType.AGENT_REPORTS_LOADING
})

export const getPauseReason = data =>
({
    type: ActionsType.AGENT_PAUSE_REASON_REPORT,
    payload: data
})

export const getPauseReasonFilter = data =>
({
    type: ActionsType.AGENT_PAUSE_REASON_REPORT_FILTER,
    payload: data
})

export const getReport = data =>
({
    type: ActionsType.AGENT_REPORTS_SUCCESS,
    payload: data
})

export const fileredReport = data =>
({
    type: ActionsType.AGENT_REPORT_FILTER,
    payload: data
})

export const reportFailed = error =>
({
    type: ActionsType.AGENT_REPORTS_FAILED,
    payload: error
})
