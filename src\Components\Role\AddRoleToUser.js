import { Checkbox, Divider, Modal, Spin } from "antd";
import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getAllUsers } from "../../Actions/UsersActions";
import { getPermissionByRole, getRole } from "../../Actions/RoleActions";

const AddRoleToUser = ({ record, isLoading, visible, setVisible, onCreate, form }) => {
    const users = useSelector(state => state.UsersReducer)
    const [checkAll, setCheckAll] = useState(false);
    const [defaultCheckedList, setDefaulCheckedList] = useState([])
    const [checkedList, setCheckedList] = useState(defaultCheckedList);
    const [plainOptions, setPlainOptions] = useState(null)
    const [indeterminate, setIndeterminate] = useState(true);
    const role = useSelector(state => state.RoleReducer)
    const dispatch = useDispatch()

    useEffect(() => {
        if (record && visible) {
            dispatch(getPermissionByRole(record.id))
            dispatch(getRole())
        }
    }, [visible])

    useEffect(() => {
        if (users.users.length <= 1) {
            // let roleName =[]
            // roleName.push(role.role.map(item => (item.name)))
            // roleName.push(users.users.map((value) => (value.name)))
            // console.log(plainOptions)
            // console.log(users.getAll)
            setPlainOptions(users.getAll)
            // setCheckedList(roleName)
        }
    }, [users])

    // useEffect(()=> {
    //
    // },[checkedList])

    const onChange = list => {
        console.log(list)
        setCheckedList(list);
        setIndeterminate(!!list.length && list.length < plainOptions.length);
        setCheckAll(list.length === plainOptions.length);
    };

    const onCheckAllChange = e => {
        console.log(e.target.checked)
        setCheckedList(e.target.checked ? plainOptions : []);
        setIndeterminate(false);
        setCheckAll(e.target.checked);
    };

    useEffect(() => {
        dispatch(getAllUsers())
    }, [])

    return (

        <Modal
            centered
            title="Assign Role To User"
            visible={visible}
            destroyOnClose={true}
            closable={true}
            okText="Assign Role"
            onOk={() => {
                onCreate(checkedList)
            }}
            onCancel={() => {
                setVisible(false)
            }}
        >
            <Spin spinning={users.isLoading}>
                <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
                    Check all
                </Checkbox>
                <Divider />
                <Checkbox.Group options={plainOptions} value={checkedList} onChange={onChange} />
            </Spin>
        </Modal>
    )
}
export default AddRoleToUser