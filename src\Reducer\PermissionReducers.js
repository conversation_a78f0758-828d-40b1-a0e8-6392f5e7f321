import * as ActionTypes from '../Constants/PermissionConstant'
const initElement =
    {
        isLoading: false,
        errMess: null,
        permissions:[],
        message: null,
        modules: []
    }

export const PermissionReducer = (state = initElement, action) =>
{
    switch (action.type)
    {
        default:
            return {...state}
        case ActionTypes.PERMISSION_FAILED:
            return {...state, isLoading: false, errMess: action.payload}
        case ActionTypes.PERMISSION_SUCCESS:
            return {...state, isLoading: false, permissions: action.payload, errMess: null}
        case ActionTypes.PERMISSION_CREATE_SUCCESS:
            return {...state, isLoading: false, message: action.payload, errMess: null}
        case ActionTypes.PERMISSION_UPDATE:
            return {...state, isLoading: false, message: action.payload, errMess: null}
        case ActionTypes.PERMISSION_DELTE:
            return {...state, isLoading: false, message: action.payload, errMess: null}
        case ActionTypes.PERMISSION_MODULE_SUSSESS:
            return {...state, isLoading: false, modules: action.payload, errMess: null}
        case ActionTypes.PERMISSION_LOADING:
            return {...state, isLoading: true, permissions: []}
    }
}