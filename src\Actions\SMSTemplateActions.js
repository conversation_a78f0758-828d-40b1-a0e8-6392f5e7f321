import apiClient from "../Shared/apiClient";
import { cookie } from "../Shared/constants";
import { handleError } from "../Shared/handleError";


export const getTemplate = () => dispatch => {
    dispatch({ type: 'LOADING_TEMPLATE' })
    apiClient('api/sMSTemplate').then(r => dispatch({ type: 'GET_TEMPLATE', payload: r.data })).catch(e => dispatch({ type: 'FAILED_TEMPLATE', payload: handleError(e) }))
}

export const saveTemplate = data => dispatch => {
    dispatch({ type: 'LOADING_TEMPLATE' })
    apiClient.post('api/sMSTemplate', data).then(r => dispatch({ type: 'MUTATE_TEMPLATE', payload: r.data })).catch(e => dispatch({ type: 'FAILED_TEMPLATE', payload: handleError(e) }))
}

export const updateTemplate = data => dispatch => {
    dispatch({ type: 'LOADING_TEMPLATE' })
    apiClient.patch(`api/sMSTemplate/${data.id}`, data).then(r => dispatch({ type: 'MUTATE_TEMPLATE', payload: r.data })).catch(e => dispatch({ type: 'FAILED_TEMPLATE', payload: handleError(e) }))
}

export const deleteTemplate = data => dispatch => {
    dispatch({ type: 'LOADING_TEMPLATE' })
    apiClient.delete(`api/sMSTemplate/${data.id}`, data).then(r => dispatch({ type: 'MUTATE_TEMPLATE', payload: r.data })).catch(e => dispatch({ type: 'FAILED_TEMPLATE', payload: handleError(e) }))
}