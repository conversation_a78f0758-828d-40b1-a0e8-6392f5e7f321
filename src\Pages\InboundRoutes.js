import { Button, Form, Space, Spin, Table } from "antd";
import { DeleteOutlined, EditTwoTone, PlusCircleOutlined } from "@ant-design/icons";
import { useState, useEffect } from 'react'
import { useDispatch, useSelector } from "react-redux";
import { createInbound, deleteInbound, getInbound, updateInbound } from "../Actions/InboundRoutesActions";
import EditInboundRoutes from "../Components/InboundRoutes/EditInboundRoutes";
import AddInboundRoutes from "../Components/InboundRoutes/AddInboundRoutes";
import { openNotificationWithIcon } from "../Shared/notification";

const { Column } = Table

const InboundRoutes = () => {

    const InboundState = useSelector(state => state.InboundRoutesReducer)
    const [selectedRowKeys, setSelectedRowKeys] = useState([])
    const [selectedRecord, setSelectedRecord] = useState(null)
    const [editVisible, setEditVisible] = useState(false)
    const [createVisible, setCreateVisible] = useState(false)

    const dispatch = useDispatch()
    const [form] = Form.useForm()

    const hasSelected = selectedRowKeys.length > 0
    const editable = selectedRowKeys.length === 1

    useEffect(() => {
        dispatch(getInbound())
    }, [])

    useEffect(() => {
        if (InboundState.errMess) {
            form.resetFields()
            setCreateVisible(false)
            openNotificationWithIcon('error', InboundState.errMess)
        }
    }, [InboundState.errMess])


    useEffect(() => {
        if (InboundState.message) {
            form.resetFields()
            setCreateVisible(false)
            openNotificationWithIcon('success', InboundState.message)
            dispatch(getInbound())
        }
    }, [InboundState.message])

    const rowSelection = {
        selectedRowKeys,
        onChange: onSelectChange,
        type: 'radio'
    }
    function onSelectChange(keys) {
        console.log('selectedRowKeys changed: ', keys)
        setSelectedRecord(InboundState.inbound.find((value, index, obj) => value.id === keys[0]))
        console.log(InboundState.inbound.find((value, index, obj) => value.id === keys[0]))
        setSelectedRowKeys(keys)
    }

    function onCreate(values) {
        dispatch(createInbound(values))
        setCreateVisible(true)
        // dispatch(getInbound())
    }

    function onUpdate(values) {
        dispatch(updateInbound(selectedRecord.id, values))
        setEditVisible(false)
    }

    function onDelete() {
        dispatch(deleteInbound(selectedRecord.id))
    }

    return (
        <Spin spinning={InboundState.isLoading}>
            <EditInboundRoutes setVisible={setEditVisible} onCancel={() => setEditVisible(false)} visible={editVisible} onCreate={onUpdate} record={selectedRecord} />
            <AddInboundRoutes form={form} isLoading={InboundState.isLoading} setVisible={setCreateVisible} onCreate={onCreate} visible={createVisible} />
            <Button onClick={() => setCreateVisible(true)} type="primary" icon={<PlusCircleOutlined />}>
                Add New
            </Button>
            <div style={{ float: 'right' }}>
                <div style={{ marginBottom: 16 }}>
                    <Space>
                        <Button onClick={() => setEditVisible(true)} icon={<EditTwoTone />} disabled={!editable}>
                            Edit
                        </Button>
                        <Button onClick={onDelete} icon={<DeleteOutlined />} type="danger" disabled={!hasSelected}>
                            Delete
                        </Button>
                    </Space>
                    <span style={{ marginLeft: 8 }}>
                        {hasSelected ? `Selected ${selectedRowKeys.length} items` : ''}
                    </span>
                </div>
            </div>
            <Table size="small" bordered={true} scroll={{ x: 1300 }} rowKey="id" dataSource={InboundState.inbound} rowSelection={rowSelection}>
                <Column title="ID" dataIndex="id" key="key" />
                <Column title="Number" dataIndex="number" key="number" />
                <Column title="Module Type" dataIndex="module_type" key="module_type" />
                <Column
                    title="Module"
                    key="action"
                    render={(text, record) => (
                        <Space size="middle">
                            {record.module.name}
                        </Space>
                    )}
                />
                {/*<Column title="Queue" dataIndex="queue" key="type" />*/}
                {/*<Column title="Queue" dataIndex="queue" key="type" />*/}
                <Column title="Created At" dataIndex="created_at" key="created_at" />
                <Column title="Updated At" dataIndex="updated_at" key="updated_at" />
            </Table>
        </Spin>
    )
}

export default InboundRoutes