import { useDispatch, useSelector } from "react-redux";
import { Button, Form, notification, Space, Spin, Table, Tooltip, Modal, Input, Popconfirm, } from "antd";
import {
    createAgent,
    deleteAgent,
    getAgent,
    updateAgent,
} from "../Actions/AgentActions";
import { assignRoleToUser } from "../Actions/RoleActions";
import EditAgent from "../Components/Agent/EditAgent";
import AddAgent from "../Components/Agent/AddAgent";
import AssignRole from "../Components/Agent/AssignRole";
import {
    DeleteOutlined,
    EditTwoTone,
    EditOutlined,
    IdcardTwoTone,
    PlusCircleOutlined,
} from "@ant-design/icons";
import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import {
    deleteSupervisor,
    getSupervisor,
    updateSupervisor,
} from "../Actions/SupervisorAction";
import { SupervisorReducer } from "../Reducer/SupervisorReducer";
import { useHistory } from "react-router";
import apiClient from "../Shared/apiClient";

const { Column } = Table;

export const Supervisor = () => {
    const supervisor = useSelector((state) => state.SupervisorReducer);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRecord, setSelectedRecord] = useState(null);
    const [editVisible, setEditVisible] = useState(false);
    const [createVisible, setCreateVisible] = useState(false);
    const [passwordModal, setPasswordModal] = useState(false);
    const [id, setId] = useState(false);
    let history = useHistory();
    const dispatch = useDispatch();
    const [form] = Form.useForm();

    const openNotificationWithIcon = (type, message) => {
        notification[type]({
            message: type === "error" ? "Error" : "Success",
            description: message,
        });
    };

    useEffect(() => {
        if (createVisible) {
            form.resetFields();
        }
    }, [createVisible]);

    useEffect(() => {
        if (supervisor.message) {
            // Display a success notification when a message is present.
            openNotificationWithIcon("success", supervisor.message);
            // Optionally, clear the message or refetch data.
            dispatch(getSupervisor());
            form.resetFields();
        }
    }, [supervisor.message]);


    // useEffect(() => {
    // if (supervisor.message) {
    //     dispatch(getSupervisor());
    //     form.resetFields();
    //     setCreateVisible(false);
    //     openNotificationWithIcon("success", supervisor.message);
    // }
    // }, [supervisor.message]);

    const hasSelected = selectedRowKeys.length > 0;
    useEffect(() => {
        dispatch(getSupervisor());
    }, [hasSelected]);

    useEffect(() => {
        if (supervisor.errMess)
            openNotificationWithIcon("error", supervisor.errMess);
    }, [supervisor.errMess]);

    const rowSelection = {
        selectedRowKeys,
        onChange: onSelectChange,
        type: "radio",
    };

    function onSelectChange(keys) {
        setSelectedRecord(
            supervisor.users.find((value, index, obj) => value.id === keys[0])
        );
        setSelectedRowKeys(keys);
    }

    useEffect(() => console.log(selectedRecord), [selectedRecord]);

    function onUpdate(values) {
        dispatch(updateSupervisor(selectedRecord.id, values));
        setSelectedRowKeys([]);
        setEditVisible(false);
    }

    // function onDelete() {
    //     dispatch(deleteSupervisor(selectedRecord.id));
    //     setSelectedRowKeys([])
    //     dispatch(getSupervisor())
    // }

    function onDelete() {
        dispatch(deleteSupervisor(selectedRecord.id))
            .then((response) => {
                console.log(`Success Supervisor Delete : ${response}`)
                setSelectedRowKeys([]);
                dispatch(getSupervisor());
            })
            .catch(error => {
                const errorMessage = error.response?.data?.message ||
                    error.response?.data?.error ||
                    error.response?.data ||
                    error.message ||
                    "An unknown error occurred";
                console.log(`Failed Supervisor Delete : ${errorMessage}`)
            });
    }

    // const onChangePassword = (values) => {
    //     apiClient
    //         .post(`/api/${id}/agent/password/reset`, values)
    //         .then((res) => {
    //             openNotificationWithIcon("success", res.data);
    //             form.resetFields();
    //             setPasswordModal(!passwordModal);
    //         })
    //         .catch((err) => {
    //             openNotificationWithIcon("error", err.response?.data?.message);
    //             form.resetFields();
    //             setPasswordModal(!passwordModal);
    //         });
    // };

    const onChangePassword = (values) => {
        apiClient
            .post(`/api/${id}/agent/password/reset`, values)
            .then((res) => {
                openNotificationWithIcon("success", res.data.message || res.data);
                form.resetFields();
                setPasswordModal(false);
                dispatch(getSupervisor()); // Optional: refresh data
            })
            .catch((err) => {
                const errorMessage = err.response?.data?.message ||
                    err.response?.data ||
                    err.message ||
                    "Failed to update password";
                openNotificationWithIcon("error", errorMessage);
                form.resetFields();
                setPasswordModal(false);
            });
    };


    const editable = selectedRowKeys.length === 1;

    return (
        <>
            <Spin spinning={supervisor.isLoading}>
                <Link to="/add-supervisor">
                    <Button type="primary" icon={<PlusCircleOutlined />}>
                        Add New
                    </Button>
                </Link>
                <div style={{ float: "right" }}>
                    <div style={{ marginBottom: 16 }}>
                        <Space>
                            <Button
                                onClick={() => {
                                    if (selectedRecord.id)
                                        history.push(`/edit-supervisor/${selectedRecord.id}`);
                                }}
                                icon={<EditTwoTone />}
                                disabled={!editable}
                            >
                                Edit
                            </Button>
                            {/* if need we will show it  */}
                            {/* <Popconfirm
  title="Are you sure want to delete this Supervisor?"
  okText="Yes"
  cancelText="No"
  onConfirm={() => onDelete()} 
>
  <Button
    onClick={onDelete} 
    icon={<DeleteOutlined />}
    type="danger"
    disabled={!hasSelected}
  >
    Delete
  </Button>
</Popconfirm> */}
                            <Button
                                onClick={onDelete}
                                icon={<DeleteOutlined />}
                                type="danger"
                                disabled={!hasSelected}
                            >
                                Delete
                            </Button>
                        </Space>
                        <span style={{ marginLeft: 8 }}>
                            {hasSelected ? `Selected ${selectedRowKeys.length} items` : ""}
                        </span>
                    </div>
                </div>
                <Table
                    size="small"
                    bordered={true}
                    scroll={{ x: 1300 }}
                    rowKey="id"
                    dataSource={supervisor.users}
                    rowSelection={rowSelection}
                >
                    <Column title="ID" dataIndex="id" key="key" />
                    <Column title="Name" dataIndex="name" key="name" />
                    <Column title="User Name" dataIndex="username" key="username" />
                    <Column title="E-Mail" dataIndex="email" key="email" />
                    <Column
                        title="Auth Username"
                        dataIndex="auth_username"
                        key="auth_username"
                    />
                    <Column
                        title="Auth Password"
                        dataIndex="auth_password"
                        key="auth_password"
                    />
                    <Column title="Created At" dataIndex="created_at" key="created_at" />
                    <Column title="Updated At" dataIndex="updated_at" key="updated_at" />
                    <Column
                        title="Action"
                        dataIndex="ation"
                        key="action"
                        render={(_, elm, index) => (
                            <div
                                key={index}
                                className="text-right d-flex justify-content-start"
                            >
                                <Tooltip title="Change Password">
                                    <Button
                                        type="primary"
                                        className="mr-2"
                                        icon={<EditOutlined />}
                                        //   disabled={!editable}
                                        size="small"
                                        onClick={() => {
                                            setId(elm?.id);
                                            setPasswordModal(true);
                                        }}
                                    />
                                </Tooltip>
                            </div>
                        )}
                    />
                </Table>
            </Spin>
            <ChangePasswordModal
                isModalOpen={passwordModal}
                handleOk={onChangePassword}
                form={form}
                onCancel={() => {
                    setPasswordModal(!passwordModal);
                    form.resetFields();
                }}
            />
        </>
    );
};

const ChangePasswordModal = ({ isModalOpen, handleOk, onCancel, form }) => {
    return (
        <Modal
            title="Change Password"
            visible={isModalOpen}
            onOk={() => {
                form.validateFields().then((values) => {
                    handleOk(values);
                });
            }}
            onCancel={onCancel}
        >
            <Form size="large" form={form}>
                <Form.Item
                    name="password"
                    rules={[
                        {
                            required: true,
                            message: "Please input your password!",
                        },
                    ]}
                    hasFeedback
                >
                    <Input.Password placeholder="Enter New Password." />
                </Form.Item>

                <Form.Item
                    name="password_confirmation"
                    dependencies={["password"]}
                    hasFeedback
                    rules={[
                        {
                            required: true,
                            message: "Please confirm your password!",
                        },
                        ({ getFieldValue }) => ({
                            validator(_, value) {
                                if (!value || getFieldValue("password") === value) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(
                                    new Error("The two passwords that you entered do not match!")
                                );
                            },
                        }),
                    ]}
                >
                    <Input.Password placeholder="Enter Confirm Password." />
                </Form.Item>
            </Form>
        </Modal>
    );
};
