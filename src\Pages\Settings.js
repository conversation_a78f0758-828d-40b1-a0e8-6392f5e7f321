import {But<PERSON>, Card, Col, Form, Input, Row, Spin, Switch} from "antd";
import {useDispatch, useSelector} from "react-redux";
import {useState, useEffect} from 'react'
import {updateSetting, getSetting} from "../Actions/SettingActions";
import {openNotificationWithIcon} from "../Shared/notification";

const layout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
};

const tailLayout = {
    wrapperCol: { offset: 2, span: 16 },
};

const inputType = data => {
    console.log(data.type == "switch")
    switch (data.type){
        case "password":
            return <Input.Password />
        case "switch":
            return <Switch defaultChecked={data.value == "1" ? true: false} />
        default:
            return <Input />
    }
}

const Settings = () => {

    const systemSetting = useSelector(state => state.SettingReducer)
    const [record, setRecord] = useState(null)
    const [id, setId]= useState()
    const dispatch = useDispatch()
    const [form] = Form.useForm()

    useEffect(() => {
        if(systemSetting.errMess){
            openNotificationWithIcon('error', systemSetting.errMess)
        }
    },[systemSetting.errMess])

    useEffect(() => {
        if(systemSetting.message){
            openNotificationWithIcon('success', systemSetting.message)
            dispatch(getSetting())
        }
    },[systemSetting.message])

    useEffect(()=> {
        dispatch(getSetting())
    },[])

    const handleSubmit = values => {
        // setShowEdit(false)
        console.log(values)
        dispatch(updateSetting(id, values))
    }

    useEffect(()=>{
        let objects = new Object();
        for (let b in systemSetting?.setting) {
            objects[systemSetting?.setting[b].key] = systemSetting?.setting[b].value
            setId(systemSetting?.setting[b].id)
        }
        setRecord(objects)
    },[systemSetting.setting])

    useEffect(()=> {
        form.setFieldsValue(record)
    },[record])

    return(
        <Spin spinning={systemSetting.isLoading}>
            <Card>
                <Form
                    form={form}
                    initialValues={record}
                    onFinish={handleSubmit}
                    {...layout}
                >
                    <Row gutter={[5, 5]}>
                        {systemSetting.setting && systemSetting.setting.map((value, index) =>
                            <Col key={index} span={10}>
                                <Form.Item
                                    label={value.key}
                                    name={value.key}
                                    key={value.key}
                                    // valuePropName={value.type == "switch" ? "checked" : null}
                                    // rules={[
                                    //     {
                                    //         // required: true,
                                    //         message: () => value.type == "switch" ? null : 'Please input your '+value.key,
                                    //     },
                                    // ]}
                                >
                                    {inputType(value)}
                                </Form.Item>
                            </Col>
                        )}
                    </Row>
                    <Form.Item>
                        <Button type="primary" htmlType="submit">
                            Update
                        </Button>
                    </Form.Item>
                </Form>
            </Card>
        </Spin>
    )
}
export default Settings