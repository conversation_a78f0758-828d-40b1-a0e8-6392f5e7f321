import * as ActionTypes from "../Constants/OutboundDispositionSummaryConstants"
import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";

export const getOutboundDispositionSummary = () => dispatch => {
    dispatch(outboundDispositionSummaryLoading())
    apiClient.post(`/api/report/outbound-disposition-summary`).then(r => dispatch(outboundDispositionSummarySuccess(r.data))).catch(e => dispatch(outboundDispositionSummaryFailed(handleError(e))))
}

export const getOutboundDispositionSummaryFilter = data => dispatch => {
    dispatch(outboundDispositionSummaryLoading())
    apiClient.post(`/api/report/outbound-disposition-summary`, data).then(r => dispatch(outboundDispositionSummarySuccess(r.data))).catch(e => dispatch(outboundDispositionSummaryFailed(handleError(e))))
}
export const OutboundDispositionSummaryReset = () => dispatch => {
    dispatch(outboundDispositionReset())

}


const outboundDispositionSummaryLoading = () => ({
    type: ActionTypes.OUTBOUND_SUMMARY_LOADING
})

const outboundDispositionSummarySuccess = data => ({
    type: ActionTypes.OUTBOUND_SUMMARY_SUCCESS,
    payload: data
})

const outboundDispositionSummaryFailed = err => ({
    type: ActionTypes.OUTBOUND_SUMMARY_FAILED,
    payload: err
})
const outboundDispositionReset = () => ({
    type: ActionTypes.OUTBOUND_SUMMARY_RESET,

})