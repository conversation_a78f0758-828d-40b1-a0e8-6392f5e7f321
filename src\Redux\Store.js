import { applyMiddleware, createStore, combineReducers, compose } from "redux"
import { logger } from 'redux-logger'
import thunk from "redux-thunk"
import { User } from "../Reducer/User"
import { UsersReducer } from "../Reducer/UsersReducer"
import { QueueReducer } from "../Reducer/QueueReducer"
import { WorkcodeReducer } from "../Reducer/WorkcodeReducer";
import { PauseResonReducer } from "../Reducer/PauseReasonReducer";
import { MediaFileReducer } from "../Reducer/MediaFilesReducer";
import { InboundRoutesReducer } from '../Reducer/InboundRoutesReducer'
import { Stats } from '../Reducer/Stats'
import { AgentReducer } from "../Reducer/AgentReducer";
import { SettingReducer } from '../Reducer/SettingReducer'
import { EmailSettingReducer } from '../Reducer/EmailSettingReducer'
import { RoleReducer } from '../Reducer/RoleReducer'
import { PermissionReducer } from '../Reducer/PermissionReducers'
import { CdrReducer } from "../Reducer/CallDetailReports";
import { AgentReportReducer } from "../Reducer/AgentReportReducer"
import { CampaignReducer } from "../Reducer/CampaignReducer";
import { CampaignNumberReducer } from "../Reducer/CampaignNumberReducer";
import { CampaignUserReducer } from "../Reducer/CampaignUserReducer"
import { FormReducer } from "../Reducer/FormReducer"
import { FormFieldTypeReducer } from "../Reducer/FormFieldTypeReducer"
import { FormFieldReducer } from "../Reducer/FormFieldReducer"
import { ScriptReducer } from "../Reducer/ScriptReducer"
import { AgentCallReportReducer } from "../Reducer/AgentCallReportReducer"
import { CallStatusReducer } from "../Reducer/CallStatusReducer"
import { OutboundDispositionSummaryReducer } from "../Reducer/OutboundDispositionSummaryReducer"
import { OutboundDispositionReducer } from "../Reducer/OutboundDispositionReducer"
import { InboundDispositionSummaryReducer } from "../Reducer/InboundDispositionSummaryReducer"
import { InboundDispositionReducer } from "../Reducer/InboundDispositionReducer"
import { OutboundAgentSummaryReducer } from "../Reducer/OutboundAgentSummaryReducer"
import { OutboundActivityReducer } from "../Reducer/OutboundActivityReducer"
import { CallDetailRecordReportReducer } from "../Reducer/CallDetailRecordReportReducer"
import { CustomNumberReducer } from "../Reducer/CustomNumberReducer";
import { InboundAgentSummaryReducer } from "../Reducer/InboundAgentSummaryReducer"
import { FormDataReportReducer } from "../Reducer/FormDataReportReducer"
import { HoldTimeReportReducer } from "../Reducer/HoldTimeReportReducer";
import { BreakReportReducer } from "../Reducer/BreakReportReducer";
import { CallPerAgentReportReducer } from "../Reducer/CallPerAgentReportReducer";
import { TrunkPerHourReportReducer } from "../Reducer/TrunkPerHourReducer";
import { RecordingReportReducer } from "../Reducer/RecordingReportReducer";
import { AbandonedReducer } from "../Reducer/AbandonedReducer";
import { CIDLookUpReducer } from "../Reducer/CIDLookUpReducer";
import { SupervisorReducer } from "../Reducer/SupervisorReducer";
import { AbandonCallReportDifferenceReducer } from "../Reducer/AbandonCallReportDifferenceReducer"
import { AspireCustomFormDataReportReducer } from "../Reducer/AspireCustomFormDataReportReducer"
import { IvrFlowReducer } from "../Reducer/IvrFlowReducer"
import { SMSTemplateReducer } from "../Reducer/SMSTemplateReducer"
import { SMSCategoryReducer } from "../Reducer/SMSCategoryReducer"
import { CallBackRequestReducer } from "../Reducer/CallBackRequestReducer";



export const ConfigureStore = () => {

    const composeEnhancers = window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ || compose

    return createStore(combineReducers({
        UsersReducer,
        User,
        QueueReducer,
        WorkcodeReducer,
        PauseResonReducer,
        MediaFileReducer,
        InboundRoutesReducer,
        Stats,
        AgentReducer,
        SettingReducer,
        EmailSettingReducer,
        RoleReducer,
        PermissionReducer,
        CdrReducer,
        AgentReportReducer,
        CampaignReducer,
        CampaignNumberReducer,
        CampaignUserReducer,
        FormReducer,
        FormFieldTypeReducer,
        FormFieldReducer,
        ScriptReducer,
        AgentCallReportReducer,
        CallStatusReducer,
        OutboundDispositionSummaryReducer,
        OutboundDispositionReducer,
        InboundDispositionSummaryReducer,
        InboundDispositionReducer,
        IvrFlowReducer,
        OutboundAgentSummaryReducer,
        InboundAgentSummaryReducer,
        OutboundActivityReducer,
        CallDetailRecordReportReducer,
        CustomNumberReducer,
        FormDataReportReducer,
        HoldTimeReportReducer,
        BreakReportReducer,
        CallPerAgentReportReducer,
        TrunkPerHourReportReducer,
        RecordingReportReducer,
        AbandonedReducer,
        CIDLookUpReducer,
        SupervisorReducer,
        AbandonCallReportDifferenceReducer,
        AspireCustomFormDataReportReducer,
        SMSCategoryReducer,
        SMSTemplateReducer,
        CallBackRequestReducer,
    }), composeEnhancers(applyMiddleware(thunk, logger)))
}
