import { useDispatch, useSelector } from "react-redux";
import {
    getRingNoAnswer, getRingNoAnswerAgentWiseSummaryReport,
    getRingNoAnswerQueueSummaryReport,
    getRingNoAsnwer, getRingNoAsnwerFilter
} from "../../Actions/AgentReportActions";
import { Button, Descriptions, Modal, Space, Input, Spin, Table } from "antd";
import { CSVDownload, CSVLink } from "react-csv";
import ReactExport from "react-export-excel";
import { DesktopOutlined, DownloadOutlined, FilterOutlined, SearchOutlined, PlusCircleOutlined, SyncOutlined } from "@ant-design/icons";
import { useState, useEffect, useRef } from 'react'
import CallPerAgentFilter from "../../Components/Reports/CallPerAgentFilter";
import RingNoAnswerQueueSummaryReportFilter from "../../Components/Reports/RingNoAnswerQueueSummaryReportFilter";
import Highlighter from "react-highlight-words";

export const RingNoAnswerQueueSummaryReport = () => {
    const report = useSelector(state => state.AgentReportReducer)
    const [record, setRecord] = useState(null)
    const [showDetails, setShowDetails] = useState(false)
    const [filter, setFilter] = useState(false)

    const dispatch = useDispatch()

    // useEffect(() => {
    //     dispatch(getRingNoAnswerQueueSummaryReport())
    // }, [])

    function onFilter(values) {
        dispatch(getRingNoAnswerQueueSummaryReport(values))
        setFilter(false)
    }

    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')

    const searchInput = useRef()

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }

    return (
        // <Spin spinning={report.isLoading}>
        <>
            <RingNoAnswerQueueSummaryReportFilter setVisible={setFilter} onCancel={() => setFilter(false)} visible={filter} onCreate={onFilter} record={"ok"} />
            <Modal
                width="100%"
                visible={showDetails}
                closable={true}
                onCancel={() => setShowDetails(false)}
                onOk={() => setShowDetails(false)}
                centered
            >
                <Descriptions style={{ padding: 20 }} title="Pause Reason Info" bordered size="small">
                    {record && Object.keys(record).map((key, index) => (
                        <Descriptions.Item label={key}>
                            {(record[key]) ? (record[key]) : ""}
                        </Descriptions.Item>))}
                </Descriptions>

            </Modal>

            <Table scroll={{ x: 800 }}
                title={d => <>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        Ring No Answer Queue Summary
                        <Space>
                            <Button onClick={() => dispatch(getRingNoAnswerQueueSummaryReport())} type="danger" icon={<PlusCircleOutlined />}>
                                Reset Filter
                            </Button>
                            <Button onClick={() => setFilter(true)} icon={<FilterOutlined />}>
                                Filter
                            </Button>
                            <CSVLink style={{ textDecoration: 'none', color: '#fff' }} data={report.agentReport} filename="RingNoAnswerQueueSummary.csv">
                                <Button type={"primary"} icon={<DownloadOutlined />} disabled={report.agentReport.length == 0} >

                                    Download
                                </Button>
                            </CSVLink>
                        </Space>

                    </div>
                </>}
                // size="small"
                bordered
                loading={{ spinning: report.isLoading, indicator: <SyncOutlined spin /> }}
                dataSource={report.agentReport}>
                <Table.Column dataIndex="Sn" key="Sn" title="S#" render={(text, record, index) => (++index)} />
                <Table.Column dataIndex="queue" key="queue" title="Queue" {...getColumnSearchProps('queue')} />
                <Table.Column dataIndex="ringtime" key="ringtime" title="Total Ring Time" sorter={(a, b) => a.ringtime - b.ringtime} {...getColumnSearchProps('ringtime')} />
                <Table.Column dataIndex="number_of_calls" key="number_of_calls" title="Number of calls" {...getColumnSearchProps('number_of_calls')} />
            </Table>
            {/* </Spin> */}
        </>
    )
}
