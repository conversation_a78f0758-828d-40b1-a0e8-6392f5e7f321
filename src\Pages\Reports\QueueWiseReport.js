import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, Card, DatePicker, Form, Table, Input, Space, Modal, Select } from "antd";
import { FilterOutlined, ReloadOutlined, SearchOutlined } from "@ant-design/icons";
import apiClient from "../../Shared/apiClient";
import moment from "moment";
import { openNotificationWithIcon } from "../../Shared/notification";
import { CSVLink } from "react-csv";
import Highlighter from "react-highlight-words";

const { RangePicker } = DatePicker;
const dateFormat = "YYYY-MM-DD";


const exportHeaders = [
    { label: 'Calls Bifurcation', key: 'queuename' },
    { label: 'Total Incomming Calls', key: 'totalInbounCalls' },
    { label: 'Total Answered Calls', key: 'totalAnswerCalls' },
    { label: 'Total Abandoned/Lost Calls', key: 'totalAbandonCalls' },
    { label: 'Customer Service Factor (%)', key: 'CustomerServiceFactor' },
    { label: 'Answered Calls %', key: 'percentageOfAnsweredCalls' },
]

const QueueWiseReport = () => {
    const [form] = Form.useForm();
    const [data, setData] = useState([]);
    const [to, setTo] = useState();
    const [from, setFrom] = useState();
    const [loading, setLoading] = useState(false);
    const [fetchReportCheck, setFetchReportCheck] = useState(true);
    const [exportReportCheck, setExportReportCheck] = useState(true);
    const [queues, setQueues] = useState([]);

    useEffect(() => {
        apiClient.get('api/queue')
            .then((res) => setQueues(res.data))
            .catch(() => openNotificationWithIcon("error", "Failed to load queues"));
    }, []);

    const onDateChange = (date, dateString) => {
        setFrom(moment(dateString[0]));
        setTo(moment(dateString[1]));
        setFetchReportCheck(false);
    };


    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const searchInput = useRef();


    const [showFilter, setShowFilter] = useState(false)
    const [resetFilter, setResetFilter] = useState(false)

    const resetFormFilter = () => {
        // dispatch(getTrunkPerHourReport())
        // dispatch(trunkPerHourReset())
        // console.log(new Date().toLocaleString() + '')
        setData([])
        form.resetFields()
        setResetFilter(true)
    }



    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }

    const columns = [
        {
            title: "Calls Bifurcation",
            dataIndex: "queuename",
            key: "queuename",
            ...getColumnSearchProps('queuename')
        },
        {
            title: "Total Incoming Calls",
            dataIndex: "totalInbounCalls",
            key: "totalInbounCalls",
            ...getColumnSearchProps('totalInbounCalls')
        },
        {
            title: "Total Answered Calls",
            dataIndex: "totalAnswerCalls",
            key: "totalAnswerCalls",
            ...getColumnSearchProps('totalAnswerCalls')
        },
        {
            title: "Total Abandoned/Lost Calls",
            dataIndex: "totalAbandonCalls",
            key: "totalAbandonCalls",
            ...getColumnSearchProps('totalAbandonCalls')
        },
        {
            title: "Customer Service Factor (%)",
            dataIndex: "CustomerServiceFactor",
            key: "CustomerServiceFactor",
            ...getColumnSearchProps('CustomerServiceFactor'),
            render: (v) => `${v}%`
        },
        {
            title: "Answered Calls %",
            dataIndex: "percentageOfAnsweredCalls",
            key: "percentageOfAnsweredCalls",
            render: (v) => `${v}%`
        },
    ];

    //   const exportReportFunc = () => {
    //     setFetchReportCheck(true);
    //     setExportReportCheck(true);
    //   };

    const fetchReport = (queue) => {
        setLoading(true);
        apiClient
            .post(`/api/report/getQueueWiseReport`, {
                from: from?._i,
                to: to?._i,
                queue
            })
            .then((resp) => {
                setData(resp.data);
                setLoading(false);
                setExportReportCheck(false);
                form.resetFields();
            })
            .catch((err) => {
                setLoading(false);
                openNotificationWithIcon('error', err.message)
                console.log(err.message);
            });
    };

    return (
        <>
            {/* <Card
                title="Queue Wise Report"
                style={{ background: "#fff", borderRadius: "2px", padding: 10 }}
            // extra={
            //     <Form
            //         form={form}
            //         layout="inline"
            //         size="large"
            //         style={{ marginTop: "3px" }}
            //     >
            //         <Form.Item name={"picker"}>
            //             <RangePicker
            //                 format={dateFormat}
            //                 onChange={onDateChange}
            //             />
            //         </Form.Item>
            //         <Form.Item>
            //             <Button
            //                 size="large"
            //                 onClick={fetchReport}
            //                 disabled={fetchReportCheck}
            //             >
            //                 Fetch Report
            //             </Button>
            //         </Form.Item>
            //         <Form.Item>
            //             <CSVLink headers={exportHeaders} data={data} filename={"Queue Wise Report.csv"}>
            //                 <Button disabled={data.length === 0}>Export Report</Button>
            //             </CSVLink>
            //         </Form.Item>
            //     </Form>
            // }
            > */}
            <Table
                title={data => <>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        {`Queue Wise Report`}
                        <Space>
                            <Button onClick={() => resetFormFilter()} type="danger" icon={<ReloadOutlined />}>Reset Filter</Button>
                            <Button onClick={() => setShowFilter(true)} icon={<FilterOutlined />}>Filter</Button>
                            <CSVLink headers={exportHeaders} data={data} filename={"Queue Wise Report.csv"}>
                                <Button disabled={data.length === 0}>Download</Button>
                            </CSVLink>
                        </Space>

                    </div>
                </>}
                columns={columns}
                dataSource={data}
                pagination={false}
                loading={loading}
                size="default"
                bordered
            //   scroll={{
            //     x: "calc(700px + 50%)",
            //     y: 240,
            //   }}
            />
            {/* </Card > */}
            <QueueWiseReportFilter
                form={form}
                resetFilter={resetFilter}
                visible={showFilter}
                fetchReport={fetchReport}
                queues={queues}
                // onChange={onChange}
                // onDateChange={onDateChange}
                // selectedQueue={selectedQueue}
                // selectedType={selectedType}
                // onSearch={onSearch}
                // month={month}
                // queues={queues}
                onDateChange={onDateChange}
                dateFormat={dateFormat}
                // setselectedType={setselectedType}
                setVisible={setShowFilter}
            />
        </>
    );
};

export const QueueWiseReportFilter = ({ form, fetchReport, queues = [], dateFormat, onDateChange, visible, setVisible }) => {

    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            okText="Submit"
            onOk={() => form.validateFields()
                .then(value => {
                    // dispatch(getTrunkPerHourReport({ date: filterDate, queue: queue }))
                    fetchReport(value.queue)
                    setVisible(false)
                })
            }
            // okButtonProps={{
            //     loading: btnLoading,
            //     icon: <SaveOutlined />
            // }}
            title="Queue Wise Report Filter"
        >
            {/* <Spin spinning={btnLoading} indicator={<SyncOutlined spin />}> */}
            <Form
                form={form}
                layout="vertical"
            >
                <Form.Item name={"picker"}>
                    <RangePicker
                        format={dateFormat}
                        onChange={onDateChange}
                        style={{ width: "100%" }}
                    />
                </Form.Item>
                <Form.Item name="queue" rules={[{ required: true }]}>
                    <Select
                        placeholder="Select Queue"
                    // onChange={value => setSelectedQueue(value)}
                    >
                        {queues.map((queue, index) => (
                            <Select.Option key={index} value={queue.name}>
                                {queue.name}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
            </Form>
            {/* </Spin> */}
        </Modal>
    )
}

export default QueueWiseReport;