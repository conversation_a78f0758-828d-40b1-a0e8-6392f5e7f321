import {
    <PERSON><PERSON>,
    Card,
    DatePicker,
    Form,
    Input,
    Modal,
    Select,
    Space,
    Spin,
    Table,
} from "antd";
import {
    CloseCircleOutlined,
    DownloadOutlined,
    FilterOutlined,
} from "@ant-design/icons";
import React, { useEffect, useRef, useState } from "react";
import {
    agentCallSummaryReset,
    getAgentCallFilteredReport,
    getAgentCallReport,
} from "../../Actions/AgentCallReportActions";
import { useDispatch, useSelector } from "react-redux";
import { CSVDownload, CSVLink } from "react-csv";
import { getCallStatuses } from "../../Actions/CallStatusActions";
import { getAgent } from "../../Actions/AgentActions";
import { openNotificationWithIcon } from "../../Shared/notification";
import Highlighter from "react-highlight-words";
import { SearchOutlined } from "@ant-design/icons";

export const AgentCallReport = () => {
    const callReportState = useSelector((state) => state.AgentCallReportReducer);

    const [filterVisible, setFilterVisible] = useState(false);
    const [csvData, setCsvData] = useState(callReportState?.data);
    const [resetFilter, setResetFilter] = useState(false);
    const [searchText, setSearchText] = useState("");
    const [searchedColumn, setSearchedColumn] = useState("");
    const dispatch = useDispatch();
    const agentState = useSelector((state) => state.AgentReducer);

    useEffect(() => {
        if (callReportState.errMess !== "")
            openNotificationWithIcon("error", callReportState.errMess);
    }, [callReportState.errMess]);

    // useEffect(() => {
    //     dispatch(getAgent());
    //     dispatch(getAgentCallReport());
    // }, []);
    useEffect(() => setResetFilter(false), [filterVisible]);

    const resetFormFilter = () => {
        dispatch(agentCallSummaryReset());
        setResetFilter(true);
    };

    const searchInput = useRef();

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({
            setSelectedKeys,
            selectedKeys,
            confirm,
            clearFilters,
        }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={(e) =>
                        setSelectedKeys(e.target.value ? [e.target.value] : [])
                    }
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: "block" }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button
                        onClick={() => handleReset(clearFilters)}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0]);
                            setSearchedColumn(dataIndex);
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined style={{ color: filtered ? "#1890ff" : undefined }} />
        ),
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex]
                    .toString()
                    .toLowerCase()
                    .includes(value.toLowerCase())
                : "",
        onFilterDropdownVisibleChange: (visible) => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: (text) =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: "#ffc069", padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ""}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0]);
        setSearchedColumn(dataIndex);
    };

    const handleReset = (clearFilters) => {
        clearFilters({ confirm: true });
        setSearchText("");
    };

    const columns = [
        {
            title: "ID",
            dataIndex: "uniqueid",
            key: "id",
        },
        {
            title: "Date",
            dataIndex: "start",
            key: "date",
        },
        {
            title: "Customer Phone",
            dataIndex: "dst",
            key: "customer_number",
            ...getColumnSearchProps("dst"),
        },
        {
            title: "Agent ID",
            dataIndex: "channel",
            key: "agent",
            ...getColumnSearchProps("channel"),
        },
        {
            title: "Agent Name",
            render: (text, record) => {
                if (agentState.users) {
                    let { name } =
                        agentState.users.find((d) => d.auth_username === record.channel) ??
                        "N/A";
                    return <>{name}</>;
                }
                return <></>;
            },
        },
        {
            title: "Call Status",
            dataIndex: "disposition",
            key: "status",
            ...getColumnSearchProps("disposition"),
        },
        {
            title: "Duration",
            dataIndex: "duration",
            key: "duration",
            ...getColumnSearchProps("duration"),
        },
    ];

    const headers = [
        { label: "ID", key: "uniqueid" },
        { label: "Date", key: "start" },
        { label: "Customer Phone", key: "dst" },
        { label: "Agent ID", key: "channel" },
        { label: "Agent Name", key: "agentName" },
        { label: "Call Status", key: "disposition" },
        { label: "Duration", key: "duration" },
    ];



    useEffect(() => {
        setCsvData(
            callReportState.data.map((v, i) => {
                if (agentState.users) {
                    let name =
                        agentState.users.find((d) => d.auth_username === v.channel) ??
                        "N/A";
                    return { ...v, agentName: name?.name };
                }
            })
        );
    }, [callReportState]);

    return (
        <>
            <div style={{ textAlign: "right", marginBottom: "10px" }}>
                {/* <Space>
                    <Button
                        onClick={() => resetFormFilter()}
                        type="primary"
                        danger
                        icon={<CloseCircleOutlined />}
                    >
                        Reset Filter
                    </Button>
                    <Button
                        onClick={() => setFilterVisible(true)}
                        icon={<FilterOutlined />}
                    >
                        Filter
                    </Button>
                    {callReportState.data && (
                        <Button
                            onClick={<CSVDownload data={callReportState.data} />}
                            target="_blank"
                            icon={<DownloadOutlined />}
                        >
                            <CSVLink filename="AgentCallReport.csv" data={csvData}>
                                {" "}
                                Download CSV
                            </CSVLink>
                        </Button>
                    )}
                </Space> */}

            </div>
            <Table
                loading={callReportState.isLoading}
                dataSource={callReportState.data}
                columns={columns}
                bordered
                title={(v) =>
                    <>
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            Agent Call Report
                            <Space>
                                <Button
                                    onClick={() => resetFormFilter()}
                                    type="primary"
                                    danger
                                    icon={<CloseCircleOutlined />}
                                >
                                    Reset Filter
                                </Button>
                                <Button
                                    onClick={() => setFilterVisible(true)}
                                    icon={<FilterOutlined />}
                                >
                                    Filter
                                </Button>
                                {callReportState.data && (
                                    <CSVLink filename="AgentCallReport.csv" data={csvData}>
                                        {" "}
                                        <Button
                                            onClick={<CSVDownload data={callReportState.data} />}
                                            disabled={callReportState.data.length == 0}
                                            type={"primary"}
                                            target="_blank"
                                            icon={<DownloadOutlined />}
                                        >

                                            Download CSV
                                        </Button>


                                    </CSVLink>
                                )}
                            </Space>
                        </div>
                    </>
                }
                scroll={{ x: 1100 }}
            />
            <AgentCallReportFilter
                resetField={resetFilter}
                visible={filterVisible}
                setVisible={setFilterVisible}
            />
        </>
    );
};

const AgentCallReportFilter = ({ visible, setVisible, resetField }) => {

    const [form] = Form.useForm();
    const agentState = useSelector((state) => state.AgentReducer);
    const callState = useSelector((state) => state.CallStatusReducer);
    const dispatch = useDispatch();

    useEffect(() => {
        if (visible == true) {


            dispatch(getCallStatuses());
            dispatch(getAgent());
        }
    }, [visible]);

    useEffect(() => form.resetFields(), [resetField]);

    return (
        <Modal
            title="Filter"
            onCancel={() => {
                setVisible(false);
            }}
            visible={visible}
            onOk={() => {
                form.validateFields()
                    .then((values) => dispatch(getAgentCallFilteredReport(values)))
                    .then(() => setVisible(false))
                    .catch((e) => console.log(e));
            }}
        >
            <Spin spinning={callState.isLoading || agentState.isLoading}>
                <Form form={form} layout="vertical">
                    <Form.Item name="range" label="Date Time">
                        <DatePicker.RangePicker showTime />
                    </Form.Item>
                    <Form.Item name="agent"
                        label="Agent">
                        <Select mode="multiple">
                            {agentState.users &&
                                agentState.users.map((value, index) => (
                                    <Select.Option value={value.auth_username} key={value.id}>
                                        {value.name}
                                    </Select.Option>
                                ))}
                        </Select>
                    </Form.Item>
                    <Form.Item name="destination" label="Destination">
                        <Input />
                    </Form.Item>
                    <Form.Item name="call_status" label="Call Status">
                        <Select>
                            {callState.statuses &&
                                callState.statuses.map((value, index) => (
                                    <Select.Option key={index} value={value.disposition}>
                                        {value.disposition}
                                    </Select.Option>
                                ))}
                        </Select>
                    </Form.Item>
                </Form>
            </Spin>
        </Modal>
    );
};
