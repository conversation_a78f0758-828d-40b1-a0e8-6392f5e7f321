import React from 'react'
import './style.css'
import { Card } from 'antd'
import { Button, Checkbox, Form, Divider, Input } from 'antd';
// import { Divider } from 'rc-menu';
const onFinish = (values) => {
    console.log('Success:', values);
};
const onFinishFailed = (errorInfo) => {
    console.log('Failed:', errorInfo);
};
function Login3() {
    return (
        <div className='login3-container' style={{ backgroundColor: '#dcdde2' }}>
            <Card style={{ width: '500px', backgroundColor: 'rgba(0, 0, 0, 0.3)', border: 'none' }}>
                <p style={{ textAlign: 'center', color: '#fff', fontWeight: 'bolder', fontSize: '2rem' }}>Sign-In</p>
                <Form
                    size='middle'
                    initialValues={{
                        remember: true,
                    }}
                    onFinish={onFinish}
                    onFinishFailed={onFinishFailed}
                    autoComplete="off"
                >
                    <Form.Item
                        name="username"
                        rules={[
                            {
                                required: true,
                                message: 'Please input your username!',
                            },
                        ]}
                    >
                        <Input placeholder='Enter username!' style={{ height: '50px' }} />
                    </Form.Item>

                    <Form.Item
                        name="password"

                        rules={[
                            {
                                required: true,
                                message: 'Please input your password!',
                            },
                        ]}
                    >
                        <Input.Password placeholder='Enter password!' style={{ height: '50px' }} />
                    </Form.Item>

                    <Form.Item
                        wrapperCol={{
                            offset: 0,
                            span: 24,
                        }}
                    >
                        <Button style={{ background: '#fbbb14', border: 'none', fontWeight: 'bold', fontSize: '1.5rem', color: '#fff', height: '50px' }} htmlType="submit" block>
                            Login
                        </Button>
                    </Form.Item>
                </Form>
            </Card>
        </div>

    )
}

export default Login3