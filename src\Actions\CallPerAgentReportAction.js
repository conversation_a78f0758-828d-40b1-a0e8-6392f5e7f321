
import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";

export const getCallPerAgentReport = data => dispatch => {
    dispatch(callPerAgentReportLoading())
    apiClient.post(`/api/report/call-per-agent`, data).then(r => dispatch(callPerAgentReportSuccess(r.data))).catch(e => dispatch(callPerAgentReportFailed(handleError(e)))).catch(e => dispatch(callPerAgentReportFailed(handleError(e))))
}

export const getCallPerAgentReportFiltered = data => dispatch => {
    dispatch(callPerAgentReportLoading())
    apiClient.post(`/api/report/hold-time-report-filtered`, data).then(r => dispatch(callPerAgentReportSuccess(r.data))).catch(e => dispatch(callPerAgentReportFailed(handleError(e)))).catch(e => dispatch(callPerAgentReportFailed(handleError(e))))
}

export const resetPerAgent = () => dispatch => {
    dispatch(callResetPerAgent());
}

const callPerAgentReportSuccess = data => ({
    type: "CALL_AGENT_REPORT_SUCCESS",
    payload: data
})

const callPerAgentReportFailed = err => ({
    type: "CALL_AGENT_REPORT_FAILED",
    payload: err
})

const callPerAgentReportLoading = () => ({
    type: "CALL_AGENT_REPORT_LOADING"
})
const callResetPerAgent = () => ({
    type: "CALL_AGENT_REPORT_RESET"
})