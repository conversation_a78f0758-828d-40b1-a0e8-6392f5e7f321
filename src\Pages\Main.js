import { Layout } from "antd"
import { useSelector } from "react-redux"
import { useState, useEffect } from "react"
import MenuComponent from "../Components/MenuComponent"
import ContentComponent from "../Components/ContentComponent"
import HeaderComponent from "../Components/HeaderComponent";
// import background from "../Assets/bg-01.jpg";
import { useHistory } from "react-router"
// import { useParams } from "react-router";
const { Header, Footer } = Layout

const Main = () => {

    const layoutStyle = {
        minHeight: '100vh',
        // backgroundImage: `url(${background})`,
        backgroundSize: 'cover',
    }

    const date = new Date();
    const year = date.getFullYear()
    // sider and drawer toggle
    const [isToggled, setToggled] = useState(false);

    const toggleTrueFalse = () => setToggled(!isToggled);

    const onClose = () => {
        setToggled(false);
    }

    // const his = useHistory()
    // console.log("s", his)


    const User = useSelector(state => state.User)

    return (
        <Layout>
            {User.loggedIn && <MenuComponent onClose={onClose} isToggled={isToggled} />}
            <Layout style={layoutStyle} >
                {User.loggedIn && <HeaderComponent User={User} setToggled={setToggled} isToggled={isToggled} />}
                <ContentComponent />
                {User.loggedIn && <Footer style={{ textAlign: 'center', fontWeight: 'bold', background: 'transparent', color: '#15347c' }}>ContactPlus ©{year} | Powered by Telecard Ltd.</Footer>}
            </Layout>
        </Layout>
    )
}

export default Main
