import * as ActionTypes from "../Constants/CampaignConstants"
import apiClient from "../Shared/apiClient";
import { CAMPAIGN } from "../Endpoints/CampaignRoutes"
import { CAMPAIGN_MONITOR_SUCCESS, CAMPAIGN_MONITORING_SUCCESS } from "../Constants/CampaignConstants";

const handleError = error => {
    if (error.response) {
        return error.response.data
    } else {
        return error.message
    }
}

export const postCampaign = data => dispatch => {
    dispatch(campaignLoading())
    apiClient.post(CAMPAIGN, data).then(m => dispatch(campaignSuccess(m.data))).then(() => dispatch(getCampaigns())).catch(e => {
        if (e.response && e.response.data === "No amount left.") {
            dispatch(campaignFailed({ 
                message: e?.response?.data ?? "Please extend your package. No amount left",
                specificError: "NO_AMOUNT_LEFT"
            }));
        } else {
            dispatch(campaignFailed(handleError(e)));
        }
    })
    // catch(e => dispatch(campaignFailed(handleError(e))))this is old as bk
}

export const patchCampaign = data => dispatch => {
    dispatch(campaignLoading())
    apiClient.patch(`${CAMPAIGN}/${data.id}`, data).then(r => dispatch(campaignSuccess(r.data))).then(() => dispatch(getCampaigns())).catch(e => dispatch(campaignFailed(handleError(e))))
}

export const getCampaignMonitor = (URL, data) => dispatch => {
    dispatch(campaignLoading())
    apiClient.post(`${URL}`, data).then(r => dispatch(campaignMonitor(r.data))).catch(e => dispatch(campaignFailed(handleError(e))))
}

export const getCampaignMonitoring = (URL, data) => dispatch => {
    dispatch(campaignLoading())
    apiClient.post(`${URL}`, data).then(r => dispatch(campaignMonitoring(r.data))).catch(e => dispatch(campaignFailed(handleError(e))))
}

export const deleteCampaign = data => dispatch => {
    dispatch(campaignLoading())
    apiClient.delete(`${CAMPAIGN}/${data.id}`).then(r => dispatch(campaignSuccess(r.data))).then(() => dispatch(getCampaigns())).catch(e => dispatch(campaignFailed(handleError(e)))).catch(e => dispatch(campaignFailed(handleError(e))))
}

export const getCampaigns = () => dispatch => {
    dispatch(campaignLoading())
    apiClient.get(CAMPAIGN).then(m => dispatch(campaignsSuccess(m.data))).catch(e => dispatch(campaignFailed(e.message))).catch(e => dispatch(e.message))
}

const campaignsSuccess = data => ({
    type: ActionTypes.ALL_CAMPAIGNS,
    payload: data
})

const campaignSuccess = message => ({
    type: ActionTypes.CAMPAIGN_SUCCESS,
    payload: message
})

const campaignMonitor = data => ({
    type: ActionTypes.CAMPAIGN_MONITOR_SUCCESS,
    payload: data
})

const campaignMonitoring = data => ({
    type: ActionTypes.CAMPAIGN_MONITORING_SUCCESS,
    payload: data
})

const campaignFailed = message => ({
    type: ActionTypes.CAMPAIGN_FAILED,
    payload: message
})

const campaignLoading = () => ({
    type: ActionTypes.CAMPAIGN_LOADING
})



