import { Form, Input, Button, Checkbox, Row, Col, Spin, notification } from 'antd';
import { useDispatch, useSelector } from "react-redux";
import { loginUser } from "../Actions/UserActions";
import { useEffect, useState, useRef } from "react";
import { useHistory } from "react-router";
import Text from "antd/es/typography/Text";
import { LockOutlined, UserOutlined } from "@ant-design/icons";
import apiClient from '../Shared/apiClient';

const Login = () => {

    const boxStyle = {
        boxShadow: '4px 4px 4px 1px rgba(0,0,0,0.5)',
        padding: '40px 20px',
        background: '#fff',
        borderRadius: 4,
        height: 400,
        minWidth: 300
    }

    const dispatch = useDispatch()
    const formRef = useRef()
    const [form] = Form.useForm()
    const User = useSelector(state => state.User)
    let record = { 'username': sessionStorage.getItem('user'), 'password': sessionStorage.getItem('password') }

    let history = useHistory()
    form.setFieldsValue(record)

    const onFinish = (values) => {
        dispatch(loginUser(values.username, values.password))
    };

    const onFinishFailed = (errorInfo) => {
        console.log('Failed:', errorInfo);
    }

    const openNotificationWithIcon = (type, content) => {
        notification[type]({
            message: type === 'error' ? 'Error' : 'Success',
            description: content,
        })
    }

    useEffect(() => {
        if ((typeof User.errMess === 'object') && User.errMess !== null) {
            if (User.errMess?.data?.errors) {
                let msg = Object.keys(User.errMess.data.errors).map((v) => User.errMess.data.errors[v])
                openNotificationWithIcon('error', msg.map(v => <Text>{v}{"\n"}</Text>))
            }
            else if (User.errMess.data.message) {
                openNotificationWithIcon('error', User.errMess.data.message)
            }
        }
        else if (User.errMess?.message) {
            openNotificationWithIcon('error', User.errMess?.message)
        } else if (User.errMess) {
            openNotificationWithIcon('error', User.errMess)
        }
        if (User.errMess?.errors) {
            formRef.current.setFields([{
                name: 'username',
                errors: User.errMess.errors.username
            }])
        }
    }, [User.errMess])

    useEffect(() => {
        if (User.loggedIn) {
            openNotificationWithIcon('success', "Login success, redirecting...")
            const timer = setTimeout(() => {
                apiClient.get('/api/check-available-minutes').then((response) => {
                    console.log(response);
                }).catch((error) => {
                    console.log(error);
                })
            }, 10000);
            history.push('/')
        }
    }, [User.loggedIn])


    return (
        <Row justify="center" align="middle">
            <Col>
                <div style={boxStyle}>
                    <Spin spinning={User.isLoading} >
                        <Form
                            ref={formRef}
                            name="basic"
                            onFinish={onFinish}
                            onFinishFailed={onFinishFailed}
                            initialValues={record}
                            size="large"
                        >
                            <Form.Item
                                name="username"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please input your username!',
                                    },
                                ]}
                            >
                                <Input prefix={<UserOutlined className="site-form-item-icon" />} placeholder="User Name" />
                            </Form.Item>

                            <Form.Item
                                name="password"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please input your password!',
                                    },
                                ]}
                            >
                                <Input.Password prefix={<LockOutlined className="site-form-item-icon" />} placeholder="Password" />
                            </Form.Item>

                            <Form.Item name="remember" valuePropName="checked">
                                <Checkbox>Remember me</Checkbox>
                            </Form.Item>

                            <Form.Item>
                                <Button size="large" type="primary" shape="rounded" block htmlType="submit">
                                    Submit
                                </Button>
                            </Form.Item>
                        </Form>
                    </Spin>
                </div>
            </Col>
        </Row>
    )
}

export default Login