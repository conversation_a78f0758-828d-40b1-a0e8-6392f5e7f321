import * as ActionTypes from "../Constants/InboundDispositionSummaryConstants"
import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";

export const getInboundSummaryDisposition = () => dispatch => {
    dispatch(inboundDispositionSummaryLoading())
    apiClient.post(`/api/report/inbound-disposition-summary`).then(r => dispatch(inboundDispositionSummarySuccess(r.data))).catch(e => dispatch(inboundDispositionSummaryFailed(handleError(e))))
}
export const getInboundSummaryDispositionReset = () => dispatch => {
    dispatch(inboundDispositionSummaryReset())

}



export const getInboundSummaryDispositionFilter = data => dispatch => {
    dispatch(inboundDispositionSummaryLoading())
    apiClient.post(`/api/report/inbound-disposition-summary-filter`, data).then(r => dispatch(inboundDispositionSummarySuccess(r.data))).catch(e => dispatch(inboundDispositionSummaryFailed(handleError(e))))
}

const inboundDispositionSummaryLoading = () => ({
    type: ActionTypes.INBOUND_DISPOSITION_SUMMARY_LOADING
})
const inboundDispositionSummaryReset = () => ({
    type: ActionTypes.INBOUND_DISPOSITION_SUMMARY_RESET
})

const inboundDispositionSummarySuccess = data => ({
    type: ActionTypes.INBOUND_DISPOSITION_SUMMARY_SUCCESS,
    payload: data
})

const inboundDispositionSummaryFailed = err => ({
    type: ActionTypes.INBOUND_DISPOSITION_SUMMARY_FAILED,
    payload: err
})