import {Card, Col, Image, Row, Table, Typography} from "antd";
import {useDispatch, useSelector} from "react-redux";
import {useEffect} from "react";
import {getCampaignMonitoring} from "../Actions/CampaignActions";
import {CampaignReducer} from "../Reducer/CampaignReducer";
import {CheckCircleTwoTone, CloseCircleTwoTone} from "@ant-design/icons";
import totalCalls from '../Assets/total_calls.png'
import dialedCalls from '../Assets/dial-pad.png'
import answeredCalls from '../Assets/answered-calls.png'
import remainingCalls from '../Assets/remaining-calls.png'

const agentColumns = [
    {
        title: 'Agent ID',
        dataIndex: 'agent_id',
        key: 'agent_id'
    },
    {
        title: 'Agent Name',
        dataIndex: 'agent_name',
        key: 'agent_name'
    },
    {
        title: 'Campaign Name',
        dataIndex: 'name',
        key: 'name'
    },
    {
        title: "Campaign Start",
        dataIndex: "campaign_date",
        key: "campaign_date"
    },
    {
        title: 'Status',
        key: 'status',
        render: (index, value) => {
            return (<>{value.event == "AGENT_START" ? <CheckCircleTwoTone twoToneColor="#1CEB18"/>: <CloseCircleTwoTone twoToneColor="#DE1212"/>}</>)
        }
    }
]

export const CampaignMonitoring = () => {

    const dispatch = useDispatch()
    const campaignMonitor = useSelector(state => state.CampaignReducer)

    useEffect(() => {
        dispatch(getCampaignMonitoring(`api/monitoring/campaign-agent`))
    },[])

    useEffect(() => {
        const interval = setInterval(() => {
            dispatch(getCampaignMonitoring(`api/monitoring/campaign-agent`))
        },20000)
        return () => clearInterval(interval)
    }, [])

    return(<>
        <Typography>Campaign Monitoring</Typography>
        <Card>
            <Row gutter={[10,10]}>
                <Col xl={6} lg={6} md={12} sm={24}>
                    <Card style={{textAlign: 'center'}}>
                        <Image src={totalCalls} width={50} height={50} />
                        <div>
                            <b>{campaignMonitor.campaignMonitoring?.campaign?.length > 0 ? campaignMonitor.campaignMonitoring?.campaign[0]?.total_calls: 0}</b>
                            <br />
                            <b>Total Calls</b>
                        </div>
                    </Card>
                </Col>
                <Col xl={6} lg={6} md={12} sm={24}>
                    <Card style={{textAlign: 'center'}}>
                        <Image src={dialedCalls} width={50} height={50} />
                        <div>
                            <b>{campaignMonitor.campaignMonitoring?.campaign?.length > 0 ? campaignMonitor.campaignMonitoring?.campaign[0]?.total_dialed : 0}</b>
                            <br />
                            <b>Total Dial Calls</b>
                        </div>
                    </Card>
                </Col>
                <Col xl={6} lg={6} md={12} sm={24}>
                    <Card style={{textAlign: 'center'}}>
                        <Image src={answeredCalls} width={50} height={50} />
                        <div>
                            <b>{campaignMonitor.campaignMonitoring?.agent?.length > 0 ? campaignMonitor.campaignMonitoring?.agent.length: 0}</b>
                            <br />
                            <b>Total Agent</b>
                        </div>
                    </Card>
                </Col>
                <Col xl={6} lg={6} md={12} sm={24}>
                    <Card style={{textAlign: 'center'}}>
                        <Image src={remainingCalls} width={50} height={50} />
                        <div>
                            <b>{campaignMonitor.campaignMonitoring?.campaign?.length > 0 ? campaignMonitor.campaignMonitoring?.campaign[0]?.total_remaining: 0}</b>
                            <br />
                            <b>Total Remaining Calls</b>
                        </div>
                    </Card>
                </Col>
                <Col xl={24} lg={24} md={24} sm={24}>
                    <Table dataSource={campaignMonitor.campaignMonitoring?.agent} columns={agentColumns} scroll={{x: 120}} />
                </Col>
            </Row>
        </Card>
        </>)
}