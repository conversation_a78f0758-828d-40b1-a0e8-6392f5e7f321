import * as ActionTypes from "../Constants/OutboundDispositionConstants"
import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";

export const getOutboundDisposition = () => dispatch => {
    dispatch(outboundDispositionLoading())
    apiClient.post(`/api/report/outbound-disposition`).then(r => dispatch(outboundDispositionSuccess(r.data))).catch(e => dispatch(outboundDispositionFailed(handleError(e))))
}

export const getFilteredOutboundDisposition = data => dispatch => {
    dispatch(outboundDispositionLoading())
    apiClient.post(`/api/report/outbound-disposition-filtered`, data).then(r => dispatch(outboundDispositionSuccess(r.data))).catch(e => dispatch(outboundDispositionFailed(handleError(e))))
}

export const outboundDispositionReset = () => dispatch => {

    dispatch(outboundReset());
}

const outboundDispositionLoading = () => ({
    type: ActionTypes.OUTBOUND_DISPOSITION_LOADING
})

const outboundDispositionSuccess = data => ({
    type: ActionTypes.OUTBOUND_DISPOSITION_SUCCESS,
    payload: data
})

const outboundDispositionFailed = err => ({
    type: ActionTypes.OUTBOUND_DISPOSITION_FAILED,
    payload: err
})
const outboundReset = () => ({
    type: ActionTypes.OUTBOUND_DISPOSITION_RESET,

})