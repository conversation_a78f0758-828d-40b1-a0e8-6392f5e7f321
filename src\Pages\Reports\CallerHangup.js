import { useDispatch, useSelector } from "react-redux";
import {
    getC<PERSON>rHangup, getCallerHangupFilter
} from "../../Actions/AgentReportActions";
import { Button, Descriptions, Modal, Space, Spin, Table } from "antd";
import { CSVDownload, CSVLink } from "react-csv";
import ReactExport from "react-export-excel";
import { DesktopOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { useState, useEffect } from 'react'
import CallPerAgentFilter from "../../Components/Reports/CallPerAgentFilter";

const CallerHangup = () => {
    const report = useSelector(state => state.AgentReportReducer)
    const [record, setRecord] = useState(null)
    const [showDetails, setShowDetails] = useState(false)
    const [filter, setFilter] = useState(false)

    const dispatch = useDispatch()

    useEffect(() => {
        dispatch(getCallerHangup())
    }, [])

    function onFilter(values) {
        // console.log(values)
        dispatch(getCallerHangupFilter(values))
        setFilter(false)
    }

    return (
        <Spin spinning={report.isLoading}>
            <CallPerAgentFilter setVisible={setFilter} isLoading={report.isLoading} onCancel={() => setFilter(false)} visible={filter} onCreate={onFilter} record={"ok"} />

            <Button onClick={() => setFilter(true)} type="primary" icon={<PlusCircleOutlined />}>
                Filter
            </Button>

            <Modal
                width="100%"
                visible={showDetails}
                closable={true}
                onCancel={() => setShowDetails(false)}
                onOk={() => setShowDetails(false)}
                centered
            >
                <Descriptions style={{ padding: 20 }} title="Pause Reason Info" bordered size="small">
                    {record && Object.keys(record).map((key, index) => (
                        <Descriptions.Item label={key}>
                            {(record[key]) ? (record[key]) : ""}
                        </Descriptions.Item>))}
                </Descriptions>

            </Modal>

            <div style={{ float: 'right' }}>
                <div style={{ marginBottom: 16 }}>
                    <Space>
                        <Button type={"primary"} onClick={(<CSVDownload data={report.agentReport} target="_blank" />)}>
                            <CSVLink data={report.agentReport} > Download CSV</CSVLink>
                        </Button>

                        <ReactExport.ExcelFile element={<Button type={"default"}>Download Excel</Button>} fileExtension={"xlsx"} filename={"CDR Report"}>
                            <ReactExport.ExcelFile.ExcelSheet data={report.agentReport} >
                                {record && Object.entries(record).map((key, index) => (
                                    <ReactExport.ExcelFile.ExcelColumn label={key} value={key} />
                                ))}
                            </ReactExport.ExcelFile.ExcelSheet>
                        </ReactExport.ExcelFile>
                    </Space>
                </div>
            </div>

            <Table scroll={{ x: 800 }} size="small" bordered dataSource={report.agentReport}>
                <Table.Column dataIndex="queuename" key="queuename" title="queuename" />
                <Table.Column dataIndex="agent" key="agent" title="Agent" />
                <Table.Column dataIndex="Event" key="Event" title="Event" sorter={true} />
                <Table.Column dataIndex="time" key="time" title="Time" />
                <Table.Column align="center" title="Actions" render={(text, record) => (
                    <Space>
                        <Button onClick={() => {
                            setRecord(record)
                            setShowDetails(true)
                        }} type="outlined" icon={<DesktopOutlined />}>Details</Button>
                        {/*<Button onClick={() => {*/}
                        {/*    dispatch(deleteQueue(record.name))*/}
                        {/*}} icon={<DeleteOutlined />} type="danger">Delete</Button>*/}
                    </Space>
                )} />
            </Table>
        </Spin>
    )
}

export default CallerHangup