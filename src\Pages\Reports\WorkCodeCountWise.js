import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, DatePicker, Form, Select, Table, Input, Space, Modal } from "antd";
import { CloudDownloadOutlined, FilterOutlined, ReloadOutlined, SearchOutlined } from "@ant-design/icons";
import apiClient from "../../Shared/apiClient";
import moment from "moment";
import { CSVLink } from "react-csv";
import Highlighter from "react-highlight-words";
const { RangePicker } = DatePicker;
const dateFormat = "YYYY-MM-DD";


const WorkCodeCountWiseReport = () => {
    const [form] = Form.useForm();
    const [data, setData] = useState([]);
    const [columnss, setColumnss] = useState([])
    const [to, setTo] = useState();
    const [from, setFrom] = useState();
    const [loading, setLoading] = useState(false);
    const [fetchReportCheck, setFetchReportCheck] = useState(true);
    const [queues, setQueues] = useState([])
    const [selectQueue, setSelectQueue] = useState('')

    useEffect(() => {
        apiClient.get('api/queue').then((res) => res.data && setQueues(res.data))
    }, [])

    const onDateChange = (date, dateString) => {
        setFrom(moment(dateString[0]));
        setTo(moment(dateString[1]));
    };

    useEffect(() => {
        if (from && to && selectQueue) setFetchReportCheck(false)
        else setFetchReportCheck(true)
    }, [from, to, selectQueue])


    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const searchInput = useRef();
    const [showFilter, setShowFilter] = useState(false)
    const [resetFilter, setResetFilter] = useState(false)

    const resetFormFilter = () => {
        // dispatch(getTrunkPerHourReport())
        // dispatch(trunkPerHourReset())
        // console.log(new Date().toLocaleString() + '')
        setData([])
        form.resetFields()
        setResetFilter(true)
    }



    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }

    // const columns = [
    //     {
    //         title: "Calls Bifurcation",
    //         dataIndex: "queuename",
    //         key: "queuename",
    //         ...getColumnSearchProps('queuename'),
    //     },
    //     {
    //         title: "Total Incoming Calls",
    //         dataIndex: "totalInbounCalls",
    //         key: "totalInbounCalls",
    //         ...getColumnSearchProps('totalInbounCalls'),
    //     },
    //     {
    //         title: "Total Answered Calls",
    //         dataIndex: "totalAnswerCalls",
    //         key: "totalAnswerCalls",
    //         ...getColumnSearchProps('totalAnswerCalls'),

    //     },
    //     {
    //         title: "Total Abandoned/Lost Calls",
    //         dataIndex: "totalAbandonCalls",
    //         key: "totalAbandonCalls",
    //         ...getColumnSearchProps('totalAbandonCalls'),
    //     },
    //     {
    //         title: "Outgoing Calls",
    //         dataIndex: "outgoingCalls",
    //         key: "outgoingCalls",
    //         ...getColumnSearchProps('outgoingCalls'),
    //     },
    //     {
    //         title: "Customer Service Factor",
    //         dataIndex: "CustomerServiceFactor",
    //         key: "CustomerServiceFactor",
    //         ...getColumnSearchProps('CustomerServiceFactor'),
    //     },
    //     {
    //         title: "Answered Calls %",
    //         dataIndex: "percentageOfAnsweredCalls",
    //         key: "percentageOfAnsweredCalls",
    //         ...getColumnSearchProps('percentageOfAnsweredCalls'),
    //     },
    // ];


    const getDateFormatted = (date) => {
        const today = new Date(date);
        const yyyy = today.getFullYear();
        let mm = today.getMonth() + 1; // Months start at 0!
        let dd = today.getDate();

        if (dd < 10) dd = '0' + dd;
        if (mm < 10) mm = '0' + mm;

        return yyyy + '-' + mm + '-' + dd;
    }

    const fetchData = (payload = {}) => {
        setLoading(true)
        apiClient
            .post(`api/report/workcode-date-wise`, payload)
            .then((res) => {
                if (res.data) {
                    setLoading(false)
                    setData(res.data.data)
                    setColumnss(res.data?.columns?.map((col) => {
                        return {
                            title: <div style={{ textTransform: 'capitalize' }}>{col}</div>,
                            dataIndex: col === 'Workcode' ? col : getDateFormatted(col),
                            key: col,
                            width: '200px'
                        }
                    }))
                    setFrom(null)
                    setTo(null)
                    setSelectQueue(null)
                    form.resetFields();
                }
            }).catch(err => console.log(err))
    }

    const fetchReport = () => {
        fetchData({ start: from?._i, end: to?._i, queue: selectQueue })
    };


    return (
        <>
            {/* <Card
                title="Work Code Day Wise"
                style={{ background: "#fff", borderRadius: "2px", padding: 10 }}
                extra={
                    <Form
                        form={form}
                        layout="inline"
                        size="large"
                        style={{ marginTop: "3px" }}
                    >
                        <Form.Item name={"picker"}>
                            <RangePicker
                                format={dateFormat}
                                onChange={onDateChange}
                            />
                        </Form.Item>
                        <Form.Item name={"queue"}>
                            <Select
                                showSearch
                                placeholder="Select Queue"
                                style={{ width: '170px' }}
                                onChange={(e) => setSelectQueue(e)}
                            >
                                {queues.map((queue, index) => (
                                    <Select.Option key={index} value={queue.name}>{queue.name}</Select.Option>
                                ))}
                            </Select>
                        </Form.Item>
                        <Form.Item>
                            <Button
                                size="large"
                                disabled={fetchReportCheck}
                                style={{ marginRight: "0px" }}
                                onClick={fetchReport}
                            >
                                Fetch Report
                            </Button>
                        </Form.Item>
                        <Form.Item>
                            <CSVLink data={data || []} filename="workcode_day_wise_report.csv">
                                <Button size="large" disabled={data.length === 0} icon={<CloudDownloadOutlined />}>Export CSV</Button>
                            </CSVLink>
                        </Form.Item>
                    </Form>
                }
            > */}

            <Table
                title={data => <>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        {`Work Code Day Wise Report`}
                        <Space>
                            <Button onClick={() => resetFormFilter()} type="danger" icon={<ReloadOutlined />}>Reset Filter</Button>
                            <Button onClick={() => setShowFilter(true)} icon={<FilterOutlined />}>Filter</Button>
                            <CSVLink data={data || []} filename="workcode_day_wise_report.csv">
                                <Button size="large" disabled={data.length === 0} icon={<CloudDownloadOutlined />}>Export CSV</Button>
                            </CSVLink>
                        </Space>

                    </div>
                </>}
                columns={columnss}
                dataSource={data}
                pagination={false}
                loading={loading}
                size="default"
                bordered
                scroll={{
                    x: "calc(700px + 50%)",
                    y: 360,
                }}
                rowKey="Workcode"
            />
            {/* </Card> */}

            <WorkCodeDayFilter
                form={form}
                resetFilter={resetFilter}
                visible={showFilter}
                fetchReport={fetchReport}
                // onChange={onChange}
                onDateChange={onDateChange}
                // selectedType={selectedType}
                // onSearch={onSearch}
                // month={month}
                queues={queues}
                setSelectQueue={setSelectQueue}
                // queueField={queueField}
                // setQueueField={setQueueField}
                // handleSelectChange={handleSelectChange}
                dateFormat={dateFormat}
                // setselectedType={setselectedType}
                setVisible={setShowFilter}
            />
        </>
    );
};

export const WorkCodeDayFilter = ({ form, fetchReport, setSelectQueue, setQueueField, queues, dateFormat, onDateChange, visible, setVisible }) => {

    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            okText="Submit"
            onOk={() => form.validateFields()
                .then(value => {
                    // dispatch(getTrunkPerHourReport({ date: filterDate, queue: queue }))
                    fetchReport()
                    setVisible(false)
                })
            }
            // okButtonProps={{
            //     loading: btnLoading,
            //     icon: <SaveOutlined />
            // }}
            title="Work Code Day Wise Filter"
        >
            {/* <Spin spinning={btnLoading} indicator={<SyncOutlined spin />}> */}
            <Form
                form={form}
                layout="vertical"
            >
                <Form.Item name={"date"} rules={[
                    {
                        required: true,
                    },
                ]}>
                    <RangePicker

                        format={dateFormat}
                        onChange={onDateChange}
                    />
                </Form.Item>
                <Form.Item name={"queue"}>
                    <Select
                        showSearch
                        placeholder="Select Queue"
                        style={{ width: '170px' }}
                        onChange={(e) => setSelectQueue(e)}
                    >
                        {queues.map((queue, index) => (
                            <Select.Option key={index} value={queue.name}>{queue.name}</Select.Option>
                        ))}
                    </Select>
                </Form.Item>
            </Form>
            {/* </Spin> */}
        </Modal>
    )
}


export default WorkCodeCountWiseReport;