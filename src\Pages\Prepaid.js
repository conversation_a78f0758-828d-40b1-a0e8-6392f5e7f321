import React, { useState, useEffect } from "react";
import { Form, InputN<PERSON>ber, <PERSON><PERSON>, DatePicker, Card, message } from "antd";
import moment from "moment";
import axios from "axios";

const PrepaidSetting = () => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [switchLoading, setSwitchLoading] = useState(false);
    const [initialValues, setInitialValues] = useState({});
    const [billingType, setBillingType] = useState('postpaid');
    const [currentStartDate, setCurrentStartDate] = useState(null);
    const token = sessionStorage.getItem("auth_token");
    const axiosInstance = axios.create({
        baseURL: process.env.REACT_APP_baseURL,
        headers: {
            Authorization: `Bearer ${token}`,
        },
    });

    // Function to fetch prepaid data
    const getPrepaidData = () => {
        axiosInstance.get("/api/prepaid")
            .then((response) => {
                if (response.data) {
                    const data = response.data.prepaid;
                    setBillingType(data.type || "postpaid");
                    setCurrentStartDate(data.start_date);
                    const formValues = {
                        start_date: data.start_date ? moment(data.start_date) : null,
                        pulse_duration: data.pulse_duration,
                        tariff: data.tariff,
                        amount: data.amount,
                        type: data.type || "postpaid",
                    };
                    setInitialValues(formValues);
                    form.setFieldsValue(formValues);
                }
            })
            .catch((error) => {
                message.error(error?.response?.data?.message || "Failed to fetch prepaid settings.");
                console.error("API Error:", error);
            });
    };

    useEffect(() => {
        getPrepaidData();
    }, []);

    // Function to handle switching between prepaid and postpaid
    const handleSwitchMode = () => {
        setSwitchLoading(true);
        const newType = billingType === 'prepaid' ? 'postpaid' : 'prepaid';

        axiosInstance.post(`/api/prepaid-mood`, { type: newType })
            .then((response) => {
                message.success(response?.data?.message);
                getPrepaidData(); // Refresh data after successful update
            })
            .catch((error) => {
                message.error(error?.response?.data?.message || "Failed to switch billing mode.");
            })
            .finally(() => {
                setSwitchLoading(false);
            });
    };

    const onFinish = (values) => {
        // Only allow form submission in prepaid mode
        if (billingType !== 'prepaid') {
            message.info("Please switch to prepaid mode first to update settings.");
            return;
        }

        setLoading(true);
        const formattedValues = {
            ...values,
            start_date: values.start_date ? values.start_date.format("YYYY-MM-DD HH:mm:ss") : null,
            type: 'prepaid', // Ensure type is set to prepaid
        };

        axiosInstance.put(`/api/prepaid`, formattedValues)
            .then((response) => {
                message.success(response?.data?.message);
                getPrepaidData(); // Refresh data after successful update
            })
            .catch((error) => {
                message.error(error?.response?.data?.message || "Failed to save prepaid settings.");
            })
            .finally(() => {
                setLoading(false);
            });
    };

    return (
        <Card
            title={
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span>{billingType === 'postpaid' ? 'Switch to Prepaid' : 'Prepaid Settings'}</span>
                    <Button
                        type={billingType === 'prepaid' ? 'default' : 'primary'}
                        onClick={handleSwitchMode}
                        loading={switchLoading}
                    >
                        {billingType === 'prepaid' ? 'Switch to Postpaid' : 'Switch to Prepaid'}
                    </Button>
                </div>
            }
            bordered={false}
            style={{ maxWidth: 600, margin: "auto", marginTop: 20 }}
        >
            <Form
                form={form}
                layout="vertical"
                onFinish={onFinish}
                initialValues={initialValues}
            >
                <Form.Item
                    label="Start Date"
                    name="start_date"
                    rules={[
                        {
                            required: billingType === 'prepaid',
                            message: "Please select start date"
                        }
                    ]}
                >
                    <DatePicker
                        showTime
                        format="YYYY-MM-DD HH:mm:ss"
                        style={{ width: "100%" }}
                        disabled={billingType === 'postpaid'}
                    />
                </Form.Item>
                <Form.Item
                    label="Pulse Duration (seconds)"
                    name="pulse_duration"
                    rules={[{ required: true, message: "Please enter pulse duration" }]}
                >
                    <InputNumber min={1} style={{ width: "100%" }} disabled={billingType === 'postpaid'} />
                </Form.Item>

                <Form.Item label="Tariff" name="tariff" rules={[{ required: true, message: "Please enter tariff in Rupees" }]}>
                    <InputNumber min={1} style={{ width: "100%" }} prefix="PKR" disabled={billingType === 'postpaid'} />
                </Form.Item>

                

                <Form.Item label="Amount" name="amount" rules={[{ required: true, message: "Please enter amount in Rupees" }]}>
                    <InputNumber min={1} style={{ width: "100%" }} prefix="PKR" disabled={billingType === 'postpaid'} />
                </Form.Item>

                
                <Form.Item>
                    <Button
                        type="primary"
                        htmlType="submit"
                        loading={loading}
                        block
                        disabled={billingType === 'postpaid'}
                    >
                        Save Prepaid Settings
                    </Button>
                </Form.Item>
            </Form>
        </Card>
    );
};

export default PrepaidSetting;
