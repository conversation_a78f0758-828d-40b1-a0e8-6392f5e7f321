import { Button, Form, Modal, Space, Spin, Table, Upload } from "antd";
import { DeleteOutlined, InboxOutlined, PlusOutlined } from "@ant-design/icons";
import apiClient from "../Shared/apiClient";
import { MEDIAFILE_STORE } from "../Endpoints/MediaFilesRoutes";
import { openNotificationWithIcon } from "../Shared/notification";
import { useDispatch, useSelector } from "react-redux";
import { useState, useEffect } from 'react'
import { deleteMediaFile, getMediaFile } from "../Actions/MediaFileActions";

const MediaFiles = () => {

    const mediaFile = useSelector(state => state.MediaFileReducer)
    // const [showEdit, setShowEdit] = useState(false)
    const [record, setRecord] = useState(null)
    const [showCreate, setShowCreate] = useState(false)

    const [form] = Form.useForm()
    const { Dragger } = Upload
    const dispatch = useDispatch()


    const draggerProps = {
        name: 'file',
        multiple: false,
        method: 'GET',
        action: '',
        beforeUpload: false,
        customRequest: action => {
            let form = new FormData()
            form.append('fileName', action.file)
            apiClient.post(MEDIAFILE_STORE, form, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            })
                .then(() => {
                    action.onSuccess('ok')
                    openNotificationWithIcon('success', "File Insert Successfully")
                }).catch(error => {
                    openNotificationWithIcon('error', error.response.data.errors.fileName)
                    action.onError("error")
                })
        },
    }

    useEffect(() => {
        if (mediaFile.errMess) {
            openNotificationWithIcon('error', mediaFile.errMess)
        }
    }, [mediaFile.errMess])

    useEffect(() => {
        if (mediaFile.message) {
            openNotificationWithIcon('success', mediaFile.message)
            dispatch(getMediaFile())
        }
    }, [mediaFile.message])

    useEffect(() => {
        console.log("media file")
        dispatch(getMediaFile())
    }, [])

    const handleShowCreate = () => {
        setRecord(mediaFile.mediaFiles)
        setShowCreate(true)
    }

    return (
        <Spin spinning={mediaFile.isLoading}>
            <Modal
                centered
                title="Add Media File"
                visible={showCreate}
                destroyOnClose={true}
                closable={true}
                okText="Submit"
                onOk={() => {
                    dispatch(getMediaFile())
                    setShowCreate(false)
                }}
                onCancel={() => {
                    setShowCreate(false)
                }}
            >
                <Form initialValues={{ remember: true, }} form={form} >

                    <Form.Item key="file" name="file" label="File" required>
                        <Dragger {...draggerProps}>
                            <p className="ant-upload-drag-icon">
                                <InboxOutlined />
                            </p>
                            <p className="ant-upload-text">Click or drag file to this area to upload</p>
                            <p className="ant-upload-hint">
                                Support for a single or bulk upload. Strictly prohibit from uploading company data or other
                                band files
                            </p>
                        </Dragger>
                    </Form.Item>
                </Form>
            </Modal>
            <Space style={{ marginBottom: 16 }}>
                <Button onClick={handleShowCreate} icon={<PlusOutlined />} type="primary">Add New</Button>
            </Space>
            <Table scroll={{ x: 800 }} size="small" bordered dataSource={mediaFile.mediaFiles}>
                <Table.Column
                    title="Audio"
                    key="audio"
                    render={(text, record) => (
                        <audio controls>
                            <source src={"http://api.contact-plus.com/contactplusapi/public/storage/" + record.path} />
                        </audio>
                    )}
                />
                <Table.Column dataIndex="created_at" key="created_at" title="Created At" />
                <Table.Column dataIndex="updated_at" key="updated_at" title="Updated At" />
                <Table.Column align="center" title="Actions" render={(text, record) => (
                    <Space>
                        <Button onClick={() => {
                            dispatch(deleteMediaFile(record.id))
                        }} icon={<DeleteOutlined />} type="danger">Delete</Button>
                    </Space>
                )} />
            </Table>
        </Spin>
    )
}

export default MediaFiles