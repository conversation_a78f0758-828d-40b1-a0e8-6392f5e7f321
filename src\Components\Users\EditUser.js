import { Form, Input, Modal, Spin } from "antd";
import { useEffect, useState } from "react"
import { getQueues } from "../../Actions/QueueActions";
import { useDispatch, useSelector } from "react-redux";

const EditUser = ({ visible, onCreate, record, setVisible }) => {

    const [form] = Form.useForm()
    const [showAgentFields, setShowAgentFields] = useState(false)
    const [showQueue, setShowQueue] = useState(false)
    const dispatch = useDispatch()
    const QueueState = useSelector(state => state.QueueReducer)

    useEffect(() => {
        form.setFieldsValue(record)
        setShowAgentFields(record ? record.type !== "Normal" : false)
        setShowQueue(record ? record.type !== "Normal" && record.type !== "Outbound" : false)
    }, [record])

    const onSelectChange = (option) => {
        setShowAgentFields(option !== "Normal")
        // Dispatch queue fetch
        if (option !== "Normal" && option !== "Outbound") {
            dispatch(getQueues())
            setShowQueue(true)
        }
    }

    return (
        <Modal
            visible={visible}
            title={`Edit: ${record?.name} <${record?.email}>`}
            okText="Update"
            cancelText="Cancel"
            closable={true}
            onCancel={() => {
                //form.resetFields()
                setVisible(false)
            }}
            onOk={() => {
                form
                    .validateFields()
                    .then((values) => {
                        // form.resetFields();
                        onCreate(values);
                    })
                    .catch((info) => {
                        console.log('Validate Failed:', info);
                    })
            }}
        >
            <Spin spinning={QueueState.isLoading}>
                <Form
                    form={form}
                    layout="vertical"
                    name="form_in_modal"
                >
                    <Form.Item
                        name="name"
                        label="Name"
                        rules={[
                            {
                                required: true,
                                message: 'Please input the name',
                            },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        name="username"
                        label="Username"
                        rules={[
                            {
                                required: true,
                                message: 'Please input the username',
                            },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        name="email"
                        label="email"
                        rules={[
                            {
                                required: true,
                                message: 'Please input email address',
                            },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    {/* <Form.Item*/}
                    {/*    name="type"*/}
                    {/*    label="Type"*/}
                    {/*    rules={[*/}
                    {/*        {*/}
                    {/*            required: true,*/}
                    {/*            message: 'Please select the type',*/}
                    {/*        },*/}
                    {/*    ]}*/}
                    {/*>*/}
                    {/*    <Select onSelect={onSelectChange}>*/}
                    {/*        <Select.Option value="Normal" key="Normal">Normal</Select.Option>*/}
                    {/*        <Select.Option value="Blended" key="Blended">Blended</Select.Option>*/}
                    {/*        <Select.Option value="Inbound" key="Inbound">Inbound</Select.Option>*/}
                    {/*        <Select.Option value="Outbound" key="Outbound">Outbound</Select.Option>*/}
                    {/*    </Select>*/}
                    {/*</Form.Item>*/}
                    {/*{showAgentFields && <>*/}
                    {/*    <Form.Item name="auth_username" label="Auth Username" rules={[{required: true, message: "Auth username is required"}]}>*/}
                    {/*        <Input/>*/}
                    {/*    </Form.Item>*/}
                    {/*    <Form.Item name="auth_password" label="Auth Password" rules={[{required: true, message: "Auth username is required"}]}>*/}
                    {/*        <Input />*/}
                    {/*    </Form.Item>*/}
                    {/*    {showQueue && <Form.Item*/}
                    {/*        name="queue"*/}
                    {/*        label="Queue"*/}
                    {/*        rules={[*/}
                    {/*            {*/}
                    {/*                required: true,*/}
                    {/*                message: 'Please select the queue',*/}
                    {/*            },*/}
                    {/*        ]}*/}
                    {/*    >*/}
                    {/*        <Select>*/}
                    {/*            {QueueState.queues.map((value, index) => (*/}
                    {/*                <Select.Option value={value.name} key={value.name}>{value.name}</Select.Option>*/}
                    {/*            ))}*/}
                    {/*        </Select>*/}
                    {/*    </Form.Item>}*/}
                    {/*</>} */}
                </Form>
            </Spin>
        </Modal>
    )
}

export default EditUser