import jsPDF from "jspdf";
import "jspdf-autotable";
// Date Fns is used to format the dates we receive
// from our API call
import { format } from "date-fns";

// define a generateCDR_PDF function that accepts a tickets argument
const generateCDR_PDF = (tickets) => {
    // initialize jsPDF
    const doc = new jsPDF();

    // define the columns we want and their titles
    const tableColumn = ["src", "dst", "disposition", "duration", "date"];
    // define an empty array of rows
    const tableRows = [];

    // for each ticket pass all its data into an array
    tickets.forEach(ticket => {
        const ticketData = [
            ticket.src,
            ticket.dst,
            ticket.disposition,
            ticket.duration,
            ticket.start,
            // called date-fns to format the date on the ticket
            // format(new Date(ticket.updated_at), "yyyy-MM-dd")
        ];
        // push each tickcet's info into a row
        tableRows.push(ticketData);
    });

    // ticket title. and margin-top + margin-left
    doc.text("CDR reports", 14, 15);
    // startY is basically margin-top
    doc.autoTable(tableColumn, tableRows, { startY: 20 });
    const date = Date().split(" ");
    // we use a date string to generate our filename.
    const dateStr = date[0] + date[1] + date[2] + date[3] + date[4];
    // we define the name of our PDF file.
    doc.save(`CDR_report_${dateStr}.pdf`);

};

export default generateCDR_PDF;