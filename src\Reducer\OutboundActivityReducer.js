import * as ActionTypes from "../Constants/OutboundActivityConstants"

const initialState = {
    data: [],
    isLoading: false,
    errMess: ''
}

export const OutboundActivityReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.OUTBOUND_ACTIVITY_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.OUTBOUND_ACTIVITY_SUCCESS:
            return { ...state, isLoading: false, data: action.payload }
        case ActionTypes.OUTBOUND_ACTIVITY_FAILED:
            return { ...state, isLoading: false, errMess: action.payload }
        case ActionTypes.OUTBOUND_ACTIVITY_RESET:
            return { ...state, data: [] }
    }
}