import * as ActionTypes from "../Constants/OutboundActivityConstants"

const initialState = {
    data: [],
    abandonedPerHour: [],
    isLoading: false,
    errMess: ''
}

export const AbandonedReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case "ABANDONED_LOADING":
            return { ...state, isLoading: true }
        case "ABANDONED_SUCCESS":
            return { ...state, isLoading: false, data: action.payload }
        case "ABANDONED_PER_HOUR_SUCCESS":
            return { ...state, isLoading: false, abandonedPerHour: action.payload }
        case "ABANDONED_FAILED":
            return { ...state, isLoading: false, errMess: action.payload }
        case "ABANDONED_RESET":
            return { ...state, data: [] }
        case "ABANDONEDCALL_RESET":
            return { ...state, abandonedPerHour: [] }
    }
}