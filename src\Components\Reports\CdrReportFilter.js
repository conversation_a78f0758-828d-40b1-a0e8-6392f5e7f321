import { DatePicker, Form, Input, Modal, Spin } from "antd";

export const CallDetailReportFilter = ({ visible, setVisible, onCreate, isLoading, setFilteredPaginate }) => {
    const [form] = Form.useForm()

    return (
        <Spin spinning={isLoading}>
            <Modal
                visible={visible}
                onCancel={() => {
                    form
                        .validateFields()
                        .then((values) => {
                            onCreate(values);
                            setFilteredPaginate(true)
                            setVisible(false)
                            form.resetFields();
                        })
                        .catch((info) => {
                            console.log('Validate Failed:', info);
                        })
                }}
                onOk={() => {
                    form
                        .validateFields()
                        .then((values) => {
                            onCreate(values);
                            setVisible(false)
                            setFilteredPaginate(false)
                            form.resetFields();
                        })
                        .catch((info) => {
                            console.log('Validate Failed:', info);
                        })
                }}
            >
                <Form
                    form={form}
                    layout="vertical"
                    name="form_in_modal"
                >
                    <Form.Item
                        name="time"
                        label="Time"
                    >
                        <DatePicker.RangePicker showTime />
                    </Form.Item>

                    <Form.Item
                        name="src"
                        label="src"
                        rules={[
                            {
                                message: 'Please input the src',
                            },
                        ]}
                    >
                        <Input />
                    </Form.Item>

                    <Form.Item
                        name="dst"
                        label="dst"
                        rules={[
                            {
                                message: 'Please input the dst',
                            },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                </Form>
            </Modal>
        </Spin>
    );
}

export default CallDetailReportFilter;