import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, Card, DatePicker, Form, Input, Space, Select, Table } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import apiClient from "../../Shared/apiClient";
import { openNotificationWithIcon } from "../../Shared/notification";
import { CSVLink } from "react-csv";
import Highlighter from "react-highlight-words";
const { RangePicker } = DatePicker;
const dateFormat = "YYYY-MM-DD";


const exportCSVHeader = [
    { label: "Queue", key: "queue" },
    { label: "Total Inbound Calls", key: "totalInboundCalls" },
    { label: "Total Answered Calls", key: "totalAnswerCalls" },
    { label: "Total Abandoned Calls", key: "totalAbandonCalls" },
    { label: "Answered Within Threshold (10s)", key: "answeredWithinThreshold" },
    { label: "Answered After Threshold (10s)", key: "answeredAfterThreshold" },
    { label: "Abandoned Calls within (10s)", key: "abandonedCallswithinThreshold" },
    { label: "Abandoned Calls After (10s)", key: "abandonedCallsAfterThreshold" }
];

const OverallCallMetric = () => {
    const [form] = Form.useForm();
    const [data, setData] = useState([]);
    const [to, setTo] = useState();
    const [from, setFrom] = useState();
    const [number, setNumber] = useState();
    const [loading, setLoading] = useState(false);
    const [fetchReportCheck, setFetchReportCheck] = useState(true);
    const [exportReportCheck, setExportReportCheck] = useState(true);
    const [filterType, setFilterType] = useState(null)
    const [monthOrDate, setMonthOrDate] = useState('')
    const [selectQueue, setSelectQueue] = useState('')
    const [queues, setQueues] = useState([])
    // const [seconds, setSeconds] = useState([{ "servicelevel": '10s' }])



    // useEffect(() => {

    //     const secondss = queues.filter((elm, idx) => (
    //         elm.name == selectQueue
    //     ))
    //     setSeconds(secondss)

    // }, [selectQueue])
    // useEffect(() => {
    //     console.log("seconds", seconds)
    // }, [])

    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const searchInput = useRef();



    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }


    const columns = [
        {
            title: "Queue",
            dataIndex: "queue",
            key: "queue",
            ...getColumnSearchProps('queue')
        },
        {
            title: "Total Inbound Calls",
            dataIndex: "totalInboundCalls",
            key: "totalInboundCalls",
            ...getColumnSearchProps('totalInboundCalls')
        },
        {
            title: "Total Answered Calls",
            dataIndex: "totalAnswerCalls",
            key: "totalAnswerCalls",
            ...getColumnSearchProps('totalAnswerCalls')


        },
        {
            title: "Total Abandoned Calls",
            dataIndex: "totalAbandonCalls",
            key: "totalAbandonCalls",
            ...getColumnSearchProps('totalAbandonCalls')
        },
        {
            title: `Answered Within Threshold (10s)`,
            dataIndex: "answeredWithinThreshold",
            key: "answeredWithinThreshold",
            ...getColumnSearchProps('answeredWithinThreshold')
        },
        {
            title: `Answered After Threshold (10s)`,
            dataIndex: "answeredAfterThreshold",
            key: "answeredAfterThreshold",
            ...getColumnSearchProps('answeredAfterThreshold')
        },
        {
            title: `Abandoned Calls within (10s)`,
            dataIndex: "abandonedCallswithinThreshold",
            key: "abandonedCallswithinThreshold",
            ...getColumnSearchProps('abandonedCallswithinThreshold')
        },
        {
            title: `Abandoned Calls After (10s)`,
            dataIndex: "abandonedCallsAfterThreshold",
            key: "abandonedCallsAfterThreshold",
            ...getColumnSearchProps('abandonedCallsAfterThreshold')
        },
    ];


    // const onNumberChange = (value) => {
    //     setFetchReportCheck(false);
    //     setNumber(value.target.value);
    // };

    // const onDateChange = (date, dateString) => {
    //     setFrom(moment(dateString[0]));
    //     setTo(moment(dateString[1]));
    //     setFetchReportCheck(false);
    // };

    const onPickerChange = (date, dateString) => {
        setMonthOrDate(dateString);
    };

    const fetchReport = () => {
        const payload = {
            queuename: selectQueue,
        }
        payload[filterType === "month" ? 'month' : 'day'] = monthOrDate
        // console.log("params ", paramsData)
        setData([])
        setLoading(true);
        apiClient
            .post(`/api/report/overall-call-handling-metric`,
                {
                    ...payload
                }
            )
            .then((resp) => {
                setData(resp.data);
                setLoading(false);
                setExportReportCheck(false);
                // setTo(null)
                // setFrom(null)
                // form.resetFields();
            })
            .catch((err) => {
                setLoading(false);
                openNotificationWithIcon('error', err.message)
            });
    };

    useEffect(() => {

        apiClient.get('api/queue').then((res) => {

            res.data && setQueues(res.data)
        })

    }, [])


    return (
        <>
            <Card
                title="Overall Call Handling Metric"
                style={{ background: "#fff", borderRadius: "2px", padding: 10 }}
                extra={
                    <Form
                        form={form}
                        layout="inline"
                        size="large"
                        style={{ marginTop: "3px" }}
                    >
                        {/* <Form.Item
                            name={"contact"}
                            rules={[
                                {
                                    pattern: new RegExp(/[0-9]/),
                                    message: "Field accepts numbers only.",
                                },
                            ]}
                        >
                            <Input
                                placeholder="Enter Contact Number"
                                size="large"
                                style={{ width: "auto" }}
                                onChange={onNumberChange}
                            />
                        </Form.Item> */}

                        {/* <Form.Item name={"picker"}>
                            <RangePicker format={dateFormat} onChange={onDateChange} />
                        </Form.Item> */}

                        <Form.Item name={'type'}>
                            <Select placeholder="Type" onChange={(val) => setFilterType(val)}>
                                <Select.Option value="date">Date</Select.Option>
                                <Select.Option value="month">Month</Select.Option>
                            </Select>
                        </Form.Item>

                        {filterType && <Form.Item name={'monthOrDate'}>
                            <DatePicker onChange={onPickerChange} picker={filterType} />
                        </Form.Item>}


                        <Form.Item name={"queue"}>
                            <Select
                                showSearch
                                placeholder="Select Queue"
                                style={{ width: '170px' }}
                                onChange={(e) => setSelectQueue(e)}
                            >
                                {queues.map((queue, index) => (
                                    <Select.Option key={index} value={queue.name}>{queue.name}</Select.Option>
                                ))}
                            </Select>
                        </Form.Item>
                        <Form.Item>
                            <Button
                                size="large"
                                onClick={fetchReport}
                                disabled={(selectQueue && monthOrDate) ? false : true}
                            >
                                Fetch Report
                            </Button>
                        </Form.Item>
                        <Form.Item>
                            <CSVLink headers={exportCSVHeader} data={data} filename="Overall Call Metric.csv">
                                <Button disabled={data.length === 0}>Export Report</Button>
                            </CSVLink>
                        </Form.Item>
                    </Form>
                }
            >
                <Table
                    columns={columns}
                    dataSource={data}
                    // pagination={false}
                    scroll={{
                        x: "calc(700px + 50%)",
                        y: 460,
                    }}
                    loading={loading}
                    size="default"
                    rowKey={"callid"}
                    bordered
                // scroll={{
                //     x: "calc(700px + 50%)",
                //     y: 440,
                // }}
                />
            </Card>
        </>
    );
};

export default OverallCallMetric;