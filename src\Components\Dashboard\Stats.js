import React, { useEffect, useState } from "react";
import {GetStats} from "../../Actions/DashboardActions.js";
import {useDispatch, useSelector} from "react-redux";
import {Card, Col, Row, Spin} from "antd"
import {upperCase } from "lodash"

const siteDemo = {
    background: "#ececec",
    padding: 30
}

function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

const Child = ({ title, value }) => (
    value && <Col xs={24} lg={8} md={12}>
        <Card size="small" title={upperCase(title)}>
            <p>{value}</p>
        </Card>
    </Col>
)

const Parent = ({value, title}) => (
    <Row gutter={[8, 8]}>
        <Col style={{textAlign: 'center'}} span={24}>
            <h4>{title}</h4>
        </Col>
        {value && Object.keys(value).map(key => {
            return <Child key={key} title={key} value={value[key]} />
        })}
    </Row>
)

const Title = ({ title }) => (
    <div >
{title}
    </div>  
    
)

const StatsComponent = ({title, value}) => (
    <Row gutter={[8, 8]}>
        <Col style={{textAlign: 'center'}} span={24}>
            <h4>{title}</h4>
        </Col>
        {value && Object.keys(value).map(key => {
            return key === 'ram' || key === 'swap' || key === 'disk' ? <Col key={key} xs={24} lg={8} md={12}>
                <Card title={<Title title={upperCase(key)} />}>
                    <p><b>Total:</b> {formatBytes(value[key]['total'])}</p>
                    <p><b>Free:</b> {formatBytes(value[key]['free'])}</p>
                </Card>
            </Col> : <Child key={key} title={key} value={value[key]} />
        })}
    </Row>
)

const Stats = () => {

    const dispatch = useDispatch()
    const stats = useSelector(state => state.Stats)
    const [client, setClient] = useState(null)
    const [host, setHost] = useState(null)
    const [database, setDatabase] = useState(null)
    const [server, setServer] = useState(null)

    useEffect(() => {
        dispatch(GetStats())
        const interval = setInterval(() => {
            dispatch(GetStats())
        }, 60000)
        return () => clearInterval(interval)
    }, [])

    useEffect(() => {
        setClient(stats.stats?.client)
        setHost(stats.stats?.host)
        setDatabase(stats.stats?.database)
        setServer(stats.stats?.server)
    }, [stats.stats])

    return(
        <Spin spinning={stats.isLoading}>
            <Parent title="Host" value={host} />
            <Parent title="Client" value={client} />
            <Parent title="Database" value={database} />
            {server && server.software && <Parent title="Software" value={server.software}/>}
            {server && server.uptime && <Parent title="Uptime" value={server.uptime}/>}
            {server && server.hardware && <StatsComponent title="Hardware" value={server.hardware}/>}
        </Spin>
    )
}

export default Stats