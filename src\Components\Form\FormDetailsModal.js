import {
    DeleteOutlined,
    DownloadOutlined,
    EditOutlined,
    UploadOutlined,
} from "@ant-design/icons";
import {
    Button,
    Card,
    Form,
    Modal,
    Space,
    message,
    Table,
    Tooltip,
    Upload,
    Tag,
    Input,
    Checkbox,
    Radio,
    Select,
    DatePicker,
    Spin,
} from "antd";
import { useEffect, useState } from "react";
import apiClient from "../../Shared/apiClient";
import { openNotificationWithIcon } from "../../Shared/notification";
import { isArray } from "lodash";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import { useParams } from "react-router-dom/cjs/react-router-dom";
import moment from 'moment';

export const FormDetailsModal = ({
    detailsVisible,
    setDetailsVisible,
    formID,
    setFormID,
}) => {

    const [formDetailHeadings, setFormDetailHeadings] = useState([]);
    const [data, setData] = useState([]);
    const [errorMessage, setErrorMessage] = useState();
    const [form] = Form.useForm();

    // form form detail edit page
    const [formDetailEditVisible, setFormDetailEditVisible] = useState(false)
    const [formDetailEditId, setFormDetailEditId] = useState(null)
    const [formDetailEditData, setFormDetailEditData] = useState({})

    // let history = useHistory();
    // const { id } = useParams();


    useEffect(() => {
        detailsVisible && getFormData();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [detailsVisible]);

    const deleteFormDetail = (id) => {
        apiClient
            .delete(`api/formData/${id}`)
            .then(() => getFormData())
            .catch((err) => console.log(err));
    };

    const actionColumn = [
        {
            title: "Actions",
            dataIndex: "actions",
            key: "actions",
            render: (_, elm, index) => (
                <Space key={index} className="text-right d-flex justify-content-start">
                    <Tooltip title="Edit">
                        <Button
                            type="primary"
                            icon={<EditOutlined />}
                            onClick={() => {
                                setFormDetailEditData(elm.data)
                                setFormDetailEditId(elm.id)
                                setFormDetailEditVisible(true)
                            }}
                        />
                    </Tooltip>
                    <Tooltip title="Delete">
                        <Button
                            icon={<DeleteOutlined />}
                            danger
                            onClick={() => {
                                console.log('fff', elm)
                                deleteFormDetail(elm.id);
                            }}
                        />
                    </Tooltip>
                </Space>
            ),
        },
    ];

    useEffect(() => {
        errorMessage &&
            openNotificationWithIcon(
                "error",
                errorMessage.map((v, i) => v)
            );
        setErrorMessage();
    }, [errorMessage]);

    const getFormData = async () => {
        try {
            const res = await apiClient.get(`api/formData?form_id=${formID}`);
            let showColumns = res.data.headings.map((v, i) => {
                return {
                    title: v.label,
                    dataIndex: v.name,
                    key: i,
                    render: (_, record) => {
                        if (isArray(record.data[v.name])) {
                            return record.data[v.name]?.map((v) => <Tag color="green">{v}</Tag>)
                        } else return record.data[v.name] || "N/A"
                    }
                }
            });
            setFormDetailHeadings([...showColumns, ...actionColumn])
            setData(res.data.record)
        } catch (err) {
            if (err.response) {
                console.log("Error: ", err.response);
            }
        }
    };


    const handleFileUpload = (file) => {
        const formData = new FormData();
        formData.append("form_id", formID);
        formData.append("file", file);
        apiClient
            .post("/api/importFormData", formData)
            .then((response) => {
                getFormData();
                message.success(`${response.data}`);
            })
            .catch((error) => {
                if (error?.response?.data?.errors) {
                    message.error(error.response?.data?.errors.map((v, i) => v))
                } else {
                    message.error(error.response?.data?.map((v, i) => v.errors));
                    message.warn(`Record against the phone number: ${error.response?.data?.map((v, i) => v.values?.phone_number)}`, 8)
                }
            });
    };

    return (
        <Modal
            title="Form Details"
            visible={detailsVisible}
            centered
            focusTriggerAfterClose
            footer={null}
            width="65%"
            onCancel={() => {
                setDetailsVisible(false);
                setFormDetailHeadings([]);
                form.resetFields();
            }}
        >
            <Card
                extra={
                    <Space>
                        <Button
                            icon={<DownloadOutlined />}
                            title="Export"
                            href={`${process.env.REACT_APP_baseURL}/api/exportFormData?form_id=${formID}`}
                            target="_blank"
                        // onClick={() => apiClient.post(`api/exportFormData`, {form_id: formID})}
                        >
                            Export
                        </Button>
                        {/* </CSVLink> */}
                        <Upload
                            accept=".xlsx"
                            action=""
                            showUploadList={false}
                            beforeUpload={handleFileUpload}
                        >
                            <Button
                                icon={<UploadOutlined />}
                                title="Import"
                            // onClick={() => dispatch(deleteForm(item.id))}
                            >
                                Import
                            </Button>
                        </Upload>
                    </Space>
                }
            >
                <Table
                    columns={formDetailHeadings || []}
                    loading={!data}
                    dataSource={data || []}
                    scroll={{ x: 1500 }}
                    rowKey={"id"}
                />
            </Card>

            {/* this is an update modal */}
            <FormDetailUpdateModal
                id={formDetailEditId}
                isVisible={formDetailEditVisible}
                onCancel={() => setFormDetailEditVisible(false)}
                getFormData={getFormData}
            />
        </Modal>
    );
};

const FormDetailUpdateModal = ({ id, isVisible, onCancel, getFormData }) => {
    const [form] = Form.useForm()
    const [formFields, setFormFields] = useState([])
    const [loading, setLoading] = useState(false)

// old
    // const fetchFormDetail = () => {
    //     setLoading(true)
    //     apiClient.get(`api/formData/${id}`).then((res) => {
    //         if (res.data) {
    //             setFormFields(res.data?.form_fields)
    //             form.setFieldsValue(res.data?.data)
    //             setLoading(false)
    //         }
    //     }).catch((err) => {
    //         openNotificationWithIcon('error', "Network errors")
    //     })
    // }
    const fetchFormDetail = () => {
        setLoading(true);
        apiClient.get(`api/formData/${id}`).then((res) => {
            if (res.data) {
                setFormFields(res.data?.form_fields);
                    const formValues = { ...res.data?.data };
                res.data?.form_fields.forEach((field) => {
                    if (field.type === "date" && formValues[field.name]) {
                        formValues[field.name] = moment(formValues[field.name]);
                    }
    
                    if (field.type === "checkbox" && typeof formValues[field.name] === "string") {
                        try {
                            formValues[field.name] = JSON.parse(formValues[field.name]);
                        } catch {
                            formValues[field.name] = [formValues[field.name]];
                        }
                    }
                });
    
                form.setFieldsValue(formValues);
                setLoading(false);
            }
        }).catch((err) => {
            openNotificationWithIcon("error", "Network errors");
            setLoading(false);
        });
    };
    useEffect(() => {
        if (isVisible && id) {
            form.resetFields();
            fetchFormDetail()
        }
    }, [id, isVisible])

    // render Inputs
    const renderInputs = (type, form_field_options, name) => {
        switch (type) {
            case 'input':
                return <Input disabled={name === 'phone_number'} />
            case "textarea":
                return <Input.TextArea />;
            case "date":
                return <DatePicker style={{ width: "100%" }} />;
            case "select":
                return (
                    <Select>
                        {form_field_options &&
                            form_field_options.map((option) => (
                                <Select.Option key={option.label}>{option.label}</Select.Option>
                            ))}
                    </Select>
                )
            case "radio":
                return (
                    <Radio.Group key={id}>
                        {form_field_options &&
                            form_field_options.map((option) => (
                                <Radio value={option.label} key={option.id}>
                                    {option.label}
                                </Radio>
                            ))}
                    </Radio.Group>
                );
            case "checkbox":
                return (
                    <Checkbox.Group >
                        {form_field_options &&
                            form_field_options.map((option) => (
                                <Checkbox value={option.label} key={option.id}>
                                    {option.label}
                                </Checkbox>
                            ))}
                    </Checkbox.Group>
                );
            default:
                break;
        }
    }


    return (
        <Modal
            title={`Edit ${id}`}
            visible={isVisible}
            closable={false}
            okText="Update"
            onOk={() => {
                form.validateFields().then((result) => {
                    setLoading(true)
                    apiClient.put(`api/formData/${id}`, result).then((res) => {
                        getFormData()
                        setLoading(false)
                        onCancel();
                    }).catch((err) => {
                        openNotificationWithIcon('error', 'Network Error!')
                        setLoading(false)
                        onCancel();
                    })
                })
            }}
            onCancel={onCancel}
        >
            {loading ? <Spin /> : <Form
                name="form-detail-update"
                form={form}
                labelCol={{
                    span: 24
                }}
            >
                {formFields.length > 0 && formFields.map((form, index) => (
                    <Form.Item
                        key={index}
                        name={form.name}
                        label={form.label}
                        required={form.required}
                    >
                        {renderInputs(form.type, form.form_field_options, form.name)}
                    </Form.Item>
                ))}
            </Form>}
        </Modal>
    )
}