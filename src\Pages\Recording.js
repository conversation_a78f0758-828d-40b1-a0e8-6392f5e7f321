import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Divider,
  Typography,
  Form,
  Input,
  message,
  Modal,
  Table,
  Card,
  Row,
  Col,
  Statistic,
  Descriptions,
  Upload,
  Space,
  Popconfirm,
} from "antd";
import { PlusOutlined, UploadOutlined, DeleteOutlined } from "@ant-design/icons";
import apiClient from "../Shared/apiClient";
import echo from "../Shared/echo";

const { Title } = Typography;

const RecordingPortal = () => {
  const [recordings, setRecordings] = useState([]); 
  const [loading, setLoading] = useState(false); 
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm(); 

  // Fetch recordings from the API
  const fetchRecordings = async () => {
    setLoading(true);
    try {
    echo.channel('recordings')
         .listen('.recordings', (data) => {
           console.log(data.recordings); 
           setRecordings(data.recordings);
         });
      const response = await apiClient.get("api/agentless/recordings");
      setRecordings(response.data); 
    } catch (error) {
      message.error(error?.response?.data);
    } finally {
      setLoading(false);
    }
  };


  const handleCreate = async (values) => {
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append("file", values.file.fileList[0].originFileObj);

     const response =  await apiClient.post("api/agentless/recording", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      message.success( response?.data);
      setIsModalVisible(false);
      form.resetFields(); 
      fetchRecordings(); 
    } catch (error) {
      message.error(error?.response?.data);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    setLoading(true);
    try {
     const response =  await apiClient.delete(`/api/agentless/recording/${id}`);
      message.success(response.data);
      fetchRecordings();
    } catch (error) {
      message.error(error.response.data);
    } finally {
      setLoading(false);
    }
  };

  // Table columns
  const columns = [
    { title: "ID", dataIndex: "id", key: "id" },
    { title: "File Name", dataIndex: "file_name", key: "file_name" },
    { title: "Created At", dataIndex: "created_at", key: "created_at" },
    {
      title: "Actions",
      key: "actions",
      render: (_, record) => (
        <Popconfirm
  title="Are you sure you want to delete this recording?"
  onConfirm={() => handleDelete(record.id)}
  okText="Yes"
  cancelText="No"
>
  <Button icon={<DeleteOutlined />} danger>
    Delete
  </Button>
</Popconfirm>
        
      ),
    },
  ];

  
  useEffect(() => {
    fetchRecordings();
  }, []);

  return (
    <div style={{ padding: "20px" }}>
      {/* Recording Overview Section */}
      <Divider orientation="center">Recordings</Divider>
     

      {/* Recording Management Table */}
      <Card
        title="Recording Management"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsModalVisible(true)}
          >
            Add Recording
          </Button>
        }
        style={{ marginTop: "20px" }}
      >
        <Table
          columns={columns}
          dataSource={recordings}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 5 }}
          scroll={{ x: 800 }}
          expandable={{
            expandedRowRender: (record) => (
              <Descriptions bordered style={{ width: "100%" }}>
                <Descriptions.Item>
                  <audio
                    controls
                    src={`${process.env.REACT_APP_baseURL}/api/file/${record.file_path}`}
                  />
                </Descriptions.Item>
              </Descriptions>
            ),
          }}
        />
      </Card>

      {/* Modal for adding recordings */}
      <Modal
        title="Add Recording"
        visible={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        okText="Add Recording"
        width={700}
      >
        <Form form={form} onFinish={handleCreate}>
          <Form.Item
            name="file"
            label="File"
            rules={[{ required: true, message: "Please upload a recording file"}]}
          >
            <Upload.Dragger
              listType="picture"
              maxCount={1}
              beforeUpload={(file) => {
                const isAudio = file.type.startsWith("audio/");
                if (!isAudio) {
                  message.error("You can only upload audio files");
                  return isAudio || Upload.LIST_IGNORE;
                }
                return false;
              }}
            >
              <Button icon={<UploadOutlined />}>Upload</Button>
              <br />
              <Typography.Text>
                .wav format and the file must be PCM Encoded, 16 Bits, at 8000Hz
              </Typography.Text>
            </Upload.Dragger>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default RecordingPortal;