import * as ActionTypes from '../Constants/EmailSettingConstants'

const initState = {
    isLoading: false,
    errMess: null,
    message: null,
    setting: []
}



export const EmailSettingReducer = (state = initState, action) =>
{
    switch (action.type)
    {
        default:
            return {...state}
        case ActionTypes.EMAIL_SETTING_SUCCESS:
            return {...state, isLoading: false, message: null, setting: action.payload, errMess: null}
        case ActionTypes.EMAIL_SETTING_UPDATE:
            return {...state, isLoading: false, message: action.payload, errMess: null}
        case ActionTypes.EMAIL_SETTING_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.EMAIL_SETTING_FAILED:
            return {...state, isLoading: false, errMess: action.payload, message: null}
    }
}