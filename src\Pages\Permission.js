import { useDispatch, useSelector } from "react-redux";
import { Button, Form, Modal, Row, Select, Space, Spin, Table, Tag } from "antd";
import { openNotificationWithIcon } from "../Shared/notification";
import {
    CloseCircleFilled,
    PlusOutlined
} from "@ant-design/icons";
import { useState, useEffect } from "react";
import {
    createPermission,
    deletePermission,
    getPermission,
    getPermissionModule,
    // updatePermission
} from "../Actions/PermissionActions";

const Permission = () => {

    const permission = useSelector(state => state.PermissionReducer)
    // const [showEdit, setShowEdit] = useState(false)
    const [form] = Form.useForm()
    const [record, setRecord] = useState(null)
    const [showCreate, setShowCreate] = useState(false)
    const dispatch = useDispatch()

    useEffect(() => {
        dispatch(getPermissionModule())
        dispatch(getPermission())
    }, [])

    useEffect(() => {
        if (permission.errMess) {
            openNotificationWithIcon('error', permission?.errMess?.data?.message)

        }
    }, [permission.errMess])

    useEffect(() => {
        if (permission.message) {
            openNotificationWithIcon('success', permission.message)
            dispatch(getPermission())
        }
    }, [permission.message])

    const handleCreate = values => {
        dispatch(createPermission({ name: values.permission + "_" + values.module }))
        setShowCreate(false)
    }

    const handleShowCreate = () => {
        setRecord(permission.permission)
        setShowCreate(true)
    }

    return (
        <Spin spinning={permission.isLoading}>
            <Modal
                centered
                title="Add Permission"
                visible={showCreate}
                destroyOnClose={true}
                closable={true}
                okText="Submit"
                onOk={() => {
                    form.validateFields()
                        .then(values => handleCreate(values))
                        .catch(error => console.log(error))
                }}
                onCancel={() => {
                    setShowCreate(false)
                }}
            >
                <Form
                    form={form}
                    layout="inline"
                    name="form_in_modal"
                >
                    <Row>

                        <Form.Item
                            name="permission"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select the permission',
                                },
                            ]}
                        >
                            <Select style={{ width: 200 }} placeholder="Select Permission">
                                <Select.Option value="create" key="create">Create</Select.Option>
                                <Select.Option value="read" key="read">Read</Select.Option>
                                <Select.Option value="update" key="update">Update</Select.Option>
                                <Select.Option value="delete" key="delete">Delete</Select.Option>
                                <Select.Option value="view" key="view">View</Select.Option>
                            </Select>
                        </Form.Item>
                    </Row>
                    <Row>
                        <Form.Item
                            name="module"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select the module',
                                },
                            ]}
                        >
                            <Select style={{ width: 200 }} placeholder="Select Module">
                                {permission.modules && permission.modules.map((value, index) => (
                                    <Select.Option value={value} key={value}>{value}</Select.Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Row>
                </Form>
            </Modal>
            <Space style={{ marginBottom: 16 }}>
                <Button onClick={handleShowCreate} icon={<PlusOutlined />} type="primary">Add New</Button>
            </Space>
            <Table scroll={{ x: 800 }} size="small" dataSource={permission.permissions} pagination={{
                pageSize: 100
            }}>
                <Table.Column dataIndex="name" key="name" title="Name" />
                <Table.Column dataIndex="permissions" key="permissions" title="Permissions"
                    render={(permissions, record) => (
                        permissions && permissions.map((tag, index) => (
                            <>
                                <Tag color='green' style={{ margin: 0 }} key={tag.id} >
                                    {tag}
                                </Tag>
                                <Tag color='volcano' key={index + 2} onClick={() => {
                                    dispatch(deletePermission(record.id[index]))
                                }} style={{ cursor: 'pointer' }}>
                                    <CloseCircleFilled />
                                </Tag>
                            </>
                        )))}
                />
            </Table>
        </Spin>
    )
}

export default Permission