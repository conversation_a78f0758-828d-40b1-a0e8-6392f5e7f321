import * as ActionTypes from '../Constants/SettingConstants'

const initState = {
    isLoading: false,
    errMess: null,
    message: null,
    setting: []
}

export const SettingReducer = (state = initState, action) =>
{
    switch (action.type)
    {
        default:
            return {...state}
        case ActionTypes.SETTING_SUCCESS:
            return {...state, isLoading: false, message: null, setting: action.payload, errMess: null}
        case ActionTypes.SETTING_UPDATE:
            return {...state, isLoading: false, message: action.payload, errMess: null}
        case ActionTypes.SETTING_LOADING:
            return {isLoading: true}
        case ActionTypes.SETTING_FAILED:
            return {...state, isLoading: false, errMess: action.payload, message: null}
    }
}