import { Button, Card, DatePicker, Form, Modal, Select } from 'antd'
import React from 'react'

export default function ChannelOccupancyFilter({ visible, onFinish, setVisible }) {
    return (

        <Modal
            open={visible}
            onCancel={() => {
                setVisible(false)
            }}

            footer={null}
        >

            <Card style={{ width: '100%', paddingTop: '20px' }}
                bordered={false}
            >

                <Form
                    name="basic"
                    // labelCol={{ span: 2 }}
                    // wrapperCol={{ span: 16 }}
                    onFinish={onFinish}
                >
                    <Form.Item label="Select Type"
                        rules={[{ required: true }]}
                        name="type">
                        <Select placeholder="Select Type">
                            <Select.Option value="">Select Type</Select.Option>
                            <Select.Option value="inbound">Inbound</Select.Option>
                            <Select.Option value="outbound">Outbound</Select.Option>
                        </Select>
                    </Form.Item>
                    <Form.Item label="Select Date"
                        rules={[{ required: true }]}
                        name="date">
                        <DatePicker style={{ width: "100%" }} />
                    </Form.Item>
                    <Form.Item wrapperCol={{ offset: 2, span: 16 }}>
                        <Button type="primary" htmlType="submit">Submit</Button>
                    </Form.Item>
                </Form>
            </Card>

        </Modal>


    )
}
