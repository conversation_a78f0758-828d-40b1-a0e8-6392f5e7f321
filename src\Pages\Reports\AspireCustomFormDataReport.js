import React, { useState, useEffect } from "react"
import { Button, Space, Table, Input, Pagination } from "antd";
import { useDispatch, useSelector } from "react-redux";
import {
    getFormDataReportFiltered
} from "../../Actions/AspireCustomFormDataReportActions";
import { DownloadOutlined, FilterOutlined, ReloadOutlined, SearchOutlined } from "@ant-design/icons";
import { AspireCustomFormDataReportFilter } from "../../Components/Reports/AspireCustomFormDataReportFilter";
import apiClient from "../../Shared/apiClient";
import ReactAudioPlayer from "react-audio-player";
import moment from "moment";
import { CSVLink } from "react-csv";
const { Search } = Input;

const handleSearch = (confirm) => {
    confirm();
};
const handleReset = (clearFilters) => {

    clearFilters({ confirm: true });
};


const columns = [
    {
        title: "Agent Name",
        dataIndex: "name",
        key: "name",
        render: (_, { name }) => name || "n/a",
        // Add filterDropdown and filterIcon for search in this column
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Search
                    placeholder={`Search Agent Name`}
                    value={selectedKeys[0]}
                    onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(confirm)}
                    style={{ width: 188, marginBottom: 8, display: "block" }}
                />
                <Button
                    type="primary"
                    onClick={() => handleSearch(confirm)}
                    icon={<SearchOutlined />}
                    size="small"
                    style={{ width: 90, marginRight: 8 }}
                >
                    Search
                </Button>
                <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                    Reset
                </Button>
            </div>
        ),
        filterIcon: (filtered) => <SearchOutlined style={{ color: filtered ? "#1890ff" : undefined }} />,
        onFilter: (value, record) => record.name.toLowerCase().includes(value.toLowerCase())
    },
    {
        title: "Call Status",
        dataIndex: "disposition",
        key: "disposition",
        render: (_, { disposition }) => disposition || "n/a",
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Search
                    placeholder={`Search Agent Name`}
                    value={selectedKeys[0]}
                    onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(confirm)}
                    style={{ width: 188, marginBottom: 8, display: "block" }}
                />
                <Button
                    type="primary"
                    onClick={() => handleSearch(confirm)}
                    icon={<SearchOutlined />}
                    size="small"
                    style={{ width: 90, marginRight: 8 }}
                >
                    Search
                </Button>
                <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                    Reset
                </Button>
            </div>
        ),
        filterIcon: (filtered) => <SearchOutlined style={{ color: filtered ? "#1890ff" : undefined }} />,
        onFilter: (value, record) => record.disposition.toLowerCase().includes(value.toLowerCase())
    },
    {
        title: "Call Duration (sec)",
        dataIndex: "duration",
        key: "duration",
        render: (_, { duration }) => duration || "n/a",
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Search
                    placeholder={`Search Agent Name`}
                    value={selectedKeys[0]}
                    onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(confirm)}
                    style={{ width: 188, marginBottom: 8, display: "block" }}
                />
                <Button
                    type="primary"
                    onClick={() => handleSearch(confirm)}
                    icon={<SearchOutlined />}
                    size="small"
                    style={{ width: 90, marginRight: 8 }}
                >
                    Search
                </Button>
                <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                    Reset
                </Button>
            </div>
        ),
        filterIcon: (filtered) => <SearchOutlined style={{ color: filtered ? "#1890ff" : undefined }} />,
        onFilter: (value, record) => record.duration.toString().includes(value.toString())
    },
    {
        title: "Call Start",
        dataIndex: "start",
        key: "start",
        render: (_, { start }) => start || "n/a"
    },
    {
        title: "Call End",
        dataIndex: "end",
        key: "end",
        render: (_, { end }) => end || "n/a"
    },
    {
        title: 'Call type',
        dataIndex: 'accountcode',
        key: 'accountcode',
        render: (_, { accountcode }) => accountcode || "n/a",
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Search
                    placeholder={`Search Agent Name`}
                    value={selectedKeys[0]}
                    onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(confirm)}
                    style={{ width: 188, marginBottom: 8, display: "block" }}
                />
                <Button
                    type="primary"
                    onClick={() => handleSearch(confirm)}
                    icon={<SearchOutlined />}
                    size="small"
                    style={{ width: 90, marginRight: 8 }}
                >
                    Search
                </Button>
                <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                    Reset
                </Button>
            </div>
        ),
        filterIcon: (filtered) => <SearchOutlined style={{ color: filtered ? "#1890ff" : undefined }} />,
        onFilter: (value, record) => record.accountcode.toString().includes(value.toString())
    },
    {
        title: 'Source',
        dataIndex: 'src',
        key: 'src',
        render: (_, { src }) => src || "n/a",
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Search
                    placeholder={`Search Agent Name`}
                    value={selectedKeys[0]}
                    onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(confirm)}
                    style={{ width: 188, marginBottom: 8, display: "block" }}
                />
                <Button
                    type="primary"
                    onClick={() => handleSearch(confirm)}
                    icon={<SearchOutlined />}
                    size="small"
                    style={{ width: 90, marginRight: 8 }}
                >
                    Search
                </Button>
                <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                    Reset
                </Button>
            </div>
        ),
        filterIcon: (filtered) => <SearchOutlined style={{ color: filtered ? "#1890ff" : undefined }} />,
        onFilter: (value, record) => record.src.toString().includes(value.toString())
    },
    {
        title: 'Destination',
        dataIndex: 'dst',
        key: 'dst',
        render: (_, { dst }) => dst || "n/a",
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Search
                    placeholder={`Search Agent Name`}
                    value={selectedKeys[0]}
                    onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(confirm)}
                    style={{ width: 188, marginBottom: 8, display: "block" }}
                />
                <Button
                    type="primary"
                    onClick={() => handleSearch(confirm)}
                    icon={<SearchOutlined />}
                    size="small"
                    style={{ width: 90, marginRight: 8 }}
                >
                    Search
                </Button>
                <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                    Reset
                </Button>
            </div>
        ),
        filterIcon: (filtered) => <SearchOutlined style={{ color: filtered ? "#1890ff" : undefined }} />,
        onFilter: (value, record) => record.dst.toString().includes(value.toString())
    },
]

let exportRoute = `${process.env.REACT_APP_baseURL}/api/exportCustomFormData`;

export const AspireCustomFormDataReport = () => {



    const dispatch = useDispatch()
    const formDataReportState = useSelector(state => state.FormDataReportReducer)
    const [filter, setFilter] = useState(false)
    const [filterValues, setFilterValues] = useState(null)
    const [agents, setAgents] = useState([])
    const [formId, setFormId] = useState([])
    const [callStatus, setCallStatus] = useState([])
    const [updatedCol, setUpdatedCol] = useState(columns)
    const [isFiltered, setIsFiltered] = useState(false)
    const [filterCheck, setFilterCheck] = useState(true)
    const [data, setData] = useState([])
    const [tableParams, setTableParams] = useState(
        {
            current: 1,
            pageSize: 15,
            total: 0
        });

    useEffect(() => {
        if (formDataReportState?.formData?.record?.data) setData(formDataReportState?.formData?.record?.data)
    }, [formDataReportState])

    useEffect(() => {
        if (filterValues) {
            if (filterValues?.form_id) {
                exportRoute = `${exportRoute}?form_id=${filterValues.form_id}`;
            }
            if (filterValues?.start) {
                exportRoute = `${exportRoute}&start=${moment(filterValues.start).format("YYYY-MM-DD HH:mm:ss")}`
            }
            if (filterValues?.end) {
                exportRoute = `${exportRoute}&end=${moment(filterValues.end).format("YYYY-MM-DD HH:mm:ss")}`
            }
            if (filterValues?.source) {
                exportRoute = `${exportRoute}&source=${filterValues.source}`
            }
            if (filterValues?.destination) {
                exportRoute = `${exportRoute}&destination=${filterValues.destination}`
            }
            if (filterValues?.callStatus) {
                exportRoute = `${exportRoute}&callStatus=${filterValues.callStatus}`
            }
        }
    }, [filterValues])

    useEffect(() => {
        const newCol = formDataReportState?.formData?.headings?.map((head) => {
            console.log('ss', head)
            return {
                title: head.label, dataIndex: head.name, key: head.name,
                render: (_, { data }) => {
                    // console.log("data", JSON.parse(data)[head?.name])
                    // console.log("data", typeof (data))
                    if (Array.isArray(JSON.parse(data)[head?.name])) {

                        console.log("data", JSON.parse(data)[head?.name][0].map((elm) => elm).join(""))
                        return data && JSON.parse(data)[head?.name][0].map((elm) => elm.toLowerCase()).join("");
                    }
                    else {

                        return data && JSON.parse(data)[head?.name]
                    }
                },
                filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
                    <div style={{ padding: 8 }}>
                        <Search
                            placeholder={`Search Agent Name`}
                            value={selectedKeys[0]}
                            onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                            onPressEnter={() => handleSearch(confirm)}
                            style={{ width: 188, marginBottom: 8, display: "block" }}
                        />
                        <Button
                            type="primary"
                            onClick={() => handleSearch(confirm)}
                            icon={<SearchOutlined />}
                            size="small"
                            style={{ width: 90, marginRight: 8 }}
                        >
                            Search
                        </Button>
                        <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                            Reset
                        </Button>
                    </div>
                ),

                filterIcon: (filtered) => <SearchOutlined style={{ color: filtered ? "#1890ff" : undefined }} />,
                onFilter: (value, record) => record.data.toLowerCase().includes(value.toLowerCase())
            }
        })

        setTableParams({ current: formDataReportState.formData.record?.current_page, pageSize: formDataReportState.formData.record?.per_page, total: formDataReportState.formData.record?.total })
        if (newCol && newCol.length > 0) setUpdatedCol(state => [...state, ...newCol])
        else setUpdatedCol(columns)

        console.log("checkk", formDataReportState)

    }, [formDataReportState.formData, filter])

    // useEffect(() => {
    //     if (formDataReportState.data) {
    //         setData(callDetailReportState.data.data);
    //         setPaginate({ ...paginate, total: formDataReportState.data.total });
    //     }
    // }, [formDataReportState, pageSize]);

    useEffect(() => {

        console.log("Check", formDataReportState?.formData?.record)
    }, [])


    const handleReset = (clearFilters) => {
        exportRoute = `${process.env.REACT_APP_baseURL}/api/exportCustomFormData`;
        dispatch(getFormDataReportFiltered({}))
        clearFilters({ confirm: true });
        setUpdatedCol([])
        setIsFiltered(false)
        setFilterValues(null)
    }

    const handlePagination = (pagination) => {
        const { current, pageSize } = pagination;
        setUpdatedCol(columns)
        dispatch(getFormDataReportFiltered(filterValues, current, pageSize))

        setTableParams({ ...pagination })
    }

    function onFilter(values) {
        setUpdatedCol(columns)
        exportRoute = `${process.env.REACT_APP_baseURL}/api/exportCustomFormData`
        setIsFiltered(true)
        setFilterValues(values)
        dispatch(getFormDataReportFiltered(values))
        setFilter(false)

    }

    // const handleExport = () => {
    //     if (!formDataReportState?.formData?.record?.data.length) return;

    //     const params = {};

    //     if (filterValues?.form_id) params.form_id = filterValues.form_id;
    //     if (filterValues?.start) params.start = moment(filterValues.start).format("YYYY-MM-DD HH:mm:ss");
    //     if (filterValues?.end) params.end = moment(filterValues.end).format("YYYY-MM-DD HH:mm:ss");
    //     if (filterValues?.source) params.source = filterValues.source;
    //     if (filterValues?.destination) params.destination = filterValues.destination;
    //     if (filterValues?.callStatus) params.callStatus = filterValues.callStatus;

    //     apiClient.get('/api/exportCustomFormData', {
    //         params,
    //         responseType: 'blob',
    //     })
    //         .then(response => {
    //             const blob = new Blob([response.data], { type: 'text/csv' });
    //             const url = window.URL.createObjectURL(blob);
    //             const a = document.createElement('a');
    //             a.href = url;

    //             const disposition = response.headers['content-disposition'];
    //             let filename = "export.csv";
    //             if (disposition && disposition.includes('filename=')) {
    //                 filename = disposition.split('filename=')[1].replace(/"/g, '');
    //             }

    //             a.download = filename;
    //             document.body.appendChild(a);
    //             a.click();
    //             a.remove();
    //             window.URL.revokeObjectURL(url);
    //         })
    //         .catch(() => {
    //             alert("Export failed. Please try again.");
    //         });
    // };

    const handleExport = () => {
        if (!formDataReportState?.formData?.record?.data.length) return;

        const payload = {};

        if (filterValues?.form_id) payload.form_id = filterValues.form_id;
        if (filterValues?.start) payload.start = moment(filterValues.start).format("YYYY-MM-DD HH:mm:ss");
        if (filterValues?.end) payload.end = moment(filterValues.end).format("YYYY-MM-DD HH:mm:ss");
        if (filterValues?.source) payload.source = filterValues.source;
        if (filterValues?.destination) payload.destination = filterValues.destination;
        if (filterValues?.callStatus) payload.callStatus = filterValues.callStatus;

        apiClient.post('/api/exportCustomFormData', payload, {
            responseType: 'blob',
        })
            .then(response => {
                const blob = new Blob([response.data], { type: 'text/csv' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;

                const disposition = response.headers['content-disposition'];
                let filename = "merge-cdr-report-export.csv";
                if (disposition && disposition.includes('filename=')) {
                    filename = disposition.split('filename=')[1].replace(/"/g, '');
                }

                a.download = filename;
                document.body.appendChild(a);
                a.click();
                a.remove();
                window.URL.revokeObjectURL(url);
            })
            .catch(async (error) => {
                if (error.response?.data instanceof Blob) {
                    try {
                        const text = await error.response.data.text();
                        const json = JSON.parse(text);
                        alert(json.message || "Export failed. Please try again.");
                    } catch {
                        alert("Export failed. Please try again.");
                    }
                } else {
                    alert(error.response?.data?.message || "Export failed. Please try again.");
                }
            });
    };


    return (
        <>

            <AspireCustomFormDataReportFilter
                setVisible={setFilter}
                isLoading={formDataReportState.isLoading}
                onCancel={() => setFilter(false)}
                // filterCheck={filterCheck}
                setFilterCheck={setFilterCheck}
                visible={filter}
                onCreate={onFilter}
                record={"ok"}
                agents={agents}
                setAgents={setAgents}
                formId={formId}
                setFormId={setFormId}
                setCallStatus={setCallStatus}
                form_id={formId}
                callStatus={callStatus}
            />
            <Table
                dataSource={formDataReportState?.formData?.record?.data || []}
                rowKey={record => record.recordingfile}
                columns={updatedCol}
                scroll={{ x: 1100 }}
                loading={formDataReportState.isLoading}
                pagination={tableParams}
                // pagination={false}
                title={d => <>

                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        Merge CDR Form Data Report
                        <Space>
                            <Button
                                icon={<ReloadOutlined />}
                                type="primary"
                                danger
                                onClick={handleReset}
                            >
                                Reset Filter
                            </Button>
                            <Button icon={<FilterOutlined />} onClick={() => setFilter(true)}>Filter</Button>

                            {/* <Button
                                type="primary"
                                icon={<DownloadOutlined />}
                                href={exportRoute}
                                disabled={formDataReportState?.formData?.record?.data.length == 0}
                                // href={`${process.env.REACT_APP_baseURL}/api/exportCustomFormData?form_id=${filterValues?.form_id || null}&callStatus=${filterValues?.callStatus || null}&destinat>
                                target="_blank"
                            >Download</Button> */}
                            <Button
                                type="primary"
                                icon={<DownloadOutlined />}
                                disabled={formDataReportState?.formData?.record?.data.length === 0}
                                onClick={handleExport}
                            >
                                Download
                            </Button>

                        </Space>
                    </div>
                </>
                }
                expandable={{
                    expandedRowRender: (record) => (
                        <ReactAudioPlayer
                            src={process.env.REACT_APP_baseURL + '/api/download/' + record.recordingfile}
                            autoPlay={false}
                            key={record.id}
                            controls

                        />
                    ),

                    rowExpandable: (record) => record.recordingfile !== "",
                }}
                onChange={handlePagination}
            />

            {/* <Pagination
                showSizeChanger
                showQuickJumper
                // showTotal={(total, range) => {
                //     return `${range[0]}-${range[1]} of ${total} items`;
                // }}
                // onChange={onPaginationChange}
                // defaultCurrent={paginate.current}
                // pageSize={tableParams.pageSize}
                total={tableParams.total}
                style={{
                    display: "flex",
                    justifyContent: "end",
                    alignItems: "end",
                    marginTop: "1rem",
                }}
            /> */}
        </>
    )
}
