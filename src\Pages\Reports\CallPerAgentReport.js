import { useDispatch, useSelector } from "react-redux";
import { useEffect, useRef, useState } from "react";
import { getAgent } from "../../Actions/AgentActions";
import apiClient from "../../Shared/apiClient";

import { openNotificationWithIcon } from "../../Shared/notification";
import { Button, DatePicker, Form, Input, Modal, Select, Space, Spin, Table } from "antd";
import {
    CloudDownloadOutlined,
    DownloadOutlined,
    FilterOutlined,
    ReloadOutlined,
    SaveOutlined,
    SearchOutlined,
    SyncOutlined
} from "@ant-design/icons";
import { CSVLink } from "react-csv";
import { getCallPerAgentReport, resetPerAgent } from "../../Actions/CallPerAgentReportAction";
import Highlighter from "react-highlight-words";


export const CallPerAgentReport = () => {

    const callPerAgentReportReducer = useSelector(state => state.CallPerAgentReportReducer)
    const [showFilter, setShowFilter] = useState(false)
    const [resetFilter, setResetFilter] = useState(false)
    const [searchText, setSearchText] = useState("");
    const [searchedColumn, setSearchedColumn] = useState("");
    const dispatch = useDispatch()

    const agentState = useSelector((state) => state.AgentReducer);

    // useEffect(() => {
    // dispatch(getAgent())
    // dispatch(getCallPerAgentReport(''))
    // }, [])


    useEffect(() => {
        if (callPerAgentReportReducer.errMess !== '') openNotificationWithIcon('error', callPerAgentReportReducer.errMess)
    }, [callPerAgentReportReducer.errMess])

    const resetFormFilter = () => {
        // dispatch(getCallPerAgentReport(''))
        dispatch(resetPerAgent())
        setResetFilter(true)
    }



    const searchInput = useRef();

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({
            setSelectedKeys,
            selectedKeys,
            confirm,
            clearFilters,
        }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={(e) =>
                        setSelectedKeys(e.target.value ? [e.target.value] : [])
                    }
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: "block" }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button
                        onClick={() => handleReset(clearFilters)}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0]);
                            setSearchedColumn(dataIndex);
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined style={{ color: filtered ? "#1890ff" : undefined }} />
        ),
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex]
                    .toString()
                    .toLowerCase()
                    .includes(value.toLowerCase())
                : "",
        onFilterDropdownVisibleChange: (visible) => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: (text, record) => {
            return searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: "#ffc069", padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ""}
                />
            ) : (
                text
            )
        }
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0]);
        setSearchedColumn(dataIndex);
    };

    const handleReset = (clearFilters) => {
        clearFilters({ confirm: true });
        setSearchText("");
    };

    const columns = [
        {
            title: 'Agent ID',
            dataIndex: 'auth_username',
            key: 'auth_username',
            ...getColumnSearchProps('auth_username')
        },
        {
            title: 'Agent Name',
            dataIndex: 'username',
            key: 'username',
            ...getColumnSearchProps('username')
        },
        {
            title: 'Type',
            dataIndex: 'accountcode',
            key: 'accountcode',
            ...getColumnSearchProps('accountcode')
        },
        {
            title: 'Queue',
            dataIndex: 'queue',
            key: 'queue',
            ...getColumnSearchProps('queue')
        },
        {
            title: 'Call Answered',
            dataIndex: 'calls',
            key: 'calls',
            ...getColumnSearchProps('calls')
        },
        {
            title: 'Duration',
            dataIndex: 'dur',
            key: 'dur',
            ...getColumnSearchProps('dur')
        },
        {
            title: 'Avg',
            dataIndex: 'avgarage',
            key: 'avgarage',
            ...getColumnSearchProps('avgarage')
        },
        {
            title: 'Longest Call',
            dataIndex: 'longest_time',
            key: 'longest_time',
            ...getColumnSearchProps('longest_time')
        }
    ]

    const exportHeaders = [
        {
            label: 'Agent ID',
            key: 'auth_username',
        },
        {
            label: 'Agent Name',
            key: 'username'
        },
        {
            label: 'Type',
            key: 'accountcode'
        },
        {
            label: 'Queue',
            key: 'queue'
        },
        {
            label: 'Call Answered',
            key: 'calls'
        },
        {
            label: 'Duration',
            key: 'dur'
        },
        {
            label: 'Avg',
            key: 'avgarage',
        },
        {
            label: 'Longest Call',
            key: 'longest_time',
        }
    ]

    return (
        <>

            <Table
                title={data => <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    Call Per Agent (Inbound)
                    <Space>
                        <Button onClick={() => resetFormFilter()} type="danger" icon={<ReloadOutlined />}>Reset Filter</Button>
                        <Button onClick={() => setShowFilter(true)} icon={<FilterOutlined />}>Filter</Button>
                        <CSVLink headers={exportHeaders} data={callPerAgentReportReducer.data} filename="callPerAgent.csv">
                            <Button icon={<DownloadOutlined />} type="primary" disabled={callPerAgentReportReducer.data.length == 0}>Download</Button>
                        </CSVLink>
                    </Space>
                </div>}
                dataSource={callPerAgentReportReducer.data}
                columns={columns}
                scroll={{ x: 1100 }}
                bordered
                loading={{ spinning: callPerAgentReportReducer.isLoading, indicator: <SyncOutlined spin /> }}
            />
            <CallPerAgentFilter resetFilter={resetFilter} btnLoading={callPerAgentReportReducer.isLoading} visible={showFilter} setVisible={setShowFilter} />
        </>
    )
}

export const CallPerAgentFilter = ({ visible, setVisible, btnLoading, resetFilter }) => {

    const [form] = Form.useForm()
    const agentState = useSelector(state => state.AgentReducer)
    const dispatch = useDispatch()

    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data))
    }, [])

    const [queues, setQueues] = useState([])

    useEffect(() => dispatch(getAgent()), [])
    useEffect(() => form.resetFields(), [resetFilter])

    useEffect(() => {
        if (!visible) form.resetFields()
    }, [visible])

    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            okText="Submit"
            onOk={() => form.validateFields()
                .then(value => {
                    dispatch(getCallPerAgentReport(value))
                    setVisible(false)
                })
            }
            okButtonProps={{
                loading: btnLoading,
                icon: <SaveOutlined />
            }}
            title="Call detail report filter"
        >
            <Spin spinning={agentState.isLoading} indicator={<SyncOutlined spin />}>
                <Form
                    form={form}
                    layout="vertical"
                >
                    <Form.Item name="range" label="Date range">
                        <DatePicker.RangePicker style={{ width: '100%' }} />
                    </Form.Item>
                    <Form.Item name="agents" label="Agents">
                        <Select mode="multiple"
                            filterOption={(input, option) =>
                                option.children.toLowerCase().includes(input.toLowerCase())
                            }
                            placeholder="Select agents">
                            {agentState.users && agentState.users.map((value, index) => <Select.Option key={value.id} value={value.name}>{value.name}</Select.Option>)}
                        </Select>
                    </Form.Item>
                    <Form.Item colon={false} name="queue" label="Queue" >
                        <Select placeholder="Select Queue" >
                            {queues && queues.map((queue, index) => <Select.Option key={index} value={queue.name}>
                                {queue.name}
                            </Select.Option>)}
                        </Select>
                    </Form.Item>
                </Form>
            </Spin>
        </Modal>
    )
}