import * as ActionTypes from "../Constants/AgentRoutesConstant"
import apiClient from "../Shared/apiClient";
import { AGENT } from "../Endpoints/AgentRoutes";
import { logoutUser } from "./UserActions";
import { handleError } from "../Shared/handleError";
import { openNotificationWithIcon } from "../Shared/notification";

export const getAgent = (param) => dispatch => {
    dispatch(agentLoading())
    let route = param ? AGENT + param : AGENT;
    apiClient.get(route).then(response => dispatch(agentSuccess(response.data))).catch(e => dispatch(agentFailed(handleError(e))))
}

export const createAgent = data => dispatch => {
    dispatch(agentLoading())
    apiClient.post(AGENT, data).then((res) => {
        dispatch(agentCreateSuccess(res.data))
    }).catch((err) => {
        if (err.response) {
            let keys = Object.keys(err.response?.data?.errors)
            let errors = err.response?.data?.errors
            for (let i = 0; i < keys.length; i++) {
                openNotificationWithIcon('error', errors[keys[i]])
            }
        }
        dispatch(agentFailed(err.response?.data))
    })

    //     apiClient.post(AGENT, data).then(response => dispatch(agentCreateSuccess(response.data)))
    //         .catch(error => {
    //             if (error.response) {
    //                 dispatch(agentFailed(error.response))
    //                 handleError(error.response)
    //             }
    //         })
}

export const updateAgent = (id, data) => dispatch => {
    dispatch(agentLoading())
    apiClient.put(`${AGENT}/${id}`, data).then(response => dispatch(agentUpdateSuccess(response.data)))
        .catch(error => {
            if (error.response) {
                if (error.response.status === 401)
                    dispatch(logoutUser())
                dispatch(agentFailed(error.response))
            }
            else
                dispatch(agentFailed(error.message))
        })

}

export const deleteAgent = id => dispatch => {
    dispatch(agentLoading())
    apiClient.delete(`${AGENT}/${id}`)
        .then(response => dispatch(agentDeleteSuccess(response.data)))
        .catch(error => {
            if (error.response) {
                if (error.response.status === 401)
                    dispatch(logoutUser())
                dispatch(agentFailed(error.response))
            }
            else
                dispatch(agentFailed(error.message))
        })

}

const agentLoading = () => ({
    type: ActionTypes.AGENT_LOADING
})

const agentSuccess = agent => ({
    type: ActionTypes.AGENT_SUCCESS,
    payload: agent
})

const agentFailed = error => ({
    type: ActionTypes.AGENT_FAILED,
    payload: error
})

const agentCreateSuccess = message => ({
    type: ActionTypes.AGENT_CREATE_SUCCESS,
    payload: message
})

const agentUpdateSuccess = message => ({
    type: ActionTypes.AGENT_UPDATE_SUCCESS,
    payload: message
})

const agentDeleteSuccess = message => ({
    type: ActionTypes.AGENT_DELETE_SUCCESS,
    payload: message
})