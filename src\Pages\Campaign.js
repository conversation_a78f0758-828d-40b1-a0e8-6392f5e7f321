import React, { useEffect, useState } from "react";
import {
    Form,
    Input,
    InputNumber,
    Button,
    Row,
    Col,
    Typography,
    List,
    Card,
    Modal,
    DatePicker,
    notification,
    Table,
    Space,
    Select,
    Tag,
} from "antd";
import {
    DeleteOutlined,
    EditFilled,
    EditOutlined,
    EyeOutlined,
    InteractionOutlined,
    PlusCircleOutlined,
    UploadOutlined,
    UsergroupAddOutlined,
} from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import {
    deleteCampaign,
    getCampaigns,
    patchCampaign,
    postCampaign,
} from "../Actions/CampaignActions";
import { openNotificationWithIcon } from "../Shared/notification";
import AddCampaign from "../Components/Campaign/AddCampaign";
import EditCampaign from "../Components/Campaign/EditCampaign";
import UploadNumbers from "../Components/Campaign/UploadNumbers";
import {
    getCampaignNumbers,
    postCampaignNumbers,
} from "../Actions/CampaignNumberActions";
import CampaignNumbers from "../Components/Campaign/CampaignNumbers";
import { useHistory, useLocation } from "react-router-dom";
import ShowCampaignAgents from "../Components/Campaign/ShowCampaignAgents";
import apiClient from "../Shared/apiClient";

const background = {
    background: "#fff",
    borderRadius: "2px",
    padding: 10,
};

const layout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
};

const Campaign = (props) => {
    const [showAddCampaign, setShowAddCampaign] = useState(false);
    const [showEditCampaign, setShowEditCampaign] = useState(false);
    const [waitTimeBeforeCall, setWaitTimeBeforeCall] = useState(false);
    const [showUploadCampaign, setShowUploadCampaign] = useState(false);
    const [showAddUser, setShowAddUser] = useState(false);
    const [fetchWaitTimeData, setFetchWaitTimeData] = useState(false);
    const dispatch = useDispatch();
    const location = useLocation();

    // Renamed the variable from Campaign to campaignsState to avoid naming conflict.
    const campaignsState = useSelector((state) => state.CampaignReducer);
    const campaignNumbersState = useSelector((state) => state.CampaignNumberReducer);
    const [data, setData] = useState([]);
    const [item, setItem] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    let history = useHistory();

    // Check if we're on a specific campaign page
    const isCampaignDetailPage = location.pathname.includes('/campaign/monitor/') ||
        location.pathname.includes('/campaign/campaignNumber/');

    // Extract campaign ID from URL if we're on a campaign detail page
    const getCampaignIdFromUrl = () => {
        const pathSegments = location.pathname.split('/');
        return pathSegments[pathSegments.length - 1];
    };

    useEffect(() => {
        // Always fetch campaigns data on component mount and when dispatch changes
        dispatch(getCampaigns());

        // If we're on a campaign detail page, ensure we have the specific campaign data
        if (isCampaignDetailPage) {
            const campaignId = getCampaignIdFromUrl();
            // Optional: Fetch specific campaign data if needed
            // This might be necessary if your application requires detailed data
            // that isn't included in the general campaigns list
            // Example: dispatch(getCampaignDetail(campaignId));
        }
    }, [dispatch, location.pathname]);

    useEffect(() => {
        const fetchWaiTimebeforeCall = async () => {
            try {
                const waitTimebeforeCallData = await apiClient.get("/api/campaignSetting");
                setFetchWaitTimeData(waitTimebeforeCallData.data);
            } catch (error) {
                console.error("Error fetching wait time data:", error);
                openNotificationWithIcon("error", "Failed to fetch wait time settings");
            } finally {
                setIsLoading(false);
            }
        };
        fetchWaiTimebeforeCall();
    }, [waitTimeBeforeCall]);

    useEffect(() => {
        if (campaignsState.campaigns) {
            setData(campaignsState.campaigns);
            setIsLoading(false);
        }
    }, [campaignsState.campaigns]);

    useEffect(() => {
        if (campaignsState.errMess && campaignsState.errMess !== "") {
            console.log(campaignsState.errMess);
            openNotificationWithIcon("error", campaignsState.errMess.message);
            if (campaignsState.errMess.errors) {
                const elemName = campaignsState.errMess.errors.name.map((value, index) => (
                    <li key={index}>{value}</li>
                ));
                openNotificationWithIcon("error", <ol>{elemName}</ol>);
            }
        }
    }, [campaignsState.errMess]);

    useEffect(() => {
        if (campaignsState.message && campaignsState.message !== "") {
            openNotificationWithIcon("success", campaignsState.message);
        }
    }, [campaignsState.message]);

    useEffect(() => {
        if (campaignNumbersState.message && campaignNumbersState.message !== "") {
            openNotificationWithIcon("success", campaignNumbersState.message);
        }
    }, [campaignNumbersState.message]);

    const handleEdit = (item) => {
        // Convert the status to a boolean value: active if 1 or "1", inactive otherwise.
        setItem({ ...item, status: item.status === 1 || item.status === "1" });
        setShowEditCampaign(true);
    };

    // const handleEdit = (item) => {
    //     setItem(item);
    //     setShowEditCampaign(true);
    // };

    const handleDelete = (item) => {
        dispatch(deleteCampaign(item));
    };

    const handleUpload = (item) => {
        setItem(item);
        setShowUploadCampaign(true);
    };

    const handleActivate = (item) => {
        dispatch(patchCampaign({ id: item.id, status: !item.status }));
    };

    const handleAddUser = (item) => {
        setItem(item);
        setShowAddUser(true);
    };

    const handleMonitor = (item) => {
        history.push(`/campaign/monitor/${item.id}`);
    };

    const onCancel = () => {
        setShowAddCampaign(false);
        setShowEditCampaign(false);
    };

    const onCreate = (values) => {
        setShowAddCampaign(false);
        console.log(values);
        dispatch(postCampaign(values));
    };

    const onUpdate = (values) => {
        setShowEditCampaign(false);
        console.log(values);
        dispatch(patchCampaign(values));
    };

    const onUploadCancel = () => {
        setShowUploadCampaign(false);
        setItem(false);
    };

    const onUploadOk = (values) => {
        console.log("1002", values);
        let formData = new FormData();
        values.file.forEach((f) => formData.append("files[]", f));
        dispatch(postCampaignNumbers(item.id, formData));
        setShowUploadCampaign(false);
    };

    const onAddUserOk = () => {
        setShowAddUser(false);
        setItem(false);
    };

    const onAddUserCancel = () => {
        setShowAddUser(false);
        setItem(false);
    };

    const showAddUserProps = {
        item,
        showAddUser,
        onAddUserOk,
        onAddUserCancel,
    };

    const itemProps = {
        item,
        showAddCampaign,
        showEditCampaign,
        onCancel,
        onCreate,
        onUpdate,
    };

    const waitTimeBeforeCallProps = {
        waitTimeBeforeCall,
        setWaitTimeBeforeCall,
        fetchWaitTimeData,
    };

    const uploadProps = {
        item,
        showUploadCampaign,
        setShowUploadCampaign,
        onUploadCancel,
        onUploadOk,
    };

    const columns = [
        {
            title: "Name",
            dataIndex: "name",
            key: "name",
            render: (text, item) => (
                <Button onClick={() => history.push(`/campaign/campaignNumber/${item.id}`)} type="link">
                    {text}
                </Button>
            ),
        },
        {
            title: "Agents",
            key: "agents",
            render: (text, item) => (
                <Button onClick={() => handleAddUser(item)} icon={<UsergroupAddOutlined />} />
            ),
        },
        {
            title: "Start",
            dataIndex: "start_time",
            key: "start_time",
        },
        {
            title: "End",
            dataIndex: "end_time",
            key: "end_time",
        },
        {
            title: "File",
            dataIndex: "path",
            key: "path",
        },
        {
            title: "Status",
            dataIndex: "status",
            key: "status",
            render: (text) =>
                text === 1 || text === "1" ? (
                    <Tag color="#87d068">Active</Tag>
                ) : (
                    <Tag color="#f50">Inactive</Tag>
                ),
        },
        {
            title: "Actions",
            key: "action",
            render: (text, item) => (
                <Space size="middle">
                    <Button icon={<EyeOutlined />} title="Monitor" onClick={() => handleMonitor(item)} />
                    <Button
                        title={item.status === "1" || item.status === 1 ? "Deactivate" : "Activate"}
                        onClick={() => handleActivate(item)}
                        danger={item.status === "1" || item.status === 1}
                        icon={<InteractionOutlined />}
                    />
                    <Button title="Upload Numbers" onClick={() => handleUpload(item)} icon={<UploadOutlined />} />
                    <Button title="Edit" onClick={() => handleEdit(item)} icon={<EditFilled />} type="primary" />
                    <Button title="Delete" onClick={() => handleDelete(item)} icon={<DeleteOutlined />} />
                </Space>
            ),
        },
    ];

    const WaitTimeBeforeCallModal = ({ waitTimeBeforeCall, setWaitTimeBeforeCall, fetchWaitTimeData }) => {
        const [selectedValue, setSelectedValue] = useState(fetchWaitTimeData?.wait_seconds_during_calls);
        const options = [];
        for (let i = 5; i <= 60; i++) {
            options.push({
                value: i,
                label: `${i} seconds`,
            });
        }

        const handleChange = (value) => {
            setSelectedValue(value);
        };

        return (
            <Modal
                visible={waitTimeBeforeCall}
                title={"Wait Time Before Call"}
                okText="Submit"
                cancelText="Cancel"
                onCancel={() => {
                    setWaitTimeBeforeCall(false);
                }}
                onOk={() => {
                    apiClient
                        .put(`/api/campaignSetting/${fetchWaitTimeData.id}`, {
                            wait_seconds_during_calls: selectedValue,
                        })
                        .then((r) => {
                            if (r.data) {
                                openNotificationWithIcon("success", r?.data);
                            }
                            setWaitTimeBeforeCall(false);
                            setSelectedValue(fetchWaitTimeData?.wait_seconds_during_calls);
                        })
                        .catch((error) => {
                            openNotificationWithIcon("error", error?.response?.data?.message);
                        });
                }}
            >
                <Select
                    defaultValue={selectedValue}
                    onChange={handleChange}
                    style={{ width: 200 }}
                    options={options}
                />
            </Modal>
        );
    };

    // Display loading state while data is being fetched
    if (isLoading && isCampaignDetailPage) {
        return (
            <Card style={background}>
                <div style={{ textAlign: 'center', padding: '50px' }}>
                    Loading campaign data...
                </div>
            </Card>
        );
    }

    return (
        <Card
            title="Campaigns"
            style={background}
            extra={
                <Space>
                    <Button onClick={() => setWaitTimeBeforeCall(true)} icon={<EditOutlined />}>
                        Wait Time Before Call
                    </Button>
                    <Button onClick={() => setShowAddCampaign(true)} icon={<PlusCircleOutlined />}>
                        Add Campaign
                    </Button>
                </Space>
            }
        >
            <AddCampaign {...itemProps} />
            <EditCampaign {...itemProps} />
            <UploadNumbers {...uploadProps} />
            <ShowCampaignAgents {...showAddUserProps} />
            <WaitTimeBeforeCallModal {...waitTimeBeforeCallProps} />
            <Table loading={campaignsState.isLoading || isLoading} columns={columns} dataSource={data} scroll={{ x: 1100 }} />
        </Card>
    );
};

export default Campaign;

// import React, { useEffect, useState } from "react";
// import {
//     Form,
//     Input,
//     InputNumber,
//     Button,
//     Row,
//     Col,
//     Typography,
//     List,
//     Card,
//     Modal,
//     DatePicker,
//     notification,
//     Table,
//     Space,
//     Select,
//     Tag,
// } from "antd";
// import {
//     DeleteOutlined,
//     EditFilled,
//     EditOutlined,
//     EyeOutlined,
//     InteractionOutlined,
//     PlusCircleOutlined,
//     UploadOutlined,
//     UsergroupAddOutlined,
// } from "@ant-design/icons";
// import { useDispatch, useSelector } from "react-redux";
// import {
//     deleteCampaign,
//     getCampaigns,
//     patchCampaign,
//     postCampaign,
// } from "../Actions/CampaignActions";
// import { openNotificationWithIcon } from "../Shared/notification";
// import AddCampaign from "../Components/Campaign/AddCampaign";
// import EditCampaign from "../Components/Campaign/EditCampaign";
// import UploadNumbers from "../Components/Campaign/UploadNumbers";
// import {
//     getCampaignNumbers,
//     postCampaignNumbers,
// } from "../Actions/CampaignNumberActions";
// import CampaignNumbers from "../Components/Campaign/CampaignNumbers";
// import { useHistory } from "react-router-dom";
// import ShowCampaignAgents from "../Components/Campaign/ShowCampaignAgents";
// import apiClient from "../Shared/apiClient";

// const background = {
//     background: "#fff",
//     borderRadius: "2px",
//     padding: 10,
// };

// const layout = {
//     labelCol: { span: 8 },
//     wrapperCol: { span: 16 },
// };

// const Campaign = (props) => {
//     const [showAddCampaign, setShowAddCampaign] = useState(false);
//     const [showEditCampaign, setShowEditCampaign] = useState(false);
//     const [waitTimeBeforeCall, setWaitTimeBeforeCall] = useState(false);
//     const [showUploadCampaign, setShowUploadCampaign] = useState(false);
//     const [showAddUser, setShowAddUser] = useState(false);
//     const [fetchWaitTimeData, setFetchWaitTimeData] = useState(false);
//     const dispatch = useDispatch();

//     // Renamed the variable from Campaign to campaignsState to avoid naming conflict.
//     const campaignsState = useSelector((state) => state.CampaignReducer);
//     const campaignNumbersState = useSelector((state) => state.CampaignNumberReducer);
//     const [data, setData] = useState([]);
//     const [item, setItem] = useState(false);

//     let history = useHistory();

//     useEffect(() => {
//         dispatch(getCampaigns());
//     }, [dispatch]);

//     useEffect(() => {
//         const fetchWaiTimebeforeCall = async () => {
//             const waitTimebeforeCallData = await apiClient.get("/api/campaignSetting");
//             setFetchWaitTimeData(waitTimebeforeCallData.data);
//         };
//         fetchWaiTimebeforeCall();
//     }, [waitTimeBeforeCall]);

//     useEffect(() => {
//         if (campaignsState.campaigns) {
//             setData(campaignsState.campaigns);
//         }
//     }, [campaignsState.campaigns]);

//     useEffect(() => {
//         if (campaignsState.errMess && campaignsState.errMess !== "") {
//             console.log(campaignsState.errMess);
//             openNotificationWithIcon("error", campaignsState.errMess.message);
//             if (campaignsState.errMess.errors) {
//                 const elemName = campaignsState.errMess.errors.name.map((value, index) => (
//                     <li key={index}>{value}</li>
//                 ));
//                 openNotificationWithIcon("error", <ol>{elemName}</ol>);
//             }
//         }
//     }, [campaignsState.errMess]);

//     useEffect(() => {
//         if (campaignsState.message && campaignsState.message !== "") {
//             openNotificationWithIcon("success", campaignsState.message);
//         }
//     }, [campaignsState.message]);

//     useEffect(() => {
//         if (campaignNumbersState.message && campaignNumbersState.message !== "") {
//             openNotificationWithIcon("success", campaignNumbersState.message);
//         }
//     }, [campaignNumbersState.message]);

//     const handleEdit = (item) => {
//         setItem(item);
//         setShowEditCampaign(true);
//     };

//     const handleDelete = (item) => {
//         dispatch(deleteCampaign(item));
//     };

//     const handleUpload = (item) => {
//         setItem(item);
//         setShowUploadCampaign(true);
//     };

//     const handleActivate = (item) => {
//         dispatch(patchCampaign({ id: item.id, status: !item.status }));
//     };

//     const handleAddUser = (item) => {
//         setItem(item);
//         setShowAddUser(true);
//     };

//     const handleMonitor = (item) => {
//         // FIX: Added quotes around the path string
//         history.push(`/campaign/monitor/${item.id}`);
//     };

//     const onCancel = () => {
//         setShowAddCampaign(false);
//         setShowEditCampaign(false);
//     };

//     const onCreate = (values) => {
//         setShowAddCampaign(false);
//         console.log(values);
//         dispatch(postCampaign(values));
//     };

//     const onUpdate = (values) => {
//         setShowEditCampaign(false);
//         console.log(values);
//         dispatch(patchCampaign(values));
//     };

//     const onUploadCancel = () => {
//         setShowUploadCampaign(false);
//         setItem(false);
//     };

//     const onUploadOk = (values) => {
//         console.log("1002", values);
//         let formData = new FormData();
//         values.file.forEach((f) => formData.append("files[]", f));
//         dispatch(postCampaignNumbers(item.id, formData));
//         setShowUploadCampaign(false);
//     };

//     const onAddUserOk = () => {
//         setShowAddUser(false);
//         setItem(false);
//     };

//     const onAddUserCancel = () => {
//         setShowAddUser(false);
//         setItem(false);
//     };

//     const showAddUserProps = {
//         item,
//         showAddUser,
//         onAddUserOk,
//         onAddUserCancel,
//     };

//     const itemProps = {
//         item,
//         showAddCampaign,
//         showEditCampaign,
//         onCancel,
//         onCreate,
//         onUpdate,
//     };

//     const waitTimeBeforeCallProps = {
//         waitTimeBeforeCall,
//         setWaitTimeBeforeCall,
//         fetchWaitTimeData,
//     };

//     const uploadProps = {
//         item,
//         showUploadCampaign,
//         setShowUploadCampaign,
//         onUploadCancel,
//         onUploadOk,
//     };

//     const columns = [
//         {
//             title: "Name",
//             dataIndex: "name",
//             key: "name",
//             render: (text, item) => (
//                 // FIX: Added quotes around the path string
//                 <Button onClick={() => history.push(`/campaign/campaignNumber/${item.id}`)} type="link">
//                     {text}
//                 </Button>
//             ),
//         },
//         {
//             title: "Agents",
//             key: "agents",
//             render: (text, item) => (
//                 <Button onClick={() => handleAddUser(item)} icon={<UsergroupAddOutlined />} />
//             ),
//         },
//         {
//             title: "Start",
//             dataIndex: "start_time",
//             key: "start_time",
//         },
//         {
//             title: "End",
//             dataIndex: "end_time",
//             key: "end_time",
//         },
//         {
//             title: "File",
//             dataIndex: "path",
//             key: "path",
//         },
//         {
//             title: "Status",
//             dataIndex: "status",
//             key: "status",
//             render: (text) =>
//                 text === 1 || text === "1" ? (
//                     <Tag color="#87d068">Active</Tag>
//                 ) : (
//                     <Tag color="#f50">Inactive</Tag>
//                 ),
//         },
//         {
//             title: "Actions",
//             key: "action",
//             render: (text, item) => (
//                 <Space size="middle">
//                     <Button icon={<EyeOutlined />} title="Monitor" onClick={() => handleMonitor(item)} />
//                     <Button
//                         title={item.status === "1" || item.status === 1 ? "Deactivate" : "Activate"}
//                         onClick={() => handleActivate(item)}
//                         danger={item.status === "1" || item.status === 1}
//                         icon={<InteractionOutlined />}
//                     />
//                     <Button title="Upload Numbers" onClick={() => handleUpload(item)} icon={<UploadOutlined />} />
//                     <Button title="Edit" onClick={() => handleEdit(item)} icon={<EditFilled />} type="primary" />
//                     <Button title="Delete" onClick={() => handleDelete(item)} icon={<DeleteOutlined />} />
//                 </Space>
//             ),
//         },
//     ];

//     const WaitTimeBeforeCallModal = ({ waitTimeBeforeCall, setWaitTimeBeforeCall, fetchWaitTimeData }) => {
//         const [selectedValue, setSelectedValue] = useState(fetchWaitTimeData?.wait_seconds_during_calls);
//         const options = [];
//         for (let i = 5; i <= 60; i++) {
//             options.push({
//                 value: i,
//                 label: `${i} seconds`,
//             });
//         }

//         const handleChange = (value) => {
//             setSelectedValue(value);
//         };

//         return (
//             <Modal
//                 visible={waitTimeBeforeCall}
//                 title={"Wait Time Before Call"}
//                 okText="Submit"
//                 cancelText="Cancel"
//                 onCancel={() => {
//                     setWaitTimeBeforeCall(false);
//                 }}
//                 onOk={() => {
//                     apiClient
//                         .put(`/api/campaignSetting/${fetchWaitTimeData.id}`, {
//                             wait_seconds_during_calls: selectedValue,
//                         })
//                         .then((r) => {
//                             if (r.data) {
//                                 openNotificationWithIcon("success", r?.data);
//                             }
//                             setWaitTimeBeforeCall(false);
//                             setSelectedValue(fetchWaitTimeData?.wait_seconds_during_calls);
//                         })
//                         .catch((error) => {
//                             openNotificationWithIcon("error", error?.response?.data?.message);
//                         });
//                 }}
//             >
//                 <Select
//                     defaultValue={selectedValue}
//                     onChange={handleChange}
//                     style={{ width: 200 }}
//                     options={options}
//                 />
//             </Modal>
//         );
//     };

//     return (
//         <Card
//             title="Campaigns"
//             style={background}
//             extra={
//                 <Space>
//                     <Button onClick={() => setWaitTimeBeforeCall(true)} icon={<EditOutlined />}>
//                         Wait Time Before Call
//                     </Button>
//                     <Button onClick={() => setShowAddCampaign(true)} icon={<PlusCircleOutlined />}>
//                         Add Campaign
//                     </Button>
//                 </Space>
//             }
//         >
//             <AddCampaign {...itemProps} />
//             <EditCampaign {...itemProps} />
//             <UploadNumbers {...uploadProps} />
//             <ShowCampaignAgents {...showAddUserProps} />
//             <WaitTimeBeforeCallModal {...waitTimeBeforeCallProps} />
//             <Table loading={campaignsState.isLoading} columns={columns} dataSource={data} scroll={{ x: 1100 }} />
//         </Card>
//     );
// };

// export default Campaign;