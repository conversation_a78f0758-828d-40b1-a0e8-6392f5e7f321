import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";

export const getBreakReport = data => dispatch => {
    dispatch(breakReportLoading())
    apiClient.post(`/api/report/break-report-data`, data).then((r) => {
        dispatch(breakReportSuccess(r?.data))
        console.log('reset data', r?.data)
    }).catch(e => dispatch(breakReportFailed(handleError(e)))).catch(e => dispatch(breakReportFailed(handleError(e))))
}

export const getColumn = () => dispatch => {
    // dispatch(breakReportLoading())
    apiClient.post(`/api/report/get-all-breaks`).then(r => dispatch(breakReportColumn(r?.data))).catch(e => dispatch(breakReportFailed(handleError(e))))
}

export const getBreakReportFiltered = data => dispatch => {
    dispatch(breakReportLoading())
    apiClient.post(`/api/report/hold-time-report-filtered`, data).then(r => dispatch(breakReportSuccess(r.data))).catch(e => dispatch(breakReportFailed(handleError(e)))).catch(e => dispatch(breakReportFailed(handleError(e))))
}
export const breakReportReset = () => dispatch => {

    dispatch(breakReset());

}

const breakReportSuccess = data => ({
    type: "BREAK_REPORT_SUCCESS",
    payload: data
})

const breakReportColumn = col => ({
    type: "BREAK_REPORT_COLUMN",
    payload: col
})

const breakReportFailed = err => ({
    type: "BREAK_REPORT_FAILED",
    payload: err
})

const breakReportLoading = () => ({
    type: "BREAK_REPORT_LOADING"
})
const breakReset = () => ({
    type: "BREAK_RESET"
})