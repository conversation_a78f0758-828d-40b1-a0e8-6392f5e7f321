const initialState = {
    data: null,
    isLoading: false,
    errMess: ''
}

export const RecordingReportReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case "RECORDING_REPORT_LOADING":
            return {...state, isLoading: true}
        case "RECORDING_REPORT_SUCCESS":
            return {...state, isLoading: false, data: action.payload, errMess: ''}
        case "RECORDING_REPORT_FAILED":
            return {...state, isLoading: false, errMess: action.payload}
    }
}