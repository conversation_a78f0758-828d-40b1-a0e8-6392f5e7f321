import * as ActionTypes from "../Constants/CallDetailReportConstant"

const initialState = {
    cdr: [],
    data: [],
    errMess: null,
    isLoading: false,
    statusCode: 0,
    message: '',
    pagination : {}
}

export const CdrReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.REPORT_LOADING:
            return {...state, isLoading: true}
        case ActionTypes.GET_REPORT:
            return {...state, isLoading: false, errMess: '', pagination: {total: action.payload.paginateReport.total}, cdr: action.payload.paginateReport.data, data: action.payload.data}
        case ActionTypes.REPORT_FAILED:
            return {...state, isLoading: false, errMess: action.payload}
        case ActionTypes.GET_ALL_REPORT:
            return {...state, isLoading: false, errMess: '', pagination: {total: action.payload.paginateReport.total}, cdr: action.payload.paginateReport.data, data: action.payload.data}
        case ActionTypes.REPORT_SUCCESS:
            return {...state, isLoading: false, errMess: '', data: action.payload}
    }
}