import * as ActionTypes from "../Constants/UsersConstants"

const initialState = {
    users: [],
    errMess: null,
    isLoading: false,
    message: '',
    getAll: []
}

export const UsersReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.USERS_LOADING:
            return {...state, isLoading: true}
        case ActionTypes.USERS_SUCCESS:
            return {...state, isLoading: false, users: action.payload, errMess: null, message: ''}
        case ActionTypes.GET_ALL_USERS:
            return {...state, isLoading: false, getAll: action.payload, errMess: null, message: ''}
        case ActionTypes.USERS_FAILED:
            return {...state, isLoading: false, errMess: action.payload, message: ''}
        case ActionTypes.USER_SUCCESS:
            return {...state, isLoading: false, message: action.payload, errMess: null}
    }
}