import { useState } from "react";
import { <PERSON><PERSON>, Space, Table } from "antd";
import { DownloadOutlined, FilterOutlined, RedoOutlined } from "@ant-design/icons";
import apiClient from "../../Shared/apiClient";
import moment from "moment";
import SummaryReportFilter from "../../Components/Reports/SummaryReportFilter";
const columns = [
    {
        title: "Date",
        dataIndex: "date",
        key: "date",
        width: 150,
    },
    {
        title: "Total Calls",
        dataIndex: "totalCalls",
        key: "totalCalls",
        width: 150,
    },
    {
        title: "Answered Calls",
        dataIndex: "answerdCalls",
        key: "answerdCalls",
        width: 150,
    },
    {
        title: "Abandoned Calls",
        dataIndex: "abandonCalls",
        key: "abandonCalls",
        width: 150,
    },
    {
        title: "Avg Talk Time",
        dataIndex: "averageTalkTime",
        key: "averageTalkTime",
        width: 150,
    },
    {
        title: "Service Level",
        dataIndex: "SLA",
        key: "SLA",
        width: 150,
    },
];




const SummaryReport = () => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [filter, setFilter] = useState(false);
    const [filterValues, setFilterValues] = useState(null);

    const onFilter = (values) => {
        setFilterValues(values);
        setLoading(true);

        const fromDate = values.dateRange ? moment(values.dateRange[0]).format('YYYY-MM-DD') : null;
        const toDate = values.dateRange ? moment(values.dateRange[1]).format('YYYY-MM-DD') : null;

        apiClient
            .post(`/api/report/summaryReport`, {
                from: fromDate,
                to: toDate,
                queue: values.queue || ''
            })
            .then((resp) => {
                setData(resp.data);
                setLoading(false);
            })
            .catch((err) => {
                setLoading(false);
                console.error('Error fetching report:', err);
            });

        setFilter(false);
    };

    const handleReset = () => {
        setData([]);
        setFilterValues(null);
    };

    const handleExport = () => {
        if (!filterValues) return;

        const fromDate = filterValues.dateRange ? moment(filterValues.dateRange[0]).format('YYYY-MM-DD') : null;
        const toDate = filterValues.dateRange ? moment(filterValues.dateRange[1]).format('YYYY-MM-DD') : null;

        apiClient
            .post(`/api/report/exportSummaryReport`, {
                from: fromDate,
                to: toDate,
                queue: filterValues.queue || '',
            }, { responseType: 'blob' })
            .then((response) => {
                const type = response.headers['content-type'];
                const blob = new Blob([response.data], { type: type });
                const link = document.createElement('a');
                link.href = window.URL.createObjectURL(blob);
                link.download = 'export-summary-report.csv';
                link.click();
            })
            .catch((err) => {
                console.error('Error exporting report:', err);
            });
    };

    return (
        <>
            <SummaryReportFilter
                visible={filter}
                setVisible={setFilter}
                onCreate={onFilter}
                isLoading={loading}
            />

            <Table
                scroll={{ x: 800 }}
                title={() => (
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        Summary Report
                        <Space>
                            <Button
                                onClick={handleReset}
                                type="danger"
                                icon={<RedoOutlined />}
                                disabled={data.length === 0}
                            >
                                Reset Filter
                            </Button>
                            <Button
                                onClick={() => setFilter(true)}
                                icon={<FilterOutlined />}
                            >
                                Filter
                            </Button>
                            <Button
                                disabled={data.length === 0}
                                onClick={handleExport}
                                type="primary"
                                icon={<DownloadOutlined />}
                            >
                                Download
                            </Button>
                        </Space>
                    </div>
                )}
                bordered
                dataSource={data}
                pagination={{ pageSize: 10 }}
                loading={loading}
                columns={columns}
                rowKey={(_, index) => index}
            />
        </>
    );
};

export default SummaryReport;
