import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, DatePicker, Form, Select, Table } from "antd";
import { ClearOutlined, DownloadOutlined, FilterOutlined, ReloadOutlined } from "@ant-design/icons";
import apiClient from "../../Shared/apiClient";
import moment from "moment";
import { CSVLink } from "react-csv";
const { RangePicker } = DatePicker;
const dateFormat = "YYYY-MM-DD";
const columns = [
    {
        title: "Date",
        dataIndex: "date",
        key: "date",
        width: 150,
    },
    {
        title: "Total Calls",
        dataIndex: "totalCalls",
        key: "totalCalls",
        width: 150,
    },
    {
        title: "Answered Calls",
        dataIndex: "answerdCalls",
        key: "answerdCalls",
        width: 150,
    },
    {
        title: "Abandoned Calls",
        dataIndex: "abandonCalls",
        key: "abandonCalls",
        width: 150,
    },
    {
        title: "Avg Talk Time",
        dataIndex: "averageTalkTime",
        key: "averageTalkTime",
        width: 150,
    },
    {
        title: "Service Level",
        dataIndex: "SLA",
        key: "SLA",
        width: 150,
    },
];

const exportHeaders = [
    {
        label: "Date",
        key: "date",
    },
    {
        label: "Total Calls",
        key: "totalCalls",
    },
    {
        label: "Answered Calls",
        key: "answerdCalls",
    },
    {
        label: "Abandoned Calls",
        key: "abandonCalls"
    },
    {
        label: "Avg Talk Time",
        key: "averageTalkTime",
    },
    {
        label: "Service Level",
        key: "SLA",
    },
];


const SummaryReport = () => {
    const [form] = Form.useForm();
    const [data, setData] = useState([]);
    const [to, setTo] = useState();
    const [from, setFrom] = useState();
    const [loading, setLoading] = useState(false);
    const [fetchReportCheck, setFetchReportCheck] = useState(true);
    const [exportReportCheck, setExportReportCheck] = useState(true);

    const onDateChange = (date, dateString) => {
        setFrom(moment(dateString[0]));
        setTo(moment(dateString[1]));
    };

    useEffect(() => {
        to && setFetchReportCheck(false);
    }, [to, fetchReportCheck]);

    const exportReportFunc = () => {
        setExportReportCheck(true);
        setFetchReportCheck(true);
        form.resetFields();

    };

    const fetchReport = () => {
        setLoading(true);
         const queue = form.getFieldValue("queue");
        apiClient
            .post(`/api/report/summaryReport`, {
                from: from?._i,
                to: to?._i,
                queue:queue || ''
            })
            .then((resp) => {
                setData(resp.data);
                setLoading(false);
                setExportReportCheck(false);
            })
            .catch((err) => {
                setLoading(false);
                console.log(err.message);
            });
    };
 const [queues, setQueues] = useState([])

    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data))
    }, [])
    const handleExport = () => {
        const queue = form.getFieldValue("queue");
        apiClient
            .post(`/api/report/exportSummaryReport`, {
                from: from?._i,
                to: to?._i,
                queue: queue || '',
            }, { responseType: 'blob' })
            .then((response) => {
                const type = response.headers['content-type'];
                const blob = new Blob([response.data], { type: type });
                const link = document.createElement('a');
                link.href = window.URL.createObjectURL(blob);
                link.download = 'export-summary-report.csv';
                link.click();
            })
            .catch((err) => {
                setLoading(false);
                console.log(err.message);
            });
    }

    return (
        <>
            <Card
                title="Summary Report"
                style={{ background: "#fff", borderRadius: "2px", padding: 10 }}
                extra={
                    <Form
                        form={form}
                        layout="inline"
                        size="large"
                        style={{ marginTop: "3px" }}
                    >
                        <Form.Item name={"picker"}>
                            <RangePicker
                                format={dateFormat}
                                onChange={onDateChange}
                                style={{ padding: "5px 20px", marginRight: "10px" }}
                            />
                        </Form.Item>
                        <Form.Item colon={false} name="queue" >
                            <Select placeholder="Select Queue" >
                                {queues && queues.map((queue, index) => <Select.Option key={index} value={queue.name}>
                                    {queue.name}
                                </Select.Option>)}
                            </Select>
                        </Form.Item>
                        <Form.Item>
                            <div style={{ display: 'flex', gap: '10px', justifyContent: 'space-between', alignItems: 'center' }}>


                                <Button
                                    size="medium"
                                    disabled={data && data?.length === 0}
                                    onClick={() => {
                                        form.resetFields()
                                        setData([])
                                        setExportReportCheck(false);
                                        setFetchReportCheck(false);
                                    }}
                                    type="primary"
                                    danger
                                    style={{ padding: "5px 20px", }}
                                    icon={<ReloadOutlined />}
                                >
                                    Reset Filter
                                </Button>

                                <Button
                                    size="medium"
                                    disabled={fetchReportCheck}
                                    type="primary"
                                    onClick={fetchReport}
                                    icon={<FilterOutlined />}
                                    style={{ padding: "5px 20px", }}
                                >
                                    Fetch Report
                                </Button>


                                <Button
                                    size="medium"
                                    disabled={data && data?.length === 0}
                                    onClick={handleExport}
                                    icon={<DownloadOutlined />}
                                >
                                    Export Report
                                </Button>
                            </div>
                        </Form.Item>

                    </Form>
                }
            >
                <Table
                    columns={columns}
                    dataSource={data}
                    pagination={false}
                    loading={loading}
                    bordered
                    size="default"
                    scroll={{
                        x: "calc(700px + 50%)",
                        y: 240,
                    }}
                />
            </Card>
        </>
    );
};

export default SummaryReport;
