import { But<PERSON>, Card, DatePicker, Form, Input, Spin, Table, Select, Space, Modal } from "antd";
import apiClient from "../../Shared/apiClient";
import React, { useState, useEffect, useRef } from "react";
import { format } from "date-fns";
import { CloudDownloadOutlined, DownloadOutlined, FilterOutlined, ReloadOutlined, SearchOutlined } from "@ant-design/icons";
import { CSVLink } from "react-csv"
import moment from "moment";
import Highlighter from "react-highlight-words";
// import { values } from "lodash";

export const AgentLoginReport = () => {

    const [loading, setLoading] = useState(false)
    const [date, setDate] = useState(new Date(Date.now()))
    const [data, setData] = useState([])
    const [queues, setQueues] = useState([])
    const [queue, setQueueOption] = useState("");
    const [filterVisible, setFilterVisible] = useState(false);
    const [searchText, setSearchText] = useState("");
    const [searchedColumn, setSearchedColumn] = useState("");


    const resetFormFilter = () => {


    }

    var handleChange = (queue) => {
        console.log(queue, 'queueoption');
        setQueueOption(queue);
    };
    // const handleSubmit = values => {
    //     setLoading(true)
    //     getAgentLoginData({ date, queue })
    //         .then(r => {
    //             setLoading(false)
    //             console.log(r.data)
    //             setData(r.data)
    //         })
    //         .catch(e => {
    //             setLoading(false)
    //             console.log(e)
    //         })
    // }

    useEffect(() => {
        console.log(new Date(Date.now()))
    }, [])
    // useEffect(() => {
    //     apiClient.get('api/queue').then((res) => setQueues(res.data))
    // }, [])

    const getAgentLoginData = data => apiClient.post(`/api/report/agent-login`, data)

    const searchInput = useRef();

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({
            setSelectedKeys,
            selectedKeys,
            confirm,
            clearFilters,
        }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={(e) =>
                        setSelectedKeys(e.target.value ? [e.target.value] : [])
                    }
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: "block" }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button
                        onClick={() => handleReset(clearFilters)}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0]);
                            setSearchedColumn(dataIndex);
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined style={{ color: filtered ? "#1890ff" : undefined }} />
        ),
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex]
                    .toString()
                    .toLowerCase()
                    .includes(value.toLowerCase())
                : "",
        onFilterDropdownVisibleChange: (visible) => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: (text) =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: "#ffc069", padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ""}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0]);
        setSearchedColumn(dataIndex);
    };

    const handleReset = (clearFilters) => {
        clearFilters({ confirm: true });
        setSearchText("");
    };

    const columns = [
        {
            title: 'Agent Name',
            dataIndex: 'agent',
            key: 'agent',
            ...getColumnSearchProps('agent')
        },
        // {
        //     title: 'Agent',
        //     dataIndex: 'agent',
        //     key: 'agent'
        // },
        {
            title: 'Duration (hh:mm:ss)',
            dataIndex: 'time_diff',
            key: 'time_diff'
        },
        {
            title: 'Queue Name',
            dataIndex: 'queuename',
            key: 'queuename',
            ...getColumnSearchProps('queuename')
        },
        {
            title: 'Login time',
            dataIndex: 'login_time',
            key: 'login_time'
        },
        {
            title: 'Logout time',
            dataIndex: 'logout_time',
            key: 'logout_time'
        }
    ]


    const exportHeaders = [
        { label: "Agent Name", key: "agent" },
        { label: 'Duration (hh:mm:ss)', key: 'time_diff' },
        { label: 'Login time', key: 'login_time' },
        { label: 'Logout time', key: 'logout_time' }
    ]

    return (
        <>

            {/* <Card style={{ marginBottom: 10 }} title="Daily Login Report" bordered={false} >
                <Form onFinish={handleSubmit} labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                    <Form.Item colon={false} name="date" label="Date" rules={[{ required: true }]}>
                        <DatePicker onChange={(date, dateString) => setDate(dateString)} format="YYYY-MM-DD" style={{ width: '50%' }} />
                    </Form.Item>
                    <Form.Item name={"queue"} label={"Queue"}>
                        <Select placeholder="Select Queue" onChange={handleChange} style={{ width: '50%' }}>
                            {queues && queues.map((queue, index) => <Select.Option key={index} value={queue.name}>
                                {queue.name}
                            </Select.Option>)}
                        </Select>
                    </Form.Item>
                    <Form.Item wrapperCol={{ span: 16, offset: 8 }}>
                        <Button type="primary" htmlType="submit">Submit</Button>
                    </Form.Item>
                </Form>
            </Card> */}

            <Spin spinning={loading}>
                <Table title={() =>
                    <>
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            Daily Login Report
                            <Space>
                                <Button
                                    danger
                                    type="primary"
                                    icon={<ReloadOutlined />}
                                    onClick={() => {
                                        setData([]);
                                    }}
                                >Reset Filter</Button>
                                <Button
                                    icon={<FilterOutlined />}
                                    onClick={() => {
                                        setFilterVisible(true);
                                    }} >Filter</Button>

                                <CSVLink filename="DailyLoginReport.csv"
                                    data={data}
                                    target="_blank" >
                                    <Button
                                        disabled={data.length == 0}
                                        type="primary"
                                        icon={<DownloadOutlined />}>
                                        Download CSV
                                    </Button>
                                </CSVLink>
                            </Space>
                        </div>
                    </>

                }
                    dataSource={data}
                    columns={columns}
                    scroll={{ x: 1100 }} />
            </Spin>

            <AgentLoginFilter visible={filterVisible} setVisible={setFilterVisible} setQueueOption={setQueueOption} queueOption={queue} loading={loading} setLoading={setLoading} data={data} setData={setData} date={date} setDate={setDate} queues={queues} setQueues={setQueues} />
        </>
    )
}

const AgentLoginFilter = ({ visible, setVisible, resetFilter, buttonLoading, setQueueOption, queue, setLoading, loading, data, setData, date, setDate, queues, setQueues }) => {

    const [form] = Form.useForm()
    const [pagination, setPagination] = useState({ current: 1, pageSize: 50 })
    // const dispatch = useDispatch();
    const getAgentLoginData = (data) => apiClient.post(`/api/report/agent-login`, data)
    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data))
    }, [])

    var handleChange = (queue) => {
        console.log(queue, 'queueoption');
        setQueueOption(queue);
    };


    function handleSubmit(values) {
        console.log("check", moment(values.date).format('YYYY-MM-DD'))
        const payload = {

            date: moment(values.date).format('YYYY-MM-DD'),
            queue: values.queue
        }
        setLoading(true)
        apiClient.post('/api/report/agent-login', payload)
            .then(r => {
                setLoading(false)
                console.log(r.data)
                setData(r.data)
            })
            .catch(e => {
                setLoading(false)
                console.log(e)
            })
    }


    useEffect(() => {
        if (resetFilter) {
            form.resetFields()
        }
    }, [resetFilter])


    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            title="Daily Login Filter"
            size="small"
            okText="Submit"
            onOk={() => {
                form.validateFields()
                    .then((values) => {
                        handleSubmit(values)
                        form.resetFields();
                    })

                    .catch(e => console.log(e))

                setVisible(false)

            }
            }
        // oKButtonProps={{
        //     loading: buttonLoading
        // }}
        >
            <Card style={{ marginBottom: 10 }} bordered={false} >
                <Form form={form} labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                    <Form.Item colon={false} name="date" label="Date" rules={[{ required: true }]}>
                        <DatePicker format="YYYY-MM-DD" style={{ width: '50%' }} />
                    </Form.Item>
                    <Form.Item name={"queue"} label={"Queue"}>
                        <Select placeholder="Select Queue" style={{ width: '50%' }}>
                            {queues && queues.map((queue, index) => <Select.Option key={index} value={queue.name}>
                                {queue.name}
                            </Select.Option>)}
                        </Select>
                    </Form.Item>
                    {/* <Form.Item wrapperCol={{ span: 16, offset: 8 }}>
                        <Button type="primary" htmlType="submit">Submit</Button>
                    </Form.Item> */}
                </Form>
            </Card>
        </Modal>
    )
}