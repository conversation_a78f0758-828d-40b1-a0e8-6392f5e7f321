import { useState, useEffect } from "react";
import {
    Button,
    Checkbox,
    DatePicker,
    Divider,
    Form,
    Input,
    Modal,
    Radio,
    Select,
    Space,
    Spin,
    Typography,
} from "antd";
import { useDispatch, useSelector } from "react-redux";
import { deleteFormField, getFormFields } from "../../Actions/FormFieldActions";
import { CheckOutlined, CloseOutlined, DeleteOutlined, DribbbleOutlined, EditOutlined } from "@ant-design/icons";
import apiClient from "../../Shared/apiClient";
import { openNotificationWithIcon } from "../../Shared/notification";

const { Text } = Typography;

export const FormFields = ({
    formFieldsVisible,
    setFormFieldsVisible,
    formId,
}) => {
    const dispatch = useDispatch();
    const formFieldState = useSelector((state) => state.FormFieldReducer);
    const [editableOptionId, setEditableOptionId] = useState(null)
    const [editableOptions, setEditableOptions] = useState([])

    useEffect(() => {
        if (formFieldsVisible) dispatch(getFormFields(formId));
    }, [formFieldsVisible]);

    useEffect(() => setEditableOptionId(null), [formFieldsVisible]);

    //   const renderField = (value) => {
    //     switch (value.type) {
    //       case "checkbox":
    //         return (
    //           <Checkbox.Group key={value.id}>
    //             {value.form_field_options &&
    //               value.form_field_options.map((value, index) => (
    //                 <Checkbox value={value.label} key={value.id}>
    //                   {value.label}
    //                 </Checkbox>
    //               ))}
    //           </Checkbox.Group>
    //         );
    //       case "radio":
    //         return (
    //           <Radio.Group key={value.id}>
    //             {value.form_field_options &&
    //               value.form_field_options.map((value, index) => (
    //                 <Radio value={value.label} key={value.id}>
    //                   {value.label}
    //                 </Radio>
    //               ))}
    //           </Radio.Group>
    //         );
    //       case "input":
    //         return <Input key={value.id} />;
    //       case "textarea":
    //         return <Input.TextArea key={value.id} />;
    //       case "select":
    //         return (
    //           <Select>
    //             {value.form_field_options &&
    //               value.form_field_options.map((value, index) => (
    //                 <Select.Option key={value.id}>{value.label}</Select.Option>
    //               ))}
    //           </Select>
    //         );
    //       case "date":
    //         return <DatePicker key={value.id} style={{ width: "100%" }} />;
    //     }
    //   };


    const handleUpdateOptions = (id) => {
        apiClient.post(`api/${id}/formFieldOptionUpdate`, { options: editableOptions }).then((res) => {
            if (res.data) {
                setEditableOptionId(null)
                setEditableOptions([])
                openNotificationWithIcon('success', res.data)
                dispatch(getFormFields(formId));
            }
        }).catch((err) => {
            openNotificationWithIcon('error', 'Network Errors')
        })
    }

    const renderField = (value) => {
        const { type, id, form_field_options } = value;

        switch (type) {
            case "checkbox":
                return (
                    <Checkbox.Group key={id}>
                        {form_field_options &&
                            form_field_options.map((option) => (
                                <Checkbox value={option.label} key={option.id}>
                                    {option.label}
                                </Checkbox>
                            ))}
                    </Checkbox.Group>
                );
            case "radio":
                return (
                    <Radio.Group key={id}>
                        {form_field_options &&
                            form_field_options.map((option) => (
                                <Radio value={option.label} key={option.id}>
                                    {option.label}
                                </Radio>
                            ))}
                    </Radio.Group>
                );
            case "input":
                return <Input key={id} />;
            case "textarea":
                return <Input.TextArea key={id} />;
            case "select":
                return (
                    <>
                        {editableOptionId === id ? <div>
                            <Select
                                showSearch
                                size="large"
                                style={{ width: "80%", marginRight: '10px' }}
                                mode="tags"
                                value={editableOptions}
                                allowClear
                                onChange={(val) => setEditableOptions(val)}
                                placeholder="Please add options"
                            />
                            <Space>
                                <Button
                                    type="primary"
                                    icon={<CheckOutlined />}
                                    title="Update"
                                    onClick={() => handleUpdateOptions(id)}
                                />
                                <Button onClick={() => {
                                    setEditableOptionId(null)
                                    setEditableOptions([])
                                }} icon={<CloseOutlined />} title="Cancel" />
                            </Space>
                        </div> :
                            <Select key={id}>
                                {form_field_options &&
                                    form_field_options.map((option) => (
                                        <Select.Option key={option.id}>{option.label}</Select.Option>
                                    ))}
                            </Select>}
                    </>
                );

            case "date":
                return <DatePicker key={id} style={{ width: "100%" }} />;
            default:
                return null;
        }
    };

    const RenderedLabel = (props) => (
        <Space split={<Divider type="vertical" />}>
            {props.label}
            {props.label === "Phone Number" ? null : (
                <Space split={<Divider />}>
                    {
                        editableOptionId !== props.id &&
                        <>
                            {props.options && <Button
                                icon={<EditOutlined />}
                                style={{ boxShadow: 'none', border: 'none' }}
                                onClick={() => {
                                    setEditableOptionId(props.id)
                                    props?.options && setEditableOptions(props?.options?.map(opt => opt.label))
                                }}
                            />}
                            <Button
                                danger
                                icon={<DeleteOutlined />}
                                style={{ boxShadow: 'none', border: 'none' }}
                                onClick={() => dispatch(deleteFormField(props.id, formId))}
                            />
                        </>
                    }
                </Space>
            )}
        </Space>
    );
    const handleOk = () => {
        setFormFieldsVisible(!formFieldsVisible)
    }
    return (

        <Modal
            visible={formFieldsVisible}
            onCancel={() => setFormFieldsVisible(false)}
            onOk={() => setFormFieldsVisible(false)}
            title="Form fields"
            footer={[

                <Button key="submit" type="primary" onClick={handleOk}>
                    Submit
                </Button>,

            ]}
        >
            <Spin
                indicator={<DribbbleOutlined />}
                spinning={formFieldState?.isLoading}
            >
                <Form layout="vertical">
                    {formFieldState?.fields?.form_fields &&
                        formFieldState.fields.form_fields.map((value, index) => (
                            <Form.Item
                                key={value.id}
                                label={<RenderedLabel
                                    id={value.id}
                                    label={value.label}
                                    options={value.type === 'select' && value?.form_field_options} />
                                }
                                required={value.required}
                            >
                                {renderField(value,)}
                            </Form.Item>
                        ))}
                </Form>
                {formFieldState?.fields?.form_function?.length > 0 && (
                    <Divider>Form Function</Divider>
                )}
                {formFieldState?.fields?.form_function?.length > 0 &&
                    formFieldState.fields.form_function.map((value, index) => (
                        <Text key={index}>
                            Function:{" "}
                            <Text strong style={{ paddingRight: "1rem" }}>
                                {value.function_type.function_name}
                            </Text>{" "}
                            Reference Field:{" "}
                            <Text strong>
                                {value.reference_field?.label
                                    ? value.reference_field?.label
                                    : "Not Set"}
                            </Text>
                        </Text>
                    ))}
            </Spin>
        </Modal>
    );
};
