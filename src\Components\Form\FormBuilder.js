import {
    Button,
    Checkbox,
    DatePicker,
    Form as AntForm,
    Input,
    Modal,
    Radio,
    Select,
    Space,
    Spin,
} from "antd";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getFormFieldTypes } from "../../Actions/FormFieldTypeActions";
import { openNotificationWithIcon } from "../../Shared/notification";
import { MinusCircleOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { postFormFields } from "../../Actions/FormFieldActions";

export const FormBuilder = ({
    formBuilderVisible,
    setFormBuilderVisible,
    formId,
}) => {
    const [form] = AntForm.useForm();
    const [builderForm] = AntForm.useForm();

    const [fields, setFields] = useState([]);
    const [option, setOption] = useState("input");
    const [date, setDate] = useState(new Date(Date.now()));
    const [isAppendableChecked, setIsAppendableChecked] = useState(false);
    const [isEditableChecked, setIsEditableChecked] = useState(false);
    const [isHiddenChecked, setIsHiddenChecked] = useState(false);// Aspire | is_hidden feature 
    const { fieldTypes, errMess, message } = useSelector(
        (state) => state.FormFieldTypeReducer
    );
    const formState = useSelector((state) => state.FormReducer);
    const formFieldState = useSelector((state) => state.FormFieldReducer);
    const dispatch = useDispatch();

    useEffect(() => {
        if (formBuilderVisible) {
            dispatch(getFormFieldTypes());
        }
    }, [formBuilderVisible]);

    useEffect(() => {
        if (formFieldState.errMess) {
            console.log(formFieldState.errMess);
            openNotificationWithIcon("error", formFieldState.errMess);
        }
    }, [formFieldState.errMess]);

    useEffect(() => {
        if (formFieldState.message)
            openNotificationWithIcon("success", formFieldState.message);
    }, [formFieldState.message]);

    useEffect(() => {
        if (errMess) openNotificationWithIcon("error", errMess);
    }, [errMess]);

    useEffect(() => {
        if (message) openNotificationWithIcon("success", message);
    }, [message]);


    const renderValue = (value) => {
        switch (value.type) {
            default:
                return <></>;
            case "input":
                return (
                    <AntForm.Item
                        key={value.id}
                        name={value.name}
                        label={
                            <Space>
                                {value.label}{" "}
                                <MinusCircleOutlined
                                    onClick={() =>
                                        setFields((fields) =>
                                            fields.filter((field) => field.name !== value.name)
                                        )
                                    }
                                />
                            </Space>
                        }
                        required={value.required}
                    >
                        <Input />
                    </AntForm.Item>
                );
            case "textarea":
                return (
                    <AntForm.Item
                        key={value.id}
                        name={value.name}
                        label={
                            <Space>
                                {value.label}{" "}
                                <MinusCircleOutlined
                                    onClick={() =>
                                        setFields((fields) =>
                                            fields.filter((field) => field.name !== value.name)
                                        )
                                    }
                                />
                            </Space>
                        }
                        required={value.required}
                    >
                        <Input.TextArea />
                    </AntForm.Item>
                );
            case "select":
                return (
                    <AntForm.Item
                        key={value.id}
                        name={value.name}
                        label={
                            <Space>
                                {value.label}{" "}
                                <MinusCircleOutlined
                                    onClick={() =>
                                        setFields((fields) =>
                                            fields.filter((field) => field.name !== value.name)
                                        )
                                    }
                                />
                            </Space>
                        }
                        required={value.required}
                    >
                        <Select
                            showSearch  // Enable the search functionality
                            optionFilterProp="children"  // Use the "children" property for filtering

                            mode="tags" // Enable the input field for searching
                            style={{ width: '100%' }} // Adjust the width as needed
                        >

                            {value.options &&
                                value.options.map((selectValue, index) => (
                                    <Select.Option key={index}>{selectValue}</Select.Option>
                                ))}
                        </Select>
                    </AntForm.Item>
                );
            case "checkbox":
                return (
                    <AntForm.Item
                        key={value.id}
                        name={value.name}
                        label={
                            <Space>
                                {value.label}{" "}
                                <MinusCircleOutlined
                                    onClick={() =>
                                        setFields((fields) =>
                                            fields.filter((field) => field.name !== value.name)
                                        )
                                    }
                                />
                            </Space>
                        }
                        required={value.required}
                    >
                        <Checkbox.Group>
                            {value.options &&
                                value.options.map((value, index) => (
                                    <Checkbox value={value} key={index}>
                                        {value}
                                    </Checkbox>
                                ))}
                        </Checkbox.Group>
                    </AntForm.Item>
                );
            case "radio":
                return (
                    <AntForm.Item
                        key={value.id}
                        name={value.name}
                        required={value.required}
                        label={
                            <Space>
                                {value.label}{" "}
                                <MinusCircleOutlined
                                    onClick={() =>
                                        setFields((fields) =>
                                            fields.filter((field) => field.name !== value.name)
                                        )
                                    }
                                />
                            </Space>
                        }
                    >
                        <Radio.Group>
                            {value.options &&
                                value.options.map((value, index) => (
                                    <Radio key={index}>{value}</Radio>
                                ))}
                        </Radio.Group>
                    </AntForm.Item>
                );
            case "date":
                return (
                    <AntForm.Item
                        key={value.id}
                        name={value.name}
                        required={value.required}
                        label={
                            <Space>
                                {value.label}{" "}
                                <MinusCircleOutlined
                                    onClick={() =>
                                        setFields((fields) =>
                                            fields.filter((field) => field.name !== value.name)
                                        )
                                    }
                                />
                            </Space>
                        }
                    >
                        <DatePicker
                            onChange={(date, dateString) => setDate(dateString)}
                            format="YYYY-MM-DD"
                            style={{ width: "50%" }}
                        />
                    </AntForm.Item>
                );
        }
    };

    const onAppendable = (e) => {
        if (e.target.checked) {
            setIsEditableChecked(false);
        }
        setIsAppendableChecked(e.target.checked);
    };

    const onEditable = (e) => {
        if (e.target.checked) {
            setIsAppendableChecked(false);
        }
        setIsEditableChecked(e.target.checked);
    };
    // Aspire | Field hidding feature for agent view 
    const onHideable = (e) => {
        if (e.target.checked) {
            setIsHiddenChecked(false);
        }
        setIsHiddenChecked(e.target.checked);
    };

    const validateWhiteSpace = (rule, value, callback) => {
        if (value && (/\s/.test(value) || /[A-Z]/.test(value))) {
            callback(
                "Capital letters and Spaces are not allowed, use underscore instead of space and small letter instead of Capital letter."
            );
        } else {
            callback();
        }
    };

    return (
        <Modal
            title="Form builder"
            okText="Save"
            visible={formBuilderVisible}
            onCancel={() => {
                setFormBuilderVisible(false)
                setIsAppendableChecked(false)
                setIsEditableChecked(false)
                setIsHiddenChecked(false)
            }}
            onOk={() => {
                form
                    .validateFields()
                    .then((values) => {
                        dispatch(postFormFields(fields, formId));
                        form.resetFields();
                        setFields([]);
                        builderForm.resetFields();
                        setFormBuilderVisible(false);
                    })
                    .catch((e) => console.log(e));
            }}
        >
            <Spin spinning={formFieldState.isLoading}>
                <Space>
                    <AntForm
                        form={builderForm}
                        name="form_builder"
                        initialValues={{ required: true }}
                        layout="vertical"
                        onFinish={(values) => {
                            setFields((fields) => [
                                ...fields,
                                {
                                    ...values,
                                    appendable:
                                        isAppendableChecked && isAppendableChecked == true ? 1 : 0,
                                    editable:
                                        isEditableChecked && isEditableChecked == true ? 1 : 0,
                                    hideable:
                                        isHiddenChecked && isHiddenChecked == true ? 1 : 0,
                                },
                            ]);
                            builderForm.resetFields();
                            setIsAppendableChecked(false)
                            setIsEditableChecked(false)
                            setIsHiddenChecked(false)
                        }}
                    >
                        <AntForm.Item label="Type" name="type" required>
                            <Select onSelect={(option) => setOption(option)}>
                                {fieldTypes &&
                                    fieldTypes.map((value, index) => (
                                        <Select.Option key={value.id} value={value.html}>
                                            {value.name}
                                        </Select.Option>
                                    ))}
                            </Select>
                        </AntForm.Item>
                        {option === "input" && (
                            <>
                                <AntForm.Item
                                    label="Name"
                                    name="name"
                                    required
                                    rules={[{ validator: validateWhiteSpace }]}
                                >
                                    <Input placeholder="Name" />
                                </AntForm.Item>
                                <AntForm.Item label="Label" name="label" required>
                                    <Input placeholder="Label" />
                                </AntForm.Item>
                                <AntForm.Item valuePropName="checked" name="required">
                                    <Checkbox>Required</Checkbox>
                                </AntForm.Item>
                                <AntForm.Item name="appendable" valuePropName="checked">
                                    <Checkbox checked={isAppendableChecked} onChange={onAppendable} disabled={isEditableChecked}>Appendable</Checkbox>
                                </AntForm.Item>
                                <AntForm.Item name="editable" valuePropName="checked">
                                    <Checkbox checked={isEditableChecked} onChange={onEditable} disabled={isAppendableChecked}>Editable</Checkbox>
                                </AntForm.Item>
                                <AntForm.Item name="hideable" valuePropName="checked">
                                    <Checkbox checked={isHiddenChecked} onChange={onHideable} >Hideable</Checkbox>
                                </AntForm.Item>
                            </>
                        )}
                        {option === "textarea" && (
                            <>
                                <AntForm.Item
                                    label="Name"
                                    name="name"
                                    required
                                    rules={[{ validator: validateWhiteSpace }]}
                                >
                                    <Input placeholder="Name" />
                                </AntForm.Item>
                                <AntForm.Item label="Label" name="label" required>
                                    <Input placeholder="Label" />
                                </AntForm.Item>
                                <AntForm.Item valuePropName="checked" name="required">
                                    <Checkbox>Required</Checkbox>
                                </AntForm.Item>
                                <AntForm.Item name="appendable" valuePropName="checked">
                                    <Checkbox checked={isAppendableChecked} onChange={onAppendable} disabled={isEditableChecked}>Appendable</Checkbox>
                                </AntForm.Item>
                                <AntForm.Item name="editable" valuePropName="checked">
                                    <Checkbox checked={isEditableChecked} onChange={onEditable} disabled={isAppendableChecked}>Editable</Checkbox>
                                </AntForm.Item>
                                <AntForm.Item name="hideable" valuePropName="checked">
                                    <Checkbox checked={isHiddenChecked} onChange={onHideable}>Hideable</Checkbox>
                                </AntForm.Item>
                            </>
                        )}
                        {option === "select" && (
                            <>
                                <AntForm.Item name="options">
                                    <Select
                                        showSearch
                                        style={{ width: 200 }}
                                        mode="tags"
                                        allowClear
                                        placeholder="Please add options"
                                    />
                                </AntForm.Item>
                                <AntForm.Item
                                    label="Name"
                                    name="name"
                                    required
                                    rules={[{ validator: validateWhiteSpace }]}
                                >
                                    <Input placeholder="Name" />
                                </AntForm.Item>
                                <AntForm.Item label="Label" name="label" required>
                                    <Input placeholder="Label" />
                                </AntForm.Item>
                                <AntForm.Item valuePropName="checked" name="required">
                                    <Checkbox>Required</Checkbox>
                                </AntForm.Item>
                                <AntForm.Item name="appendable" valuePropName="checked">
                                    <Checkbox checked={isAppendableChecked} onChange={onAppendable} disabled={isEditableChecked}>Appendable</Checkbox>
                                </AntForm.Item>
                                <AntForm.Item name="editable" valuePropName="checked">
                                    <Checkbox checked={isEditableChecked} onChange={onEditable} disabled={isAppendableChecked}>Editable</Checkbox>
                                </AntForm.Item>
                                <AntForm.Item name="hideable" valuePropName="checked">
                                    <Checkbox checked={isHiddenChecked} onChange={onHideable}>Hideable</Checkbox>
                                </AntForm.Item>
                            </>
                        )}
                        {option === "checkbox" && (
                            <>
                                <AntForm.Item
                                    label="Name"
                                    name="name"
                                    required
                                    rules={[{ validator: validateWhiteSpace }]}
                                >
                                    <Input placeholder="Name" />
                                </AntForm.Item>
                                <AntForm.Item label="Label" name="label" required>
                                    <Input placeholder="Label" />
                                </AntForm.Item>
                                <AntForm.Item label="Options" name="options" required>
                                    <Select
                                        style={{ width: 200 }}
                                        mode="tags"
                                        allowClear
                                        placeholder="Please add options"
                                    />
                                </AntForm.Item>
                                <AntForm.Item valuePropName="checked" name="required">
                                    <Checkbox>Required</Checkbox>
                                </AntForm.Item>
                                <AntForm.Item name="appendable" valuePropName="checked">
                                    <Checkbox checked={isAppendableChecked} onChange={onAppendable} disabled={isEditableChecked}>Appendable</Checkbox>
                                </AntForm.Item>
                                <AntForm.Item name="editable" valuePropName="checked">
                                    <Checkbox checked={isEditableChecked} onChange={onEditable} disabled={isAppendableChecked}>Editable</Checkbox>
                                </AntForm.Item>
                                <AntForm.Item name="hideable" valuePropName="checked">
                                    <Checkbox checked={isHiddenChecked} onChange={onHideable}>Hideable</Checkbox>
                                </AntForm.Item>
                            </>
                        )}
                        {option === "date" && (
                            <>
                                <AntForm.Item
                                    label="Name"
                                    name="name"
                                    required
                                    rules={[{ validator: validateWhiteSpace }]}
                                >
                                    <Input placeholder="Name" />
                                </AntForm.Item>
                                <AntForm.Item label="Label" name="label" required>
                                    <Input placeholder="Label" />
                                </AntForm.Item>
                                <AntForm.Item valuePropName="checked" name="required">
                                    <Checkbox>Required</Checkbox>
                                </AntForm.Item>
                                <AntForm.Item name="editable" valuePropName="checked">
                                    <Checkbox checked={isEditableChecked} onChange={onEditable} disabled={isAppendableChecked}>Editable</Checkbox>
                                </AntForm.Item>
                                <AntForm.Item name="hideable" valuePropName="checked">
                                    <Checkbox checked={isHiddenChecked} onChange={onHideable}>Hideable</Checkbox>
                                </AntForm.Item>
                            </>
                        )}
                        {option === "radio" && (
                            <>
                                <AntForm.Item
                                    label="Name"
                                    name="name"
                                    required
                                    rules={[{ validator: validateWhiteSpace }]}
                                >
                                    <Input placeholder="Name" />
                                </AntForm.Item>
                                <AntForm.Item label="Label" name="label" required>
                                    <Input placeholder="Label" />
                                </AntForm.Item>
                                <AntForm.Item label="Options" name="options" required>
                                    <Select
                                        style={{ width: 200 }}
                                        mode="tags"
                                        allowClear
                                        placeholder="Please add options"
                                    />
                                </AntForm.Item>
                                <AntForm.Item valuePropName="checked" name="required">
                                    <Checkbox>Required</Checkbox>
                                </AntForm.Item>
                                <AntForm.Item name="appendable" valuePropName="checked">
                                    <Checkbox checked={isAppendableChecked} onChange={onAppendable} disabled={isEditableChecked}>Appendable</Checkbox>
                                </AntForm.Item>
                                <AntForm.Item name="editable" valuePropName="checked">
                                    <Checkbox checked={isEditableChecked} onChange={onEditable} disabled={isAppendableChecked}>Editable</Checkbox>
                                </AntForm.Item>
                                <AntForm.Item name="hideable" valuePropName="checked">
                                    <Checkbox checked={isHiddenChecked} onChange={onHideable}>Hideable</Checkbox>
                                </AntForm.Item>
                            </>
                        )}
                        <AntForm.Item>
                            <Button htmlType="submit" icon={<PlusCircleOutlined />}>
                                Add
                            </Button>
                        </AntForm.Item>
                    </AntForm>
                </Space>
                <AntForm form={form} layout="vertical">
                    {fields && fields.map((value, index) => renderValue(value))}
                </AntForm>
            </Spin>
        </Modal>
    );
};
