
import React, { useState, useEffect, useRef } from "react"
import { Button, Descriptions, Input, Space, Table } from "antd";
import { useDispatch, useSelector } from "react-redux";
import {
    getFormDataPaginated,
    getFormDataReportFiltered
} from "../../Actions/FormDataReportActions";
import { DownloadOutlined, FilterOutlined, ReloadOutlined, SearchOutlined } from "@ant-design/icons";
import Highlighter from "react-highlight-words";
import { CSVLink } from "react-csv";
import { FormDataFilter } from "../../Components/Reports/FormDataFilter";
import apiClient from "../../Shared/apiClient";

export const FormDataReport = () => {

    const dispatch = useDispatch()
    const [data, setData] = useState([])
    const [csvData, setCsvData] = useState([])
    const formDataReportState = useSelector(state => state.FormDataReportReducer)
    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const [filter, setFilter] = useState(false)
    const [filterValues, setFilterValues] = useState(null)
    const searchInput = useRef()
    const downloadRef = useRef()

    useEffect(() => dispatch(getFormDataPaginated()), [])
    useEffect(() => {
        setData(formDataReportState.formData.data)
    }, [formDataReportState.formData.data])

    // useEffect(() => {
    //     setCsvData(data.map(d => {
    //         let temp = d.data
    //         let obj = Object.entries(temp).map(a => {
    //             const [key, value] = a
    //             return {[key]: value}
    //         })
    //         obj = Object.assign({}, ...obj)
    //         return {id: d.id, created_at: d.created_at, ...obj}
    //     }))
    // }, [data])

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    }

    const handleReset = clearFilters => {
        clearFilters()
        setSearchText('')
        setFilterValues(null)
    }

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    })

    const columns = [
        {
            title: 'Id',
            dataIndex: 'id',
            key: 'id'
        },
        {
            title: 'Call type',
            dataIndex: 'accountcode',
            key: 'accountcode',
            ...getColumnSearchProps('accountcode')
        },
        {
            title: 'Source',
            dataIndex: 'src',
            key: 'src',
            ...getColumnSearchProps('src')
        },
        {
            title: 'Destination',
            dataIndex: 'dst',
            key: 'dst',
            ...getColumnSearchProps('dst')
        },
        {
            title: 'Created At',
            dataIndex: 'created_at',
            key: 'created_at',
            ...getColumnSearchProps('created_at')
        },
        {
            title: 'Data',
            dataIndex: 'data',
            key: 'data',
            ...getColumnSearchProps('data'),
            render: record => {
                //const parsed = JSON.parse(record)
                return (<Descriptions size="small" bordered layout="vertical">
                    {record && Object.keys(record).map((value, index) => <Descriptions.Item
                        label={<b>{value}</b>} key={index}>{record[value]}</Descriptions.Item>)}
                </Descriptions>)
            }
        }
    ]

    const handlePagination = ({ current, pageSize }) => {
        if (filterValues) {
            dispatch(getFormDataReportFiltered(filterValues, current))
        } else {
            dispatch(getFormDataPaginated(current))
        }
    }

    function onFilter(values) {
        setFilterValues(values)
        dispatch(getFormDataReportFiltered(values))
        setFilter(false)
    }

    const handleDownloadReport = async () => {
        const data = await getDownloadData()
        const parsedData = data.map(d => {
            let temp = d.data
            let obj = Object.entries(temp).map(a => {
                const [key, value] = a
                return { [key]: value }
            })
            obj = Object.assign({}, ...obj)
            return { id: d.id, call_type: d.accountcode ?? 'Queue', source: d.src, destination: d.dst, created_at: d.created_at, ...obj }
        })
        setCsvData(parsedData)
        setTimeout(() => {
            downloadRef.current.link.click()
        }, 2000)
    }

    const getDownloadData = () => {
        return apiClient.post(`/api/report/form-data-report-download`, filterValues).then(r => r.data)
    }

    return (
        <>
            <FormDataFilter setVisible={setFilter} isLoading={formDataReportState.isLoading} onCancel={() => setFilter(false)} visible={filter} onCreate={onFilter} record={"ok"} />
            <Table
                dataSource={data}
                rowKey={record => record.id}
                columns={columns}
                scroll={{ x: 1100 }}
                loading={formDataReportState.isLoading}
                pagination={{
                    current: formDataReportState.formData.current_page,
                    pageSize: formDataReportState.formData.per_page,
                    total: formDataReportState.formData.total
                }}
                title={d => <>
                    <span>Form Data Report</span>
                    <div style={{ textAlign: 'right' }}>
                        <Space>
                            <Button icon={<ReloadOutlined />} type="primary" danger onClick={() => dispatch(getFormDataPaginated())}>Reset</Button>
                            <Button icon={<FilterOutlined />} type="primary" onClick={() => setFilter(true)}>Filter</Button>
                            <Button icon={<DownloadOutlined />} onClick={handleDownloadReport}>Download</Button>
                            <CSVLink ref={downloadRef} data={csvData} filename="FormDataReport.csv" target="_blank" className="ant-btn" />
                        </Space>
                    </div>
                </>}
                onChange={handlePagination}
            />
        </>
    )
}