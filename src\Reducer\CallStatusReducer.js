import * as ActionTypes from "../Constants/CallStatusConstants"

const initialState = {
    statuses: [],
    accountCodes: [],
    errMess: false,
    isLoading: false
}

export const CallStatusReducer = (state = initialState, action) => {
    switch (action.type) {
        case ActionTypes.CALL_STATUS_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.CALL_STATUS_SUCCESS:
            return { ...state, isLoading: false, statuses: action.payload }
        case ActionTypes.CALL_STATUS_FAILED:
            return { ...state, isLoading: false, errMess: action.payload }
        case ActionTypes.ACCOUNT_CODES_SUCCESS:
            return { ...state, isLoading: false, accountCodes: action.payload }
        case ActionTypes.ACCOUNT_CODES_FAILED:
            return { ...state, isLoading: false, errMess: action.payload }
        default:
            return state
    }
}