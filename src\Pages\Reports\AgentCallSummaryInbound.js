import React, { useState, useEffect, useRef } from 'react'
import { useDispatch, useSelector } from "react-redux";
import { openNotificationWithIcon } from "../../Shared/notification";
import {
    agentCallSummary,
    agentCallSummaryFiltered, agentCallSummaryFilteredInbound,
    agentCallSummaryInbound,
    agentCallSummaryReset
} from "../../Actions/AgentCallReportActions";
import { Button, DatePicker, Form, Input, Modal, Space, Spin, Table, Select } from "antd";
import { CloseCircleOutlined, DownloadOutlined, FilterOutlined, SearchOutlined } from "@ant-design/icons";
import Highlighter from "react-highlight-words";
import { CSVDownload, CSVLink } from "react-csv";
import { getCallStatuses } from "../../Actions/CallStatusActions";
import { getAgent } from "../../Actions/AgentActions";
import apiClient from "../../Shared/apiClient";


export const AgentCallSummaryInbound = () => {

    const [filterVisible, setFilterVisible] = useState(false)
    const [resetFilter, setResetFilter] = useState(false)
    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const dispatch = useDispatch()
    const callReportState = useSelector(state => state.AgentCallReportReducer)

    useEffect(() => {
        if (callReportState.errMess !== '') openNotificationWithIcon('error', callReportState.errMess)
    }, [callReportState.errMess])

    // useEffect(() => dispatch(agentCallSummaryInbound()), [])
    // useEffect(() => setResetFilter(false), [filterVisible])

    const resetFormFilter = () => {
        // dispatch(agentCallSummaryInbound())
        dispatch(agentCallSummaryReset())
        setResetFilter(true)
    }

    const searchInput = useRef()

    const getColumnSearchProps = dataIndex => ({

        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }

    const columns = [
        {
            title: 'ext1',
            dataIndex: 'ext1',
            key: 'ext1'
        },
        {
            title: 'name',
            dataIndex: 'name',
            key: 'name',
            ...getColumnSearchProps('name')
        },
        {
            title: 'Total Call Attempts',
            dataIndex: 'Total_Call_Attempts',
            key: 'Total_Call_Attempts',
            ...getColumnSearchProps('Total_Call_Attempts')
        },
        {
            title: 'Total Unique Customers',
            dataIndex: 'Total_Unique_Customers',
            key: 'Total_Unique_Customers',
            ...getColumnSearchProps('Total_Unique_Customers')
        },
        {
            title: 'Total Answered Calls',
            dataIndex: 'Total_Answered_Calls',
            key: 'Total_Answered_Calls',
            ...getColumnSearchProps('Total_Answered_Calls')
        },
        {
            title: 'Total Unique Answered Calls',
            dataIndex: 'Total_Unique_Answered_Calls',
            key: 'Total_Unique_Answered_Calls',
            ...getColumnSearchProps('Total_Unique_Answered_Calls')
        },
        {
            title: 'Total Talk Time',
            dataIndex: 'Total_Talk_Time',
            key: 'Total_Talk_Time',
            ...getColumnSearchProps('Total_Talk_Time')
        },
        {
            title: 'Total',
            dataIndex: 'Total',
            key: 'Total',
            ...getColumnSearchProps('Total')
        },

    ]

    // const downloadReport = () => {
    //     console.log('download report')
    // }

    return (
        <>
            <Table title={() =>
                <>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        Agent Call Summary Inbound
                        <Space>
                            <Button onClick={() => resetFormFilter()} type="primary" danger icon={<CloseCircleOutlined />}>Reset Filter</Button>
                            <Button onClick={() => setFilterVisible(true)} icon={<FilterOutlined />}>Filter</Button>
                            {callReportState.data &&
                                <CSVLink filename="AgentCallReport.csv"
                                    data={callReportState.data}>
                                    <Button disabled={callReportState.data.length == 0} onClick={<CSVDownload data={callReportState.data} />}
                                        type='primary'
                                        target="_blank"
                                        icon={<DownloadOutlined />}>
                                        Download CSV
                                    </Button>
                                </CSVLink>
                            }
                        </Space>

                    </div>
                </>

            }
                loading={callReportState.isLoading} dataSource={callReportState.data} columns={columns} bordered scroll={{ x: 1100 }} />
            <AgentCallReportFilter resetField={resetFilter} visible={filterVisible} setVisible={setFilterVisible} />
        </>
    )
}

const AgentCallReportFilter = ({ visible, setVisible, resetField }) => {

    const [form] = Form.useForm()
    const agentState = useSelector(state => state.AgentReducer)
    const callState = useSelector(state => state.CallStatusReducer)
    const dispatch = useDispatch()
    const [queues, setQueues] = useState([])
    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data))
    }, [])
    useEffect(() => {
        dispatch(getCallStatuses())
        dispatch(getAgent())
    }, [])

    useEffect(() => form.resetFields(), [resetField])

    return (
        <Modal
            title="Filter"
            onCancel={() => {
                setVisible(false)
            }}
            visible={visible}
            onOk={() => {
                form.validateFields()
                    .then(values => {
                        dispatch(agentCallSummaryFilteredInbound(values));
                        setVisible(false)
                        form.resetFields()
                    })
                    .catch(e => console.log(e))
            }}
        >
            <Spin spinning={callState.isLoading || agentState.isLoading}>
                <Form form={form} layout="vertical">
                    <Form.Item name="range" label="Date Time">
                        <DatePicker.RangePicker showTime />
                    </Form.Item>
                    <Form.Item name="queue" label="Queue" >
                        <Select  >
                            {queues &&
                                queues.map((value, index) => (
                                    <Select.Option key={index} value={value.name}>
                                        {value.name}
                                    </Select.Option>
                                ))}
                        </Select>
                    </Form.Item>
                </Form>
            </Spin>
        </Modal>
    )
}
