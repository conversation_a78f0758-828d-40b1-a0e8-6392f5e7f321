import * as ActionTypes from '../Constants/SettingConstants'
import apiClient from "../Shared/apiClient";
import { SETTING, SETTINGS } from "../Endpoints/SettingRoutes";
import { logoutUser } from "../Actions/UserActions";

export const getSetting = () => dispatch => {
    dispatch(Loading())
    apiClient.get(SETTING).then(response => {
        dispatch(settingSuccess(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(settingFailed(error.response))
        }
        else
            dispatch(settingFailed(error.message))
    })
}

export const updateSetting = (id, data) => dispatch => {
    dispatch(Loading())
    apiClient.put(`${SETTINGS}/${id}`, data).then(response => {
        dispatch(settingUpdate(response.data))
    }).catch(error => {
        if (error.response) {
            if (error.response.status === 401)
                dispatch(logoutUser())
            dispatch(settingFailed(error.response))
        }
        else
            dispatch(settingFailed(error.message))
    })
}


export const Loading = () => ({
    type: ActionTypes.SETTING_LOADING
})

export const settingSuccess = setting => ({
    type: ActionTypes.SETTING_SUCCESS,
    payload: setting
})

export const settingUpdate = message => ({
    type: ActionTypes.SETTING_UPDATE,
    payload: message
})

export const settingFailed = error => ({
    type: ActionTypes.SETTING_FAILED,
    payload: error
})
