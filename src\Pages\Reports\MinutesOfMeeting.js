import {
    <PERSON><PERSON>,
    Card,
    DatePicker,
    Form,
    Input,
    Modal,
    Select,
    Col,
    Row,
    Space,
    Spin,
    Table,
} from "antd";
import {
    CloseCircleOutlined,
    DownloadOutlined,
    FilterOutlined,
} from "@ant-design/icons";
import React, { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { CSVDownload, CSVLink } from "react-csv";
import { getCallStatuses } from "../../Actions/CallStatusActions";
import { getAgent } from "../../Actions/AgentActions";
import { openNotificationWithIcon } from "../../Shared/notification";
import Highlighter from "react-highlight-words";
import { SearchOutlined } from "@ant-design/icons";
import {
    getMinutesOfMeetingFilteredReport,
    MinutesOfMeetingReport,
} from "../../Actions/CallDetailReportsActions";

export const MinutesOfMeeting = () => {
    const [filterVisible, setFilterVisible] = useState(false);
    const [resetFilter, setResetFilter] = useState(false);
    const [searchText, setSearchText] = useState("");
    const [searchedColumn, setSearchedColumn] = useState("");
    const dispatch = useDispatch();
    const callReportState = useSelector((state) => state.CdrReducer);

    useEffect(() => {
        if (callReportState.errMess) {

            openNotificationWithIcon("error", callReportState.errMess);
        }

    }, [callReportState.errMess]);

    // useEffect(() => dispatch(MinutesOfMeetingReport()), []);
    useEffect(() => setResetFilter(false), [filterVisible]);

    const resetFormFilter = () => {
        dispatch(MinutesOfMeetingReport());
        setResetFilter(true);
    };

    const searchInput = useRef();

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({
            setSelectedKeys,
            selectedKeys,
            confirm,
            clearFilters,
        }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={(e) =>
                        setSelectedKeys(e.target.value ? [e.target.value] : [])
                    }
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: "block" }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button
                        onClick={() => handleReset(clearFilters)}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0]);
                            setSearchedColumn(dataIndex);
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined style={{ color: filtered ? "#1890ff" : undefined }} />
        ),
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex]
                    .toString()
                    .toLowerCase()
                    .includes(value.toLowerCase())
                : "",
        onFilterDropdownVisibleChange: (visible) => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: (text) =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: "#ffc069", padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ""}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0]);
        setSearchedColumn(dataIndex);
    };

    const handleReset = (clearFilters) => {
        clearFilters({ confirm: true });
        setSearchText("");
    };

    const columns = [
        {
            title: "Unique_Party_B",
            dataIndex: "Unique_Party_B",
            key: "Unique_Party_B",
            ...getColumnSearchProps("Unique_Party_B"),
        },
        {
            title: "Total_Call_Attempts",
            dataIndex: "Total_Call_Attempts",
            key: "Total_Call_Attempts",
            ...getColumnSearchProps("Total_Call_Attempts"),
        },
        {
            title: "Answered_Calls",
            dataIndex: "Answered_Calls",
            key: "Answered_Calls",
            ...getColumnSearchProps("Answered_Calls"),
        },
        {
            title: "Total_Talk_Time",
            dataIndex: "Total_Talk_Time",
            key: "Total_Talk_Time",
            ...getColumnSearchProps("Total_Talk_Time"),
        },
    ];
    return (
        <>
            <Table
                loading={callReportState.isLoading}
                title={() => (
                    <Row>
                        <Col xs={12}>
                            <div style={{ padding: "0.5rem 0 0 1rem" }}>
                                <span>Unique Outbound Calls Summary</span>
                            </div>
                        </Col>
                        <Col xs={12}>
                            <div style={{ textAlign: "right" }}>
                                <Space>
                                    <Button
                                        onClick={() => resetFormFilter()}
                                        type="primary"
                                        danger
                                        icon={<CloseCircleOutlined />}
                                    >
                                        Reset Filter
                                    </Button>
                                    <Button
                                        onClick={() => setFilterVisible(true)}
                                        icon={<FilterOutlined />}
                                    >
                                        Filter
                                    </Button>
                                    {callReportState.data && (
                                        <CSVLink
                                            filename="AgentCallReport.csv"
                                            data={callReportState.data}
                                        >
                                            <Button
                                                disabled={callReportState.data.length == 0}
                                                onClick={<CSVDownload data={callReportState.data} />}
                                                target="_blank"
                                                type="primary"
                                                icon={<DownloadOutlined />}
                                            >
                                                Download CSV

                                            </Button>
                                        </CSVLink>
                                    )}
                                </Space>
                            </div>
                        </Col>
                    </Row>
                )}
                dataSource={callReportState.data}
                columns={columns}
                bordered
            />
            <AgentCallReportFilter
                resetField={resetFilter}
                visible={filterVisible}
                setVisible={setFilterVisible}
            />
        </>
    );
};

const AgentCallReportFilter = ({ visible, setVisible, resetField }) => {
    const [form] = Form.useForm();
    const agentState = useSelector((state) => state.AgentReducer);
    const callState = useSelector((state) => state.CallStatusReducer);
    const dispatch = useDispatch();

    useEffect(() => {
        dispatch(getCallStatuses());
        dispatch(getAgent());
    }, []);

    useEffect(() => form.resetFields(), [resetField]);

    return (
        <Modal
            title="Filter"
            onCancel={() => {
                setVisible(false);
            }}
            visible={visible}
            onOk={() => {
                form
                    .validateFields()
                    .then((values) => {
                        dispatch(getMinutesOfMeetingFilteredReport(values));
                        setVisible(false);
                    })
                    .catch((e) => console.log(e));
            }}
        >
            <Spin spinning={callState.isLoading || agentState.isLoading}>
                <Form form={form} layout="vertical">
                    <Form.Item name="range" label="Date Time">
                        <DatePicker.RangePicker showTime />
                    </Form.Item>
                </Form>
            </Spin>
        </Modal>
    );
};
