import { useDispatch, useSelector } from "react-redux";
import { DatePicker, Form, Input, Modal, Select, Spin } from "antd";
import { useEffect, useState } from "react"
import { getQueues } from "../../Actions/QueueActions";

export const PauseReasonReportFilter = ({ visible, setVisible, onCreate, isLoading }) => {
    const [form] = Form.useForm()
    const QueueState = useSelector(state => state.QueueReducer)
    const [showQueue, setShowQueue] = useState(false)
    const dispatch = useDispatch()

    useEffect(() => {
        if (visible) {
            dispatch(getQueues())
            setShowQueue(true)
        }
    }, [visible])

    return (
        <Spin spinning={QueueState.isLoading || isLoading}>
            <Modal
                visible={visible}
                onCancel={() => {
                    form.resetFields()
                    setVisible(false)
                }}
                onOk={() => {
                    form
                        .validateFields()
                        .then((values) => {
                            console.log("validated")
                            onCreate(values);
                            form.resetFields();
                        })
                        .catch((info) => {
                            console.log('Validate Failed:', info);
                        })
                }}
            >
                <Form
                    form={form}
                    layout="vertical"
                    name="form_in_modal"
                >
                    <Form.Item
                        name="time"
                        label="Time"
                    >
                        {/*<Input type={"datetime-local"} />*/}
                        <DatePicker.RangePicker showTime />

                    </Form.Item>

                    <Form.Item
                        name="callid"
                        label="CallId"
                        rules={[
                            {
                                // required: true,
                                message: 'Please input the callId',
                            },
                        ]}
                    >
                        <Input />
                    </Form.Item>

                    <Form.Item
                        name="agent"
                        label="Agent"
                        rules={[
                            {
                                // required: true,
                                message: 'Please input the Agent',
                            },
                        ]}
                    >
                        <Input />
                    </Form.Item>

                    {/*<Form.Item*/}
                    {/*    name="event"*/}
                    {/*    label="CallId"*/}
                    {/*    rules={[*/}
                    {/*        {*/}
                    {/*            // required: true,*/}
                    {/*            message: 'Please input the callId',*/}
                    {/*        },*/}
                    {/*    ]}*/}
                    {/*>*/}
                    {/*    <Input />*/}
                    {/*</Form.Item>*/}

                    {showQueue && <Form.Item
                        name="queuename"
                        label="Queue"
                        rules={[
                            {
                                message: 'Please select the queue',
                            },
                        ]}
                    >
                        <Select>
                            {QueueState.queues.map((value, index) => (
                                <Select.Option value={value.name} key={value.name}>{value.name}</Select.Option>
                            ))}
                        </Select>
                    </Form.Item>}

                </Form>

            </Modal>
        </Spin>
    );
}

export default PauseReasonReportFilter;