import { Form, Input, InputNumber, Modal } from "antd";
import { useEffect, useState } from "react";

export const EditCustomNumber = (props) => {

    const [form] = Form.useForm()
    const [visible, setVisible] = useState(false)

    useEffect(() => {
        props.item && form.setFieldsValue({ customer_phone: props.item.customer_phone, retailer_name: props.item.retailer_name, agent_id: props.item.agent_id, inactive_days: props.item.inactive_days, target: props.item.target, gmv: props.item.gmv, number_of_unique_sku_sold: props.item.number_of_unique_sku_sold, attempts: props.item.attempts, shop_name: props.item.shop_name })
    }, [props.item])

    useEffect(() => console.log(props.item), [props.item])

    return (
        <Modal
            visible={props.item}
            title={`Edit number ${props.item.customer_phone}`}
            okText="Save"
            onCancel={props.onCancel}
            onOk={() => {
                form
                    .validateFields()
                    .then(values => {
                        props.onSubmit(values)
                    }).catch(err => console.log(err))
            }}
        >
            <Form
                form={form}
                layout="vertical"
            >
                <Form.Item name="customer_phone" label="Customer Phone">
                    <Input />
                </Form.Item>
                <Form.Item name="retailer_name" label="Retailer Name">
                    <Input />
                </Form.Item>
                <Form.Item name="shop_name" label="Shop Name">
                    <Input />
                </Form.Item>
                <Form.Item name="agent_id" label="Agent">
                    <Input />
                </Form.Item>
                <Form.Item name="inactive_days" label="Inactive days">
                    <Input />
                </Form.Item>
                <Form.Item name="target" label="Target">
                    <Input />
                </Form.Item>
                <Form.Item name="gmv" label="GMV">
                    <Input />
                </Form.Item>
                <Form.Item name="number_of_unique_sku_sold" label="Number of Unique SKUs sold">
                    <Input />
                </Form.Item>
                <Form.Item name="attempts" label="Attempts">
                    <InputNumber max={10} min={1} />
                </Form.Item>
            </Form>
        </Modal>
    )

}