// import {<PERSON><PERSON>, <PERSON>, Col, Form, Input, Row, Spin, Switch} from "antd";
// import {useDispatch, useSelector} from "react-redux";
// import {useState, useEffect} from 'react'
// import {updateEmailSetting, getEmailSetting} from "../Actions/EmailSettingActions";
// import {openNotificationWithIcon} from "../Shared/notification";

// const layout = {
//     labelCol: { span: 8 },
//     wrapperCol: { span: 16 },
// };

// const tailLayout = {
//     wrapperCol: { offset: 2, span: 16 },
// };

// const inputType = data => {
//     console.log(data.type == "switch")
//     switch (data.type){
//         case "password":
//             return <Input.Password />
//         case "switch":
//             return <Switch defaultChecked={data.value == "1" ? true: false} />
//         default:
//             return <Input />
//     }
// }

// const EmailSetting = () => {

//     const systemSetting = useSelector(state => state.SettingReducer)
//     const [record, setRecord] = useState(null)
//     const [id, setId]= useState()
//     const dispatch = useDispatch()
//     const [form] = Form.useForm()

//     useEffect(() => {
//         if(systemSetting.errMess){
//             openNotificationWithIcon('error', systemSetting.errMess)
//         }
//     },[systemSetting.errMess])

//     useEffect(() => {
//         if(systemSetting.message){
//             openNotificationWithIcon('success', systemSetting.message)
//             dispatch(getEmailSetting())
//         }
//     },[systemSetting.message])

//     useEffect(()=> {
//         dispatch(getEmailSetting())
//     },[])

//     const handleSubmit = values => {
//         // setShowEdit(false)
//         console.log(values)
//         dispatch(updateEmailSetting(id, values))
//     }



//     useEffect(()=>{
//         let objects = new Object();
//         for (let b in systemSetting?.setting) {
//             objects[systemSetting?.setting[b].key] = systemSetting?.setting[b].value
//             setId(systemSetting?.setting[b].id)
//         }
//         setRecord(objects)
//     },[systemSetting.setting])

//     useEffect(()=> {
//         form.setFieldsValue(record)
//     },[record])

//     return(
//         <Spin spinning={systemSetting.isLoading}>
//             <Card>
//                 <Form
//                     form={form}
//                     initialValues={record}
//                     onFinish={handleSubmit}
//                     {...layout}
//                 >
//                     <Row gutter={[5, 5]}>
//                         {systemSetting.setting && systemSetting.setting.map((value, index) =>
//                             <Col key={index} span={10}>
//                                 <Form.Item
//                                     label={value.key}
//                                     name={value.key}
//                                     key={value.key}
//                                     // valuePropName={value.type == "switch" ? "checked" : null}        form.setFieldsValue(record)

//                                     // rules={[
//                                     //     {
//                                     //         // required: true,
//                                     //         message: () => value.type == "switch" ? null : 'Please input your '+value.key,
//                                     //     },
//                                     // ]}
//                                 >
//                                     {inputType(value)}
//                                 </Form.Item>
//                             </Col>
//                         )}
//                     </Row>
//                     <Form.Item>
//                         <Button type="primary" htmlType="submit">
//                             Update
//                         </Button>
//                     </Form.Item>
//                 </Form>
//             </Card>
//         </Spin>
//     )
// }
// export default EmailSetting


// import {Button, Card, Col, Form, Input, Row, Spin, Switch} from "antd";
// import {useDispatch, useSelector} from "react-redux";
// import {useState, useEffect} from 'react'
// import {updateEmailSetting, getEmailSetting} from "../Actions/EmailSettingActions";
// import {openNotificationWithIcon} from "../Shared/notification";

// const layout = {
//     labelCol: { span: 8 },
//     wrapperCol: { span: 16 },
// };

// const tailLayout = {
//     wrapperCol: { offset: 2, span: 16 },
// };

// const inputType = data => {
//     console.log(data.type == "switch")
//     switch (data.type){
//         case "password":
//             return <Input.Password />
//         case "switch":
//             return <Switch defaultChecked={data.value == "1" ? true: false} />
//         default:
//             return <Input />
//     }
// }

// const EmailSetting = () => {

//     const systemSetting = useSelector(state => state.EmailSettingReducer)
//     const [record, setRecord] = useState(null)
//     const [id, setId]= useState()
//     const dispatch = useDispatch()
//     const [form] = Form.useForm()

//     useEffect(() => {
//         if(systemSetting.errMess){
//             openNotificationWithIcon('error', systemSetting.errMess)
//         }
//     },[systemSetting.errMess])

//     useEffect(() => {
//         if(systemSetting.message){
//             openNotificationWithIcon('success', systemSetting.message)
//             dispatch(getEmailSetting())
//         }
//     },[systemSetting.message])

//     useEffect(()=> {
//         dispatch(getEmailSetting())
//     },[])

//     const handleSubmit = values => {
//         // setShowEdit(false)
//         console.log(values)
//         dispatch(updateEmailSetting(id, values))
//     }

//     useEffect(()=>{
//         let objects = new Object();
//         for (let b in systemSetting?.setting) {
//             objects[systemSetting?.setting[b].key] = systemSetting?.setting[b].value
//             setId(systemSetting?.setting[b].id)
//         }
//         setRecord(objects)
//     },[systemSetting.setting])

//     useEffect(()=> {
//         form.setFieldsValue(record)
//     },[record])

//     return(
//         <Spin spinning={systemSetting.isLoading}>
//             <Card>
//                 <Form
//                     form={form}
//                     initialValues={record}
//                     onFinish={handleSubmit}
//                     {...layout}
//                 >
//                     <Row gutter={[5, 5]}>
//                         {systemSetting.setting && systemSetting.setting.map((value, index) =>
//                             <Col key={index} span={10}>
//                                 <Form.Item
//                                     label={value.key}
//                                     name={value.key}
//                                     key={value.key}
//                                     // valuePropName={value.type == "switch" ? "checked" : null}
//                                     // rules={[
//                                     //     {
//                                     //         // required: true,
//                                     //         message: () => value.type == "switch" ? null : 'Please input your '+value.key,
//                                     //     },
//                                     // ]}
//                                 >
//                                     {inputType(value)}
//                                 </Form.Item>
//                             </Col>
//                         )}
//                     </Row>
//                     <Form.Item>
//                         <Button type="primary" htmlType="submit">
//                             Update
//                         </Button>
//                     </Form.Item>
//                 </Form>
//             </Card>
//         </Spin>
//     )
// }
// export default EmailSetting



// import {Button, Card, Col, Form, Input, Row, Spin, Typography} from "antd";
// import {useDispatch, useSelector} from "react-redux";
// import {useState, useEffect} from 'react'
// import {updateEmailSetting, getEmailSetting} from "../Actions/EmailSettingActions";
// import {openNotificationWithIcon} from "../Shared/notification";

// const { Title } = Typography;

// const layout = {
//     labelCol: { span: 8 },
//     wrapperCol: { span: 16 },
// };

// const tailLayout = {
//     wrapperCol: { offset: 8, span: 16 },
// };

// const EmailSetting = () => {
//     const systemSetting = useSelector(state => state.EmailSettingReducer)
//     const [record, setRecord] = useState(null)
//     const [id, setId] = useState()
//     const dispatch = useDispatch()
//     const [form] = Form.useForm()

//     useEffect(() => {
//         if(systemSetting.errMess){
//             openNotificationWithIcon('error', systemSetting.errMess)
//         }
//     },[systemSetting.errMess])

//     useEffect(() => {
//         if(systemSetting.message){
//             openNotificationWithIcon('success', systemSetting.message)
//             dispatch(getEmailSetting())
//         }
//     },[systemSetting.message])

//     useEffect(()=> {
//         dispatch(getEmailSetting())
//     },[])

//     const handleSubmit = values => {
//         dispatch(updateEmailSetting(id, values))
//     }

//     useEffect(()=>{
//         let objects = new Object();
//         for (let b in systemSetting?.setting) {
//             objects[systemSetting?.setting[b].key] = systemSetting?.setting[b].value
//             setId(systemSetting?.setting[b].id)
//         }
//         setRecord(objects)
//     },[systemSetting.setting])

//     useEffect(()=> {
//         form.setFieldsValue(record)
//     },[record])

//     return(
//         <Spin spinning={systemSetting.isLoading}>
//             <Card 
//                 title={<Title level={5} style={{ marginBottom: 0 ,
//                  }}>Email Settings</Title>}
//                 headStyle={{ borderBottom: 0 }}
//                 style={{ 
//                     boxShadow: '0 4px 8px 0 rgba(0,0,0,0.2)',
//                     borderRadius: '8px',
//                 }}
//                 bodyStyle={{ padding: '24px' }}
//             >
//                 <Form
//                     form={form}
//                     initialValues={record}
//                     onFinish={handleSubmit}
//                     {...layout}
//                 >
//                     <Row gutter={[16, 16]}>
//                         {/* SMTP Host */}
//                         <Col span={12}>
//                             <Form.Item
//                                 label="SMTP Host"
//                                 name="host"
//                                 rules={[{ required: true, message: 'Please input SMTP host' }]}
//                             >
//                                 <Input placeholder="smtp.example.com" />
//                             </Form.Item>
//                         </Col>

//                         {/* SMTP Port */}
//                         <Col span={12}>
//                             <Form.Item
//                                 label="SMTP Port"
//                                 name="port"
//                                 rules={[{ required: true, message: 'Please input SMTP port' }]}
//                             >
//                                 <Input type="number" placeholder="587" />
//                             </Form.Item>
//                         </Col>

//                         {/* Encryption */}
//                         <Col span={12}>
//                             <Form.Item
//                                 label="Encryption"
//                                 name="encryption"
//                                 rules={[{ required: true, message: 'Please select encryption' }]}
//                             >
//                                 <Input placeholder="tls/ssl" />
//                             </Form.Item>
//                         </Col>

//                         {/* Email Address */}
//                         <Col span={12}>
//                             <Form.Item
//                                 label="Email Address"
//                                 name="email"
//                                 rules={[
//                                     { required: true, message: 'Please input email address' },
//                                     { type: 'email', message: 'Please enter a valid email' }
//                                 ]}
//                             >
//                                 <Input placeholder="<EMAIL>" />
//                             </Form.Item>
//                         </Col>

//                         {/* Password */}
//                         <Col span={12}>
//                             <Form.Item
//                                 label="Password"
//                                 name="password"
//                                 rules={[{ required: true, message: 'Please input password' }]}
//                             >
//                                 <Input.Password placeholder="Email account password" />
//                             </Form.Item>
//                         </Col>
//                     </Row>

//                     <Form.Item {...tailLayout} style={{display:'flex'}}>
//                         <Button 
//                             type="primary" 
//                             htmlType="submit"
//                             style={{ width: '150px', height: '40px' }}
//                         >
//                             Update Settings
//                         </Button>
//                     </Form.Item>
//                 </Form>
//             </Card>
//         </Spin>
//     )
// }

// export default EmailSetting;

import {Button, Card, Col, Form, Input, Row, Spin, Typography} from "antd";
import {useDispatch, useSelector} from "react-redux";
import {useState, useEffect} from 'react'
import {updateEmailReplaySetting, updateEmailSetting, getEmailSetting} from "../Actions/EmailSettingActions";
import {openNotificationWithIcon} from "../Shared/notification";

const { Title } = Typography;

const layout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
};

const tailLayout = {
    wrapperCol: { offset: 8, span: 16 },
};

const EmailSetting = () => {
    const systemSetting = useSelector(state => state.EmailSettingReducer)
    const [receivingRecord, setReceivingRecord] = useState(null)
    const [sendingRecord, setSendingRecord] = useState(null)
    const [receivingId, setReceivingId] = useState()
    const [sendingId, setSendingId] = useState()
    const dispatch = useDispatch()
    const [receivingForm] = Form.useForm()
    const [sendingForm] = Form.useForm()

    useEffect(() => {
        if(systemSetting.errMess){
            openNotificationWithIcon('error', systemSetting.errMess)
        }
    },[systemSetting.errMess])

    useEffect(() => {
        if(systemSetting.message){
            openNotificationWithIcon('success', systemSetting.message)
            dispatch(getEmailSetting())
        }
    },[systemSetting.message])

    useEffect(()=> {
        dispatch(getEmailSetting())
    },[])

    const handleSubmit = values => {
        dispatch(updateEmailSetting(receivingId, values))
    }

    const handleReplaySubmit = values => {
        dispatch(updateEmailReplaySetting(sendingId, values))
    }

    useEffect(()=>{
        if (systemSetting?.setting) {
            const receivingObjects = {};
            const sendingObjects = {};
            
            systemSetting.setting.forEach(item => {
                if (item.key.startsWith('sending_')) {
                    sendingObjects[item.key] = item.value;
                    setSendingId(item.id);
                } else {
                    receivingObjects[item.key] = item.value;
                    setReceivingId(item.id);
                }
            });
            
            setReceivingRecord(receivingObjects);
            setSendingRecord(sendingObjects);
        }
    },[systemSetting.setting])

    useEffect(()=> {
        receivingForm.setFieldsValue(receivingRecord)
    },[receivingRecord])

    useEffect(()=> {
        sendingForm.setFieldsValue(sendingRecord)
    },[sendingRecord])

    return(
        <Spin spinning={systemSetting.isLoading}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
                {/* First Card - Receiving Email Settings */}
                <Card 
                    title={<Title level={5} style={{ marginBottom: 0 }}>Receiving Email Settings</Title>}
                    headStyle={{ borderBottom: 0 }}
                    style={{ 
                        boxShadow: '0 4px 8px 0 rgba(0,0,0,0.2)',
                        borderRadius: '8px'
                    }}
                    bodyStyle={{ padding: '24px' }}
                >
                    <Form
                        form={receivingForm}
                        initialValues={receivingRecord}
                        onFinish={handleSubmit}
                        {...layout}
                    >
                        <Row gutter={[16, 16]}>
                            <Col span={12}>
                                <Form.Item
                                    label="Host"
                                    name="host"
                                    rules={[{ required: true, message: 'Please input SMTP host' }]}
                                >
                                    <Input placeholder="smtp.example.com" />
                                </Form.Item>
                            </Col>

                            <Col span={12}>
                                <Form.Item
                                    label="Port"
                                    name="port"
                                    rules={[{ required: true, message: 'Please input SMTP port' }]}
                                >
                                    <Input type="number" placeholder="587" />
                                </Form.Item>
                            </Col>

                            <Col span={12}>
                                <Form.Item
                                    label="Encryption"
                                    name="encryption"
                                    rules={[{ required: true, message: 'Please select encryption' }]}
                                >
                                    <Input placeholder="tls/ssl" />
                                </Form.Item>
                            </Col>

                            <Col span={12}>
                                <Form.Item
                                    label="Email Address"
                                    name="email"
                                    rules={[
                                        { required: true, message: 'Please input email address' },
                                        { type: 'email', message: 'Please enter a valid email' }
                                    ]}
                                >
                                    <Input placeholder="<EMAIL>" />
                                </Form.Item>
                            </Col>

                            <Col span={12}>
                                <Form.Item
                                    label="Password"
                                    name="password"
                                    rules={[{ required: true, message: 'Please input password' }]}
                                >
                                    <Input.Password placeholder="Email account password" />
                                </Form.Item>
                            </Col>
                        </Row>

                        <Form.Item {...tailLayout} style={{display:'flex'}}>
                            <Button 
                                type="primary" 
                                htmlType="submit"
                                style={{ width: '150px', height: '40px' }}
                            >
                                Update Settings
                            </Button>
                        </Form.Item>
                    </Form>
                </Card>

                {/* Second Card - Sending Email Settings */}
                <Card 
                    title={<Title level={5} style={{ marginBottom: 0 }}>Sending Email Settings</Title>}
                    headStyle={{ borderBottom: 0 }}
                    style={{ 
                        boxShadow: '0 4px 8px 0 rgba(0,0,0,0.2)',
                        borderRadius: '8px'
                    }}
                    bodyStyle={{ padding: '24px' }}
                >
                    <Form
                        form={sendingForm}
                        initialValues={sendingRecord}
                        onFinish={handleReplaySubmit}
                        {...layout}
                    >
                        <Row gutter={[16, 16]}>
                            <Col span={12}>
                                <Form.Item
                                    label="Host"
                                    name="sending_host"
                                    rules={[{ required: true, message: 'Please input SMTP host' }]}
                                >
                                    <Input placeholder="smtp.example.com" />
                                </Form.Item>
                            </Col>

                            <Col span={12}>
                                <Form.Item
                                    label="Port"
                                    name="sending_port"
                                    rules={[{ required: true, message: 'Please input SMTP port' }]}
                                >
                                    <Input type="number" placeholder="587" />
                                </Form.Item>
                            </Col>

                            <Col span={12}>
                                <Form.Item
                                    label="Encryption"
                                    name="sending_encryption"
                                    rules={[{ required: true, message: 'Please select encryption' }]}
                                >
                                    <Input placeholder="tls/ssl" />
                                </Form.Item>
                            </Col>

                            <Col span={12}>
                                <Form.Item
                                    label="Email Address"
                                    name="sending_email"
                                    rules={[
                                        { required: true, message: 'Please input email address' },
                                        { type: 'email', message: 'Please enter a valid email' }
                                    ]}
                                >
                                    <Input placeholder="<EMAIL>" />
                                </Form.Item>
                            </Col>

                            <Col span={12}>
                                <Form.Item
                                    label="Password"
                                    name="sending_password"
                                    rules={[{ required: true, message: 'Please input password' }]}
                                >
                                    <Input.Password placeholder="Email account password" />
                                </Form.Item>
                            </Col>
                            
                            <Col span={12}>
                                <Form.Item
                                    label="From Address"
                                    name="sending_from_address"
                                    rules={[
                                        { required: true, message: 'Please input from email address' },
                                        { type: 'email', message: 'Please enter a valid email' }
                                    ]}
                                >
                                    <Input placeholder="<EMAIL>" />
                                </Form.Item>
                            </Col>

                            <Col span={12}>
                                <Form.Item
                                    label="From Name"
                                    name="sending_from_name"
                                    rules={[{ required: true, message: 'Please enter a from name' }]}
                                >
                                    <Input placeholder="Xyz" />
                                </Form.Item>
                            </Col>

                        </Row>

                        <Form.Item {...tailLayout} style={{display:'flex'}}>
                            <Button 
                                type="primary" 
                                htmlType="submit"
                                style={{ width: '150px', height: '40px' }}
                            >
                                Update Settings
                            </Button>
                        </Form.Item>
                    </Form>
                </Card>
            </div>
        </Spin>
    )
}

export default EmailSetting;