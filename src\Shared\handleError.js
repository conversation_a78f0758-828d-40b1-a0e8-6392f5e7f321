export const handleError = error => {
    if (error?.data) {
        let message = '';
        if (typeof error.data === "object") {
            if (error.data.errors) {
                // Handle the error array case
                Object.keys(error.data.errors).forEach(key => {
                    if (Array.isArray(error.data.errors[key])) {
                        error.data.errors[key].forEach(err => {
                            message += `${err}\n`;
                        });
                    }
                });
                return message;
            }
        }
    }
    else if (error?.response) {
        if (error?.response?.status === 419 || error?.response?.status === 422)
            if (error?.response?.data?.message)
                return error?.response?.data?.message;
            else return error?.response?.data;
    }
    else if (error?.request) return error?.request;
    else return error?.message;
};
