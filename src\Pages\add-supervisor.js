import { But<PERSON>, Card, Form, Input } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { useEffect, useRef } from "react";
import { createSupervisor } from "../Actions/SupervisorAction";
import { openNotificationWithIcon } from "../Shared/notification";
import { useHistory } from "react-router-dom";
import { handleError } from "../Shared/handleError";

export const AddSupervisor = () => {
    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const supervisor = useSelector(state => state.SupervisorReducer);
    const history = useHistory();
    const prevError = useRef(null); // ✅ Prevents duplicate error messages

    const onFinish = (values) => {
        dispatch(createSupervisor(values));
    };

    useEffect(() => {
        if (supervisor.message) {
            // openNotificationWithIcon('success', supervisor.message);
            form.resetFields(); // ✅ Reset form after successful submission
            history.push('/supervisor');
        } else if (supervisor.errMess && supervisor.errMess !== prevError.current) {
            prevError.current = supervisor.errMess; // ✅ Store previous error
            openNotificationWithIcon('error', handleError(supervisor.errMess));
        }
    }, [supervisor, history, form]);

    return (
        <Card>
            <Form
                form={form}
                name="form_in_modal"
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 16 }}
                onFinish={onFinish}
            >
                <Form.Item
                    name="name"
                    label="Name"
                    rules={[{ required: true, message: 'Please input the name' }]}
                >
                    <Input />
                </Form.Item>

                <Form.Item
                    name="username"
                    label="Username"
                    rules={[{ required: true, message: 'Please input the username' }]}
                >
                    <Input />
                </Form.Item>

                <Form.Item
                    name="email"
                    label="E-Mail"
                    rules={[
                        { required: true, message: 'Please input the email address' },
                        { type: 'email', message: 'Please enter a valid email' },
                    ]}
                >
                    <Input />
                </Form.Item>

                {/* ✅ Improved password validation to show message only once */}
                <Form.Item
                    name="password"
                    label="Password"
                    rules={[
                        { required: true, message: 'Please input the password' },
                        { min: 4, message: 'Password must be at least 4 characters' },
                    ]}
                >
                    <Input.Password />
                </Form.Item>

                <Form.Item
                    name="password_confirmation"
                    label="Confirm Password"
                    dependencies={["password"]}
                    rules={[
                        { required: true, message: 'Please confirm your password' },
                        ({ getFieldValue }) => ({
                            validator(_, value) {
                                if (!value || getFieldValue('password') === value) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(new Error('Passwords do not match!'));
                            },
                        }),
                    ]}
                >
                    <Input.Password />
                </Form.Item>

                <Form.Item name="auth_username" label="Auth Username" rules={[{ required: true, message: 'Please input the auth username' }]}>
                    <Input />
                </Form.Item>

                <Form.Item name="auth_password" label="Auth Password" rules={[{ required: true, message: 'Please input the auth password' }]}>
                    <Input />
                </Form.Item>

                <Form.Item wrapperCol={{ offset: 4, span: 16 }}>
                    <Button type="primary" htmlType="submit">
                        Save
                    </Button>
                </Form.Item>
            </Form>
        </Card>
    );
};


// import { Button, Card, Form, Input, Layout, Select } from "antd";
// import { useDispatch, useSelector } from "react-redux";
// import { useEffect, useState } from "react"
// import { getQueues } from "../Actions/QueueActions";
// import { createSupervisor, getDataByID } from "../Actions/SupervisorAction";
// import { SupervisorReducer } from "../Reducer/SupervisorReducer";
// import { openNotificationWithIcon } from "../Shared/notification";
// import { useHistory, useParams } from "react-router-dom";
// import { handleError } from "../Shared/handleError";

// const tailLayout = {
//     wrapperCol: { offset: 8, span: 16 },
// };

// export const AddSupervisor = () => {

//     const [form] = Form.useForm()
//     const dispatch = useDispatch()
//     const supervisor = useSelector(state => state.SupervisorReducer)
//     let history = useHistory()
//     const { id } = useParams()

//     function onFinish(values) {
//         dispatch(createSupervisor(values))
//     }

//     useEffect(() => {
//         if (supervisor.message) {
//             openNotificationWithIcon('success', supervisor.message)
//             history.push('/supervisor')
//         }
//         else if (supervisor.errMess) {
//             openNotificationWithIcon('error', handleError(supervisor.errMess))
//             console.log(handleError(supervisor.errMess))
//             console.log(supervisor.errMess)
//         }

//     }, [supervisor])

//     return (<Card>
//         <Form
//             form={form}
//             name="form_in_modal"
//             labelCol={{ span: 4 }}
//             wrapperCol={{ span: 16 }}
//             onFinish={onFinish}
//         >

//             <Form.Item
//                 name="name"
//                 label="Name"
//                 rules={[
//                     {
//                         required: true,
//                         message: 'Please input the name',
//                     },
//                 ]}
//             >
//                 <Input />
//             </Form.Item>
//             <Form.Item
//                 name="username"
//                 label="Username"
//                 rules={[
//                     {
//                         required: true,
//                         message: 'Please input the username',
//                     },
//                 ]}
//             >
//                 <Input />
//             </Form.Item>
//             <Form.Item
//                 name="email"
//                 label="E-Mail"
//                 rules={[
//                     {
//                         required: true,
//                         message: 'Please input the email address',
//                     },
//                 ]}
//             >
//                 <Input />
//             </Form.Item>
//             <Form.Item
//                 name="password"
//                 label="Password"
//                 rules={[
//                     {
//                         required: true,
//                         message: 'Please input the password',
//                     },
//                 ]}
//             >
//                 <Input.Password />
//             </Form.Item>
//             <Form.Item
//                 name="password_confirmation"
//                 label="Confirm Password"
//                 rules={[
//                     {
//                         required: true,
//                         message: 'Please input confirm password',
//                     },
//                 ]}
//             >
//                 <Input.Password />
//             </Form.Item>

//             <Form.Item name="auth_username" label="Auth Username" rules={[
//                 {
//                     required: true,
//                     message: 'Please input the auth username',
//                 },
//             ]}>
//                 <Input />
//             </Form.Item>
//             <Form.Item name="auth_password" label="Auth Password" rules={[
//                 {
//                     required: true,
//                     message: 'Please input the auth password',
//                 },
//             ]}>
//                 <Input />
//             </Form.Item>

//             <Form.Item wrapperCol={{ offset: 4, span: 16 }}>
//                 <Button type="primary" htmlType="submit">
//                     Save
//                 </Button>
//             </Form.Item>
//         </Form>
//     </Card>)

// }