import {
    <PERSON><PERSON>,
    Col,
    DatePicker,
    Form,
    Input,
    Modal,
    Row,
    Select,
    Space,
    Spin,
    Table,
} from "antd";
import { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
    getOutboundActivityFiltered,
    getOutboundActivityReport,
    outboundActivityReset,
} from "../../Actions/OutboundActivityActions";
import {
    DownloadOutlined,
    FilterOutlined,
    ReloadOutlined,
    SearchOutlined,
    SettingOutlined,
} from "@ant-design/icons";
import { CSVLink } from "react-csv";
import Highlighter from "react-highlight-words";
import { getCallStatuses } from "../../Actions/CallStatusActions";
import { getAgent } from "../../Actions/AgentActions";

export const OutboundActivityReport = () => {
    const [filterVisible, setFilterVisible] = useState(false);
    const [resetFilter, setResetFilter] = useState(false);
    const agentState = useSelector((state) => state.AgentReducer);
    const dispatch = useDispatch();
    const outboundActivityState = useSelector(
        (state) => state.OutboundActivityReducer
    );
    const [searchText, setSearchText] = useState("");
    const [searchedColumn, setSearchedColumn] = useState("");
    const searchInput = useRef();

    //   useEffect(() => {
    //     dispatch(getOutboundActivityReport());
    //     dispatch(getAgent());
    //   }, []);

    const resetFormFilter = () => {
        dispatch(outboundActivityReset());
        setResetFilter(true);
        form.resetFields()
    };

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({

            setSelectedKeys,
            selectedKeys,
            confirm,
            clearFilters,
        }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={(e) =>
                        setSelectedKeys(e.target.value ? [e.target.value] : [])
                    }
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: "block" }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button
                        onClick={() => handleReset(clearFilters)}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0]);
                            setSearchedColumn(dataIndex);
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined style={{ color: filtered ? "#1890ff" : undefined }} />
        ),
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex]
                    .toString()
                    .toLowerCase()
                    .includes(value.toLowerCase())
                : "",
        onFilterDropdownVisibleChange: (visible) => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: (text) =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: "#ffc069", padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ""}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0]);
        setSearchedColumn(dataIndex);
    };

    const handleReset = (clearFilters) => {
        clearFilters({ confirm: true });
        setSearchText("");
    };

    const columns = [
        {
            title: "Unique ID",
            dataIndex: "uniqueid",
            key: "uniqueid",
        },
        {
            title: "Source",
            dataIndex: "channel",
            key: "channel",
            ...getColumnSearchProps("channel"),
        },
        {
            title: "Destination",
            dataIndex: "dst",
            key: "dst",
            ...getColumnSearchProps("dst"),
        },
        {
            title: "Start Time",
            dataIndex: "start",
            key: "start",
            ...getColumnSearchProps("start"),
        },
        {
            title: "Duration",
            dataIndex: "duration",
            key: "duration",
            ...getColumnSearchProps("duration"),
        },
        {
            title: "Agent ID",
            dataIndex: "channel",
            key: "agent",
            ...getColumnSearchProps("channel"),
        },
        // {
        //     title: "Agent Name",
        //     dataIndex: "name",
        //     key: "name",
        //     ...getColumnSearchProps("name")
        //     // render: (text, record) => {
        //     //     if (agentState.users) {
        //     //         console.log("channel", record?.channel)
        //     //         console.log("Agent State", agentState?.users)
        //     //         let { name } =
        //     //             agentState.users.find((d) => d.auth_username === record.channel) ??
        //     //             "N/A";
        //     //         return <>{name}</>;
        //     //     }
        //     //     return <></>;
        //     // },
        // },
        {
            title: "Status",
            dataIndex: "disposition",
            key: "disposition",
            ...getColumnSearchProps("disposition"),
        },
        {
            title: "Recording",
            dataIndex: "recordingfile",
            key: "recordingfile",
            render: (text, record) =>
                record.recordingfile !== "" ? (
                    <Button
                        block
                        icon={<DownloadOutlined />}
                        target="_blank"
                        type="primary"
                        href={`${process.env.REACT_APP_baseURL}/api/download/${record.recordingfile}`}
                    />
                ) : (
                    ""
                ),
        },
    ];
    const [form] = Form.useForm();
    return (
        <>
            <Table
                columns={columns}
                dataSource={outboundActivityState?.data || []}
                scroll={{ x: true }}
                loading={outboundActivityState.isLoading}
                title={(data) => (
                    <Row>
                        <Col xs={12}>
                            <div style={{ padding: "0.5rem 0 0 1rem" }}>
                                <span>Outbound Activity Report</span>
                            </div>
                        </Col>
                        <Col xs={12}>
                            <div style={{ textAlign: "right" }}>
                                <Space>
                                    <Button
                                        type="primary"
                                        danger
                                        onClick={() => resetFormFilter()}
                                        icon={<ReloadOutlined />}
                                    >
                                        Reset Filter
                                    </Button>
                                    <Button
                                        onClick={() => setFilterVisible(true)}
                                        icon={<FilterOutlined />}
                                    >
                                        Filter
                                    </Button>
                                    <CSVLink
                                        style={{ color: "#fff" }}
                                        target="_blank"
                                        filename="OutboundActivityReport.csv"
                                        data={outboundActivityState.data}

                                    >
                                        <Button disabled={outboundActivityState.data.length == 0} type="primary" icon={<DownloadOutlined />}>


                                            Download
                                        </Button>
                                    </CSVLink>
                                </Space>
                            </div>
                        </Col>
                    </Row>
                )}
            />
            <OutboundActivityFilter
                form={form}
                visible={filterVisible}
                setVisible={setFilterVisible}
                resetField={resetFilter}
                buttonLoading={outboundActivityState.isLoading}
            />
        </>
    );
};

const OutboundActivityFilter = ({
    visible,
    setVisible,
    resetField,
    buttonLoading,
    form
}) => {

    const agentState = useSelector((state) => state.AgentReducer);
    const callState = useSelector((state) => state.CallStatusReducer);
    const dispatch = useDispatch();

    useEffect(() => {
        dispatch(getCallStatuses());
        dispatch(getAgent());
    }, []);

    useEffect(() => form.resetFields(), [resetField]);

    return (
        // <Spin
        //   spinning={agentState.isLoading || callState.isLoading}
        //   indicator={<SettingOutlined />}
        // >
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            title="Outbound Activity Filter"
            size="small"
            onOk={() =>
                form
                    .validateFields()
                    .then((data) => dispatch(getOutboundActivityFiltered(data)))
                    .then(() => setVisible(false))
                    .catch((e) => console.log(e))
            }
            okText="Submit"
            oKButtonProps={{
                loading: buttonLoading,
            }}
        >
            <Form layout="vertical" form={form}>
                <Form.Item name="range" label="Date Range">
                    <DatePicker.RangePicker showTime />
                </Form.Item>
                <Form.Item name="agent" label="Agent">
                    <Select mode="multiple"
                        filterOption={(input, option) =>
                            option.children.toLowerCase().includes(input.toLowerCase())
                        }
                    >
                        {agentState?.users &&
                            agentState?.users.map((value, index) => (
                                <Select.Option value={value?.auth_username} key={value?.username}>
                                    {value?.name}
                                </Select.Option>
                            ))}
                    </Select>
                </Form.Item>
                <Form.Item name="dst" label="Destination">
                    <Input />
                </Form.Item>
                <Form.Item name="call_status" label="Call Status">
                    <Select>
                        {callState.statuses &&
                            callState.statuses.map((value, index) => (
                                <Select.Option key={index} value={value.disposition}>
                                    {value.disposition}
                                </Select.Option>
                            ))}
                    </Select>
                </Form.Item>
            </Form>
        </Modal>
        // </Spin>
    );
};
