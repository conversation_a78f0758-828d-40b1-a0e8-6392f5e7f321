import { useDispatch, useSelector } from "react-redux";
import { Button, Form, Input, Modal, Row, Space, Spin, Table } from "antd";
import { allowModule, assignPermission, assignRoleToUser, createRole, deleteRole, getRole, updateRole } from "../Actions/RoleActions";
import { openNotificationWithIcon } from "../Shared/notification";
import { DeleteOutlined, DesktopOutlined, EditOutlined, GoldOutlined, PlusOutlined } from "@ant-design/icons";
import { useState, useEffect } from "react";
import AddPermission from "../Components/Role/AddPermission";
import apiClient from "../Shared/apiClient";
import AssignQueue from "../Components/Role/AssignQueue";
import { getQueues } from "../Actions/QueueActions";

const Role = () => {
    const role = useSelector(state => state.RoleReducer)
    // const { queues } = useSelector(state => state.QueueReducer)
    const [queues, setQueues] = useState([])

    const permission = useSelector(state => state.PermissionReducer)
    const [showEdit, setShowEdit] = useState(false)
    const [form] = Form.useForm()
    const [record, setRecord] = useState(null)
    const [showCreate, setShowCreate] = useState(false)
    const [createVisible, setCreateVisible] = useState(false)
    const [assignQueueVisible, setAssignQueueVisible] = useState(false)
    const dispatch = useDispatch()

    const fetchAllQueus = () => {
        apiClient.get('api/getAllQueues').then((res) => {
            setQueues(res.data)
        })
    }

    useEffect(() => {
        dispatch(getRole())
        // dispatch(getQueues())
        fetchAllQueus()
    }, [])

    useEffect(() => {
        if (role.errMess) {
            openNotificationWithIcon('error', role.errMess)
        }
    }, [role.errMess])

    useEffect(() => {
        if (role.message) {
            openNotificationWithIcon('success', role.message)
            dispatch(getRole())
        }
    }, [role.message])

    const handleSubmit = values => {
        setShowEdit(false)
        dispatch(updateRole(values, record.id))
    }

    const handleCreate = values => {
        dispatch(createRole(values))
        setShowCreate(false)
    }

    const handleShowCreate = () => {
        setRecord(role.role)
        setShowCreate(true)
    }

    const fetchModules = () => {
        apiClient.get('api/allowed_modules').then((r) => {
            console.log("dd", r.data)
        })
            .catch((e) => {
                console.log("Error", e)
            })
    }

    function onCreate(values) {
        dispatch(assignPermission(record, values))
        setCreateVisible(false)
        // fetchModules()
        dispatch(allowModule());
        console.log("modules check", role.modules)
    }

    useEffect(() => {
        console.log("modules check", role.modules)
    }, [])

    function assignRole(values) {
        dispatch(assignRoleToUser(record.id, values))
        // setCreateVisible(false)
    }

    // queue assign 
    const handleAssignQueue = (values) => {
        apiClient.post('/api/assign-queue-role', values).then(res => {
            openNotificationWithIcon('success', res.data?.message)
            setAssignQueueVisible(false)
            setRecord(null)
            // console.log(values)
            // setRecord(null)
        }).catch((err) => {
            openNotificationWithIcon('error', err?.response?.data || "Network Error")
        })
    }

    return (
        <Spin spinning={role.isLoading}>
            <AssignQueue queues={queues || []} record={record} isLoading={false} setVisible={setAssignQueueVisible} visible={assignQueueVisible} onCreate={handleAssignQueue} />
            <AddPermission record={record} isLoading={permission.isLoading} setVisible={setCreateVisible} onCreate={onCreate} visible={createVisible} />
            <Modal
                centered
                title="Edit Role"
                visible={showEdit}
                destroyOnClose={true}
                closable={true}
                okText="Update"
                onOk={() => {
                    form.validateFields()
                        .then(values => handleSubmit(values))
                        .catch(error => console.log(error))
                }}
                onCancel={() => {
                    setShowEdit(false)
                }}
            >
                <Form
                    form={form}
                    layout="inline"
                    name="form_in_modal"
                    initialValues={record}
                >
                    <Form.Item
                        name="name"
                        kay="1"
                        label="Role Name"
                    >
                        <Input />
                    </Form.Item>

                </Form>
            </Modal>
            <Modal
                centered
                title="Add Role"
                visible={showCreate}
                destroyOnClose={true}
                closable={true}
                okText="Submit"
                onOk={() => {
                    form.validateFields()
                        .then(values => handleCreate(values))
                        .catch(error => console.log(error))
                }}
                onCancel={() => {
                    setShowCreate(false)
                }}
            >
                <Form
                    form={form}
                    layout="inline"
                    name="form_in_modal"
                >
                    <Row gutter={[16, 24]}>
                        <Form.Item
                            name="name"
                            kay="2"
                            label="Role Name"
                        >
                            <Input />
                        </Form.Item>
                    </Row>
                </Form>
            </Modal>
            <Space style={{ marginBottom: 16 }}>
                <Button onClick={handleShowCreate} icon={<PlusOutlined />} type="primary">Add New</Button>
            </Space>
            <Table scroll={{ x: 800 }} size="small" bordered dataSource={role.role}>
                <Table.Column dataIndex="name" key="name" title="Name" />
                {/*<Table.Column dataIndex="created_at" key="created_at" title="CreatedAt" />*/}
                {/*<Table.Column dataIndex="updated_at" key="updated_at" title="UpdateAt" />*/}
                <Table.Column align="center" title="Actions" render={(text, record) => (
                    <Space>
                        {/*<Button onClick={() => {*/}
                        {/*    setRecord(record)*/}
                        {/*    setAssignRoleModal(true)*/}
                        {/*}} type="outlined" icon={<DesktopOutlined />}>Role Assign To User</Button>*/}

                        <Button onClick={() => {
                            setRecord(record)
                            setCreateVisible(true)
                        }} type="outlined" icon={<DesktopOutlined />}>Assign Permission</Button>

                        <Button onClick={() => {
                            setRecord(record)
                            setAssignQueueVisible(true)
                        }} type="outlined" icon={<GoldOutlined />}>Assign Queue</Button>

                        <Button onClick={() => {
                            form.setFieldsValue(record)
                            setRecord(record)
                            setShowEdit(true)
                        }} icon={<EditOutlined />} type="primary">Edit</Button>

                        <Button onClick={() => {
                            dispatch(deleteRole(record.id))
                        }} icon={<DeleteOutlined />} type="danger">Delete</Button>
                    </Space>
                )} />
            </Table>
        </Spin>
    )
}

export default Role