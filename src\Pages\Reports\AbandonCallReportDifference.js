import { useDispatch, useSelector } from "react-redux";
import { useEffect, useRef, useState } from "react";
import { getTrunkPerHourReport } from "../../Actions/TrunkPerHoursActions";
import { openNotificationWithIcon } from "../../Shared/notification";
import { Button, Card, DatePicker, Form, Input, Modal, Select, Space, Spin, Table, Typography } from "antd";
import { CloudDownloadOutlined, DownloadOutlined, FilterOutlined, ReloadOutlined, SearchOutlined, SyncOutlined } from "@ant-design/icons";
import { CSVLink } from "react-csv";
import { getData, resetDifference } from "../../Actions/AbandonCallReportDifferenceAction"
import apiClient from "../../Shared/apiClient";
import Highlighter from "react-highlight-words";

export const AbandonCallReportDifference = () => {

    const [visible, setVisible] = useState(false)
    const [resetFilter, setResetFilter] = useState(false);
    const [queues, setQueues] = useState([]);
    const [form] = Form.useForm()

    useEffect(() => {

        apiClient.get('api/queue').then((res) => {
            setQueues(res?.data);
        })
    }, [])

    const AbandonCallReportDifferenceReducer = useSelector(state => state.AbandonCallReportDifferenceReducer)
    const dispatch = useDispatch()

    const resetFormFilter = () => {
        setResetFilter(true)
        form.resetFields();
        dispatch(resetDifference())
    }

    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')

    const searchInput = useRef()

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }

    const columns = [
        {
            title: "Abandon DateTime",
            dataIndex: "abandon_time",
            key: "Abandon DateTime"
        },
        {
            title: "Called Back/ Called In",
            dataIndex: "followupTime",
            key: "Called Back/ Called In"
        },
        {
            title: "Difference",
            dataIndex: "difference",
            key: "Differnce"
        },
        {
            title: "Call Type",
            dataIndex: "CallDirection",
            key: "CallDirection"
        },
        {
            title: "Number",
            dataIndex: "number",
            key: "number",
            ...getColumnSearchProps('number')
        }
    ]

    const exportHeaders = [
        {
            label: "Abandon DateTime",
            key: "abandon_time",
        },
        {
            label: "Called Back/ Called In",
            key: "followupTime",
        },
        {
            label: "Difference",
            key: "difference"
        },

        {
            label: "Call Type",
            key: "CallDirection"
        },
        {
            label: "Number",
            key: "number",
        }
    ]
    return (
        <div>
            <SearchModal
                setVisible={setVisible}
                queues={queues}
                form={form}
                setQueues={setQueues}
                visible={visible}
                confirmLoading={AbandonCallReportDifferenceReducer.isLoading}
                resetFilter={resetFilter} />
            <Table
                title={() => <Typography.Text>
                    Abandon Call Report Difference
                    <span style={{ float: 'right' }}>
                        <Space>
                            <Button
                                danger
                                type="primary"
                                icon={<ReloadOutlined />}
                                onClick={() => {
                                    resetFormFilter();
                                }}
                            >Reset Filter</Button>
                            <Button icon={<FilterOutlined />} onClick={() => setVisible(true)}>Filter</Button>
                            <CSVLink headers={exportHeaders} data={AbandonCallReportDifferenceReducer?.data} filename="AbandonCallReportDifferenceReducer.csv">
                                <Button
                                    disabled={AbandonCallReportDifferenceReducer?.data?.length == 0}
                                    type={"primary"}
                                    target="_blank"
                                    icon={<DownloadOutlined />}>
                                    Download
                                </Button>
                            </CSVLink>
                        </Space>
                    </span>
                </Typography.Text>}
                dataSource={AbandonCallReportDifferenceReducer?.data || []}
                columns={columns}
                scroll={{ x: 1000 }}
                bordered
                loading={{ spinning: AbandonCallReportDifferenceReducer?.isLoading, indicator: <SyncOutlined spin /> }}
            />
        </div>)
}

const SearchModal = ({ visible, form, setVisible, queues, confirmLoading, resetFilter }) => {


    const dispatch = useDispatch()
    const [date, setDate] = useState()

    // const handleOk = () => {
    //     console.log("handleOk")
    // }
    useEffect(() => {
        if (resetFilter) (
            form.resetFields()
        )
    }, [resetFilter])

    const onChange = (date, dateString) => setDate(dateString)

    return (
        <Modal title="Abandon Call Report Difference Filter Modal" visible={visible} onCancel={() => setVisible(false)} confirmLoading={confirmLoading}
            onOk={() => form.validateFields().then(data => dispatch(getData({ ...data, date }))).then(() => setVisible(false))
                .catch(e => console.log(e))
            }>
            <Form form={form}
                layout="vertical"
            >
                <Form.Item name="date" label={"Date Range"} rules={[{ required: true, message: 'Please input date!' }]}>
                    <DatePicker size={"large"} style={{ width: "100%" }} onChange={onChange} />
                </Form.Item>
                <Form.Item
                    label="Queue"
                    name="queue"
                >
                    <Select placeholder="Queue Option">
                        {/* <Select.Option>Select Queue</Select.Option> */}
                        {queues.map((elm, index) => {
                            return (
                                <Select.Option value={elm?.name}>
                                    {elm?.name}
                                </Select.Option>)

                        })}
                    </Select>
                </Form.Item>
                {/*<Form.Item name="number" label={"Number"}>*/}
                {/*    <Input />*/}
                {/*</Form.Item>*/}
            </Form>
        </Modal>
    )
}