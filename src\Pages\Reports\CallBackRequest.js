import { useDispatch, useSelector } from "react-redux";
import { openNotificationWithIcon } from "../../Shared/notification";
import { Button, DatePicker, Form, Input, Modal, Select, Space, Spin, Table } from "antd";
import { DownloadOutlined, FilterOutlined, ReloadOutlined, SearchOutlined, SyncOutlined } from "@ant-design/icons";
import { useEffect, useRef, useState } from "react";
import { getCallBackRequest, callBackRequestReset } from "../../Actions/CallBackRequestAction";
import apiClient from "../../Shared/apiClient";
import Highlighter from "react-highlight-words";

export const CallBackRequest = () => {
    const [form] = Form.useForm();
    const callBackRequestReducer = useSelector(state => state.CallBackRequestReducer);
    const [pagination, setPagination] = useState({ current: 1, pageSize: 50 });
    const [filterValues, setFilterValues] = useState({});
    const [loading, setLoading] = useState(false);
    const [searchText, setSearchText] = useState('');
    const [searchedColumn, setSearchedColumn] = useState('');
    const [filterVisible, setFilterVisible] = useState(false);
    const searchInput = useRef(null);
    const dispatch = useDispatch();
    const [queues, setQueues] = useState([])

    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data))
    }, [])

    useEffect(() => {
        // Initial load with default pagination
        dispatch(getCallBackRequest({}, pagination));
    }, []);

    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search ${dataIndex}`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]?.toString().toLowerCase().includes(value.toLowerCase()),
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0]);
        setSearchedColumn(dataIndex);
    };

    const handleReset = clearFilters => {
        clearFilters();
        setSearchText('');
        setSearchedColumn('');
    };

    const handleTableChange = (pagination, filters, sorter) => {
        const newPagination = {
            current: pagination.current,
            pageSize: pagination.pageSize
        };
        
        setPagination(newPagination);
        dispatch(getCallBackRequest(filterValues, newPagination));
    };

    const handleFilterSubmit = (values) => {
        // Create a copy of the filter values
        const filters = { ...values };
        
        // Format date range if it exists
        if (values.date_range && values.date_range[0] && values.date_range[1]) {
            filters.start_date = values.date_range[0].format('YYYY-MM-DD');
            filters.end_date = values.date_range[1].format('YYYY-MM-DD');
            delete filters.date_range; // Remove the original date_range
        }
        
        setFilterValues(filters);
        dispatch(getCallBackRequest(filters, pagination));
        setFilterVisible(false);
    };

    const handleResetFilters = () => {
        form.resetFields();
        setFilterValues({});
        dispatch(getCallBackRequest({}, pagination));
    };

    const exportToCSV = () => {
        setLoading(true);
        apiClient.post('/api/report/export', filterValues, { 
            responseType: 'blob' 
        })
        .then(response => {
            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', 'callback_requests.csv');
            document.body.appendChild(link);
            link.click();
            link.remove();
        })
        .catch(error => {
            openNotificationWithIcon('error', 'Failed to export data');
        })
        .finally(() => setLoading(false));
    };

    const columns = [
        {
            title: 'Caller ID',
            dataIndex: 'caller_id',
            key: 'caller_id',
            ...getColumnSearchProps('caller_id')
        },
        {
            title: 'Queue',
            dataIndex: 'queue',
            key: 'queue',
            ...getColumnSearchProps('queue')
        },
        {
            title: 'Agent',
            dataIndex: 'agent',
            key: 'agent',
            ...getColumnSearchProps('agent')
        },
        {
            title: 'Status',
            dataIndex: 'status',
            key: 'status',
            render: status => status ? 'Completed' : 'Pending'
        },
        {
            title: 'Request Date/Time',
            dataIndex: 'request_time',
            key: 'request_time',
            render: time => new Date(time).toLocaleString()
        },
        {
            title: 'Answered Date/Time',
            dataIndex: 'answered_date',
            key: 'answered_date',
            render: date => date ? new Date(date).toLocaleString() : 'Not answered'
        }
    ];

    return (
        <>
            <Table
                title={() => (
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>Callback Requests</span>
                        <Space>
                            <Button
                                danger
                                type="primary"
                                icon={<ReloadOutlined />}
                                onClick={handleResetFilters}
                            >
                                Reset Filters
                            </Button>
                            <Button
                                icon={<FilterOutlined />}
                                onClick={() => setFilterVisible(true)}
                            >
                                Filters
                            </Button>
                            <Button
                                type="primary"
                                icon={<DownloadOutlined />}
                                onClick={exportToCSV}
                                loading={loading}
                                disabled={!callBackRequestReducer.data?.data?.length}
                            >
                                Export
                            </Button>
                        </Space>
                    </div>
                )}
                columns={columns}
                dataSource={callBackRequestReducer.data?.data || []}
                loading={callBackRequestReducer.isLoading}
                pagination={{
                    current: callBackRequestReducer.data?.current_page || 1,
                    pageSize: callBackRequestReducer.data?.per_page || 50,
                    total: callBackRequestReducer.data?.total || 0,
                    showSizeChanger: true,
                }}
                onChange={handleTableChange}
                rowKey="id"
                scroll={{ x: true }}
            />

            <Modal
                title="Filter Callback Requests"
                visible={filterVisible}
                onCancel={() => setFilterVisible(false)}
                onOk={() => form.submit()}
            >
                <Form
                    form={form}
                    layout="vertical"
                    onFinish={handleFilterSubmit}
                >
                    <Form.Item name="date_range" label="Date Range">
                        <DatePicker.RangePicker style={{ width: '100%' }} />
                    </Form.Item>
                    <Form.Item name="caller_id" label="Caller ID">
                        <Input placeholder="Search by caller ID" />
                    </Form.Item>
                    <Form.Item name="queue" label="Queue">
                        <Select placeholder="Select Queue" style={{ width: '100%' }} allowClear>
                            {queues && queues.map((queue, index) => <Select.Option key={index} value={queue.name}>
                                {queue.name}
                            </Select.Option>)}
                        </Select>
                    </Form.Item>
                    <Form.Item name="status" label="Status">
                        <Select placeholder="Select status" allowClear>
                            <Select.Option value="1">Completed</Select.Option>
                            <Select.Option value="0">Pending</Select.Option>
                        </Select>
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
};