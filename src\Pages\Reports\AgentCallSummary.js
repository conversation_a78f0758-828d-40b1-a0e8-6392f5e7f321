import { But<PERSON>, Card, DatePicker, Form, Input, Modal, Select, Space, Spin, Table } from "antd";
import { CloseCircleOutlined, DownloadOutlined, FilterOutlined } from "@ant-design/icons";
import React, { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { CSVDownload, CSVLink } from "react-csv";
import { getCallStatuses } from "../../Actions/CallStatusActions";
import { getAgent } from "../../Actions/AgentActions";
import { openNotificationWithIcon } from "../../Shared/notification"
import Highlighter from 'react-highlight-words'
import { SearchOutlined } from '@ant-design/icons'
import { getMinutesOfMeetingFilteredReport, MinutesOfMeetingReport } from "../../Actions/CallDetailReportsActions";
import { agentCallSummary, agentCallSummaryFiltered, agentCallSummaryFilteredOutbound, agentCallSummaryReset } from "../../Actions/AgentCallReportActions";
import { AgentCallReportReducer } from "../../Reducer/AgentCallReportReducer";

export const AgentCallSummary = () => {

    const [filterVisible, setFilterVisible] = useState(false)
    const [resetFilter, setResetFilter] = useState(false)
    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const dispatch = useDispatch()
    const callReportState = useSelector(state => state.AgentCallReportReducer)

    useEffect(() => {
        if (callReportState.errMess !== '') openNotificationWithIcon('error', callReportState.errMess)
    }, [callReportState.errMess])

    // useEffect(() => dispatch(agentCallSummary()), [])

    useEffect(() => setResetFilter(false), [filterVisible])

    const resetFormFilter = () => {
        dispatch(agentCallSummaryReset())

        setResetFilter(true)
    }

    const searchInput = useRef()

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }

    const columns = [
        {
            title: 'ext1',
            dataIndex: 'ext1',
            key: 'ext1'
        },
        {
            title: 'name',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: 'Total Call Attempts',
            dataIndex: 'Total_Call_Attempts',
            key: 'Total_Call_Attempts',
            ...getColumnSearchProps('Total_Call_Attempts')
        },
        {
            title: 'Total Unique Customers',
            dataIndex: 'Total_Unique_Customers',
            key: 'Total_Unique_Customers',
            ...getColumnSearchProps('Total_Unique_Customers')
        },
        {
            title: 'Total Answered Calls',
            dataIndex: 'Total_Answered_Calls',
            key: 'Total_Answered_Calls',
            ...getColumnSearchProps('Total_Answered_Calls')
        },
        {
            title: 'Total Unique Answered Calls',
            dataIndex: 'Total_Unique_Answered_Calls',
            key: 'Total_Unique_Answered_Calls',
            ...getColumnSearchProps('Total_Unique_Answered_Calls')
        },
        {
            title: 'Total Talk Time',
            dataIndex: 'Total_Talk_Time',
            key: 'Total_Talk_Time',
            ...getColumnSearchProps('Total_Talk_Time')
        },
        {
            title: 'Total',
            dataIndex: 'Total',
            key: 'Total',
            ...getColumnSearchProps('Total')
        },

    ]

    const downloadReport = () => {
        console.log('download report')
    }

    return (
        <>
            <Table loading={callReportState.isLoading} title={v =>
                <>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        Agent Call Summary Outbound
                        <Space>
                            <Button onClick={() => resetFormFilter()} type="primary" danger icon={<CloseCircleOutlined />}>Reset Filter</Button>
                            <Button onClick={() => setFilterVisible(true)} icon={<FilterOutlined />}>Filter</Button>
                            {callReportState.outbound &&
                                <CSVLink filename="AgentCallReport.csv" data={callReportState.outbound}>
                                    <Button
                                        disabled={callReportState.outbound.length == 0}
                                        type="primary"
                                        onClick={<CSVDownload
                                            data={callReportState.outbound} />}
                                        target="_blank" icon={<DownloadOutlined />}>
                                        Download CSV
                                    </Button>
                                </CSVLink>
                            }
                        </Space>
                    </div>
                </>

            }
                dataSource={callReportState.outbound} columns={columns} scroll={{ x: 1100 }} bordered />
            <AgentCallReportFilter resetField={resetFilter} visible={filterVisible} setVisible={setFilterVisible} />
        </>
    )
}

const AgentCallReportFilter = ({ visible, setVisible, resetField }) => {

    const [form] = Form.useForm()
    const agentState = useSelector(state => state.AgentReducer)
    const callState = useSelector(state => state.CallStatusReducer)
    const dispatch = useDispatch()

    useEffect(() => {
        dispatch(getCallStatuses())
        dispatch(getAgent())
    }, [])

    useEffect(() => form.resetFields(), [resetField])

    return (
        <Modal
            title="Filter"
            onCancel={() => {
                setVisible(false)
            }}
            visible={visible}
            onOk={() => {
                form.validateFields()
                    .then(values => {
                        dispatch(agentCallSummaryFilteredOutbound(values));
                        setVisible(false)
                        form.resetFields()
                    })
                    .catch(e => console.log(e))
            }}
        >
            <Spin spinning={callState.isLoading || agentState.isLoading}>
                <Form form={form} layout="vertical">
                    <Form.Item name="range" label="Date Time">
                        <DatePicker.RangePicker showTime />
                    </Form.Item>
                    <Form.Item name="agent" label="Agent">
                        <Select
                        >
                            {agentState.users && agentState.users.map((value, index) => <Select.Option value={value.auth_username} key={value.username}>
                                {value.name}
                            </Select.Option>)}
                        </Select>
                    </Form.Item>
                </Form>
            </Spin>
        </Modal>
    )
}

