import Echo from "laravel-echo";
import Pusher from "pusher-js";

window.Pusher = Pusher;

const echo = new Echo({
    broadcaster: "pusher",
    key: process.env.REACT_APP_PUSHER_KEY ?? 'local',
    cluster: process.env.REACT_APP_PUSHER_CLUSTER ?? 'mt1',
    wsHost: process.env.REACT_APP_SOCKET_APP_URL ?? '', //solutionsv3.tclcontactplus.com
    wsPort: process.env.REACT_APP_SOCKET_PORT ?? 6001,
    forceTLS: process.env.REACT_APP_PUSHER_FORCETLS === true, // Disable TLS for local development
    disableStats: process.env.REACT_APP_PUSHER_DISABLE_STATS === true,
    encrypted: process.env.REACT_APP_PUSHER_ENCRYPTED === true,
    authEndpoint: process.env.REACT_APP_PUSHER_AUTH_ENDPOINT ?? '', //https://solutionsv3.tclcontactplus.com/backend/broadcasting/auth
    enabledTransports: ['ws', 'wss'],
    enableClientEvents: true,
});

export default echo;


// import Echo from "laravel-echo";
// import Pusher from "pusher-js";
// window.Pusher = Pusher;
// const initializeEcho = (token) => {
//     return new Echo({
//         broadcaster: 'pusher',
//         // key: 'local',
//         // cluster: 'mt1',
//         // wsHost: process.env.SOCKET_APP_URL || '127.0.0.1',
//         // wsPort: process.env.SOCKET_PORT || 6001,
//         // forceTLS: false, // Disable TLS for local development
//         // disableStats: true,
//         // encrypted: false,
//         // authEndpoint: 'http://127.0.0.1:8000/broadcasting/auth',
//         key: process.env.REACT_APP_PUSHER_KEY ?? 'local',
//         cluster: process.env.REACT_APP_PUSHER_CLUSTER ?? 'mt1',
//         wsHost: process.env.REACT_APP_SOCKET_APP_URL ?? 'solutionsv3.tclcontactplus.com',
//         wsPort: process.env.REACT_APP_SOCKET_PORT ?? 6001,
//         forceTLS: process.env.REACT_APP_PUSHER_FORCETLS === true, // Disable TLS for local development
//         disableStats: process.env.REACT_APP_PUSHER_DISABLE_STATS === true,
//         encrypted: process.env.REACT_APP_PUSHER_ENCRYPTED === true,
//         authEndpoint: process.env.REACT_APP_PUSHER_AUTH_ENDPOINT ?? 'https://solutionsv3.tclcontactplus.com/backend/broadcasting/auth',
//         auth: {
//             headers: {
//                 Authorization: `Bearer ${token}`,
//             },
//         },
//         enabledTransports: ['ws', 'wss'],
//         enableClientEvents: true,
//     });
// };
// export default initializeEcho;
// // const echo = new Echo({
//     broadcaster: 'pusher',
//     key: 'local',
//     cluster: 'mt1',
//     wsHost: process.env.SOCKET_APP_URL || '127.0.0.1',
//     wsPort: process.env.SOCKET_PORT || 6001,
//     forceTLS: false, // Disable TLS for local development
//     disableStats: true, // Disable stats collection
//     encrypted: false,
//     authEndpoint: 'http://127.0.0.1:8000/broadcasting/auth', // Laravel authentication endpoint
//     auth: {
//         headers: {
//             Authorization: `Bearer ${sessionStorage.getItem('agent_token')}`, // Pass your auth token
//         },
//     },
//     enabledTransports: ['ws', 'wss'],
//     enableClientEvents: true,
// });
// export default echo;