import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, Card, DatePicker, Form, Select, Table, Input, Space, Modal } from "antd";
import { CloudDownloadOutlined, FilterOutlined, ReloadOutlined, SearchOutlined } from "@ant-design/icons";
import apiClient from "../../Shared/apiClient";
import moment from "moment";
import { CSVLink } from "react-csv";
import Highlighter from "react-highlight-words";
const { RangePicker } = DatePicker;
const dateFormat = "YYYY-MM-DD";

const WorkCodeWiseReport = () => {
    const [form] = Form.useForm();
    const [data, setData] = useState([]);
    const [columns, setColumns] = useState([]);
    const [to, setTo] = useState();
    const [from, setFrom] = useState();
    const [loading, setLoading] = useState(false);
    const [mainCheck, setMainCheck] = useState(true);
    const [queueField, setQueueField] = useState("");

    const [selectedQueue, setSelectedQueue] = useState([]);

    const [queues, setQueues] = useState([]);

    const [realQueues, setRealQueues] = useState([]);

    useEffect(() => {
        if (from && to && queueField) setMainCheck(false)
    }, [from, to, queueField])

    const onDateChange = (date, dateString) => {
        setFrom(moment(dateString[0]));
        setTo(moment(dateString[1]));
    };

    const handleSelectChange = (selected) => {
        if (selected.includes("select_all")) {
            // form.setFieldsValue({
            //     queues: queues.map((q) => q.name),
            // });
            setSelectedQueue(queues.map((q) => q.name))
        }
        else {
            // form.setFieldsValue({
            //     queues: selected,
            // });
            setSelectedQueue(selected)
        }
    };

    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const searchInput = useRef();
    const [showFilter, setShowFilter] = useState(false)
    const [resetFilter, setResetFilter] = useState(false)

    const resetFormFilter = () => {
        // dispatch(getTrunkPerHourReport())
        // dispatch(trunkPerHourReset())
        // console.log(new Date().toLocaleString() + '')
        setData([])
        form.resetFields()
        setResetFilter(true)
    }


    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }





    const sharedProps = {
        mode: 'multiple',
        style: {
            width: '100%',
        },
        selectedQueue,
        placeholder: 'Select Item...',
        maxTagCount: 'responsive',
    };

    const fetchData = (payload = {}) => {
        setLoading(true);
        apiClient
            .post(`api/report/workcode-agent-wise`, payload)
            .then((res) => {
                if (res.data) {
                    setLoading(false);
                    setMainCheck(true)
                    setFrom(null)
                    setTo(null)
                    setSelectedQueue([])
                    setQueueField("")
                    form.resetFields();
                    setData(res.data.data);
                    setColumns(
                        res.data?.columns?.map((col) => {
                            return {
                                title: <div style={{ textTransform: "capitalize" }}>{col}</div>,
                                dataIndex: col,
                                key: col,
                                width: "200px",
                                ...getColumnSearchProps(col)

                            };
                        })
                    );
                }
            })
            .catch((err) => {
                console.log(err)
                setLoading(false)
            });
    };

    const fetchData2 = (payload = {}) => {

        setLoading(true);
        apiClient
            .post(`api/getAgentsForFilter`, payload)
            .then((res) => {
                if (res.data) {
                    setQueues(res.data);
                }
            })
            .catch((err) => console.log(err));
        setLoading(false);
    };


    useEffect(() => {
        if (queueField.length > 0) {
            fetchData2({ start: from?._i, end: to?._i, queue: queueField })
        };
    }, [queueField]);

    useEffect(() => {
        apiClient.get('/api/queue').then((res) => {
            setRealQueues(res.data)
        }).catch(err => console.log(err.response))
    }, [])

    const fetchReport = () => {
        if (selectedQueue.length > 0) {
            fetchData({ start: from?._i, end: to?._i, agents: selectedQueue, queue: queueField });
        }
        if (selectedQueue.length == 0 || selectedQueue.length < 0) {
            fetchData({ start: from?._i, end: to?._i, queue: queueField });
        }
    };

    return (
        <>
            <Card
            // title="Work Code Agent Wise"
            // style={{ background: "#fff", borderRadius: "2px", padding: 10 }}
            // extra={
            //     <>
            //         <Form
            //             form={form}
            //             layout="inline"
            //             size="large"
            //             style={{ marginTop: "3px" }}
            //         >
            //             <Form.Item name={"picker"}>
            //                 <RangePicker format={dateFormat} onChange={onDateChange} />
            //             </Form.Item>
            //             <Form.Item name={"real_queues"}>
            //                 <Select
            //                     size="default"
            //                     placeholder="Select Queues"
            //                     value={queueField}
            //                     onChange={(e) => {
            //                         setQueueField(e)
            //                     }}
            //                     style={{ marginRight: "10px", minWidth: "130px" }}
            //                 >

            //                     {realQueues.length > 0 &&
            //                         realQueues.map((value, index) => (
            //                             <Select.Option key={index} value={value.name}>
            //                                 {value.name}
            //                             </Select.Option>
            //                         ))}
            //                 </Select>
            //             </Form.Item>

            //             <Form.Item name={"queues"} >
            //                 <Select
            //                     {...sharedProps}
            //                     // size="default"
            //                     placeholder="Agents"
            //                     mode="multiple"
            //                     value={selectedQueue}
            //                     onChange={(e) => handleSelectChange(e)}
            //                     style={{ marginRight: "10px", minWidth: "130px" }}
            //                 >
            //                     <Select.Option
            //                         key="Select All"
            //                         value={"select_all"}
            //                         label="Select All"
            //                     >
            //                         Select All
            //                     </Select.Option>

            //                     {/* <Option key="Select All">Select All</Option> */}
            //                     {queues.length > 0 &&
            //                         queues.map((value, index) => (
            //                             <Select.Option key={index} value={value.name}>
            //                                 {value.name}
            //                             </Select.Option>
            //                         ))}
            //                 </Select>
            //             </Form.Item>


            //             <Form.Item>
            //                 <Button
            //                     size="large"
            //                     onClick={fetchReport}
            //                     disabled={mainCheck}
            //                     style={{ marginRight: "10px" }}
            //                 >
            //                     Fetch Report
            //                 </Button>
            //             </Form.Item>



            //             <Form.Item>
            //                 <CSVLink data={data || []} filename="WorkCodeAgentWise.csv">
            //                     <Button
            //                         size="large"
            //                         disabled={data.length === 0}
            //                         icon={<CloudDownloadOutlined />}
            //                     >
            //                         Export CSV
            //                     </Button>
            //                 </CSVLink>
            //             </Form.Item>
            //         </Form>
            //     </>
            // }
            >
                <Table
                    title={data => <>
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            {`Work Code Agent Wise Report`}
                            <Space>
                                <Button onClick={() => resetFormFilter()} type="danger" icon={<ReloadOutlined />}>Reset Filter</Button>
                                <Button onClick={() => setShowFilter(true)} icon={<FilterOutlined />}>Filter</Button>
                                <CSVLink data={data || []} filename="WorkCodeAgentWise.csv">
                                    <Button
                                        size="large"
                                        disabled={data.length === 0}
                                        icon={<CloudDownloadOutlined />}
                                    >
                                        Download
                                    </Button>
                                </CSVLink>
                            </Space>

                        </div>
                    </>}
                    loading={loading}
                    columns={columns}
                    dataSource={data}
                    pagination={false}
                    size="large"
                    bordered
                    scroll={{
                        x: "calc(700px + 50%)",
                        y: 360,
                    }}
                    rowKey="Workcode"
                />
            </Card>

            <WorkCodeWiseFilter
                form={form}
                resetFilter={resetFilter}
                visible={showFilter}
                fetchReport={fetchReport}
                sharedProps={sharedProps}
                // onChange={onChange}
                // onDateChange={onDateChange}
                selectedQueue={selectedQueue}
                // selectedType={selectedType}
                // onSearch={onSearch}
                // month={month}
                queues={queues}
                realQueues={realQueues}
                queueField={queueField}
                setQueueField={setQueueField}
                handleSelectChange={handleSelectChange}
                onDateChange={onDateChange}
                dateFormat={dateFormat}
                // setselectedType={setselectedType}
                setVisible={setShowFilter}
            />

        </>
    );
};

export const WorkCodeWiseFilter = ({ form, fetchReport, sharedProps, selectedQueue, queueField, setQueueField, queues, handleSelectChange, realQueues, dateFormat, onDateChange, visible, setVisible }) => {

    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            okText="Submit"
            onOk={() => form.validateFields()
                .then(value => {
                    // dispatch(getTrunkPerHourReport({ date: filterDate, queue: queue }))
                    fetchReport()
                    setVisible(false)
                })
            }
            // okButtonProps={{
            //     loading: btnLoading,
            //     icon: <SaveOutlined />
            // }}
            title="Work Code Agent Wise Report Filter"
        >
            {/* <Spin spinning={btnLoading} indicator={<SyncOutlined spin />}> */}
            <Form
                form={form}
                layout="vertical"
            >
                <Form.Item name={"picker"}>
                    <RangePicker format={dateFormat} onChange={onDateChange} />
                </Form.Item>
                <Form.Item name={"real_queues"}>
                    <Select
                        size="default"
                        placeholder="Select Queues"
                        value={queueField}
                        onChange={(e) => {
                            setQueueField(e)
                        }}
                        style={{ marginRight: "10px", minWidth: "130px" }}
                    >

                        {realQueues.length > 0 &&
                            realQueues.map((value, index) => (
                                <Select.Option key={index} value={value.name}>
                                    {value.name}
                                </Select.Option>
                            ))}
                    </Select>
                </Form.Item>

                <Form.Item name={"queues"} >
                    <Select
                        {...sharedProps}
                        // size="default"
                        placeholder="Agents"
                        mode="multiple"
                        value={selectedQueue}
                        onChange={(e) => handleSelectChange(e)}
                        style={{ marginRight: "10px", minWidth: "130px" }}
                    >
                        <Select.Option
                            key="Select All"
                            value={"select_all"}
                            label="Select All"
                        >
                            Select All
                        </Select.Option>

                        {/* <Option key="Select All">Select All</Option> */}
                        {queues.length > 0 &&
                            queues.map((value, index) => (
                                <Select.Option key={index} value={value.name}>
                                    {value.name}
                                </Select.Option>
                            ))}
                    </Select>
                </Form.Item>
            </Form>
            {/* </Spin> */}
        </Modal>
    )
}


export default WorkCodeWiseReport;