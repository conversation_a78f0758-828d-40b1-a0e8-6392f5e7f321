import { <PERSON><PERSON>, Card, DatePicker, Form, Select, message } from "antd";
import { SaveOutlined } from "@ant-design/icons";
import moment from "moment";
import { useEffect, useState } from "react";
import apiClient from "../../Shared/apiClient";
import { openNotificationWithIcon } from "../../Shared/notification";

export const BulkRecordingDownloader = () => {
    const [loading, setLoading] = useState(false)
    const [startDate, setStartDate] = useState();
    const [selectedQueue, setSelectedQueue] = useState();
    const [queues, setQueues] = useState([]);

    const { RangePicker } = DatePicker;

    useEffect(() => {
        apiClient.get('api/queue')
            .then((res) => setQueues(res.data))
            .catch(() => message.error("Failed to load queues"));
    }, []);

    const onFinish = () => {
        if (!startDate || !selectedQueue) {
            message.warning("Please select both date and queue.");
            return;
        }

        setLoading(true)

        apiClient.post('/api/getZipFile',
            {
                date: startDate,
                queue: selectedQueue
            },
            {
                responseType: 'blob'
            }
        )
            .then((response) => {
                const blob = new Blob([response.data], { type: 'application/zip' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `recordings_${startDate}_${selectedQueue}.zip`;
                document.body.appendChild(a);
                a.click();
                a.remove();
                window.URL.revokeObjectURL(url);
                setLoading(false);
            })
            .catch(async (error) => {
                setLoading(false);

                if (error.response?.data instanceof Blob) {
                    try {
                        const text = await error.response.data.text();
                        const json = JSON.parse(text);
                        const errorMsg = json.message || "Download failed. Please try again.";
                        openNotificationWithIcon('error', errorMsg);
                    } catch {
                        openNotificationWithIcon('error', "Download failed. Please try again.");
                    }
                } else {
                    const errorMsg =
                        error.response?.data?.message || "Download failed. Please try again.";
                    message.error(errorMsg);
                    openNotificationWithIcon('error', errorMsg);
                }
            });


    };

    return (
        <Card>
            <b>Bulk Recording Download</b>
            <Card style={{ width: '100%' }}>
                <Form
                    name="bulk_recording_download"
                    onFinish={onFinish}
                    style={{ paddingTop: '10px', display: 'flex', alignItems: 'center', gap: '10px' }}
                >
                    <Form.Item name="start_date" label="Date" rules={[{ required: true }]}>
                        <DatePicker
                            onChange={(date, dateString) => setStartDate(dateString)}
                            format="YYYY-MM-DD"
                        />
                    </Form.Item>
                    <Form.Item name="queue" rules={[{ required: true }]}>
                        <Select
                            placeholder="Select Queue"
                            onChange={value => setSelectedQueue(value)}
                        >
                            {queues.map((queue, index) => (
                                <Select.Option key={index} value={queue.name}>
                                    {queue.name}
                                </Select.Option>
                            ))}
                        </Select>
                    </Form.Item>
                    <Form.Item>
                        <Button
                            htmlType="submit"
                            type="primary"
                            icon={<SaveOutlined />}
                            loading={loading}
                            disabled={!startDate || !selectedQueue}
                        >
                            Download
                        </Button>
                    </Form.Item>
                </Form>
            </Card>
        </Card>
    );
};
