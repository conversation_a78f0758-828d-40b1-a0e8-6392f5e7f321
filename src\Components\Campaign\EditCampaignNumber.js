import { useState, useEffect } from "react"
import { Form, Input, InputNumber, Modal } from "antd";

const EditCampaignNumber = props => {

    const [form] = Form.useForm()
    const [visible, setVisible] = useState(false)

    useEffect(() => {
        props.item && form.setFieldsValue({ number: props.item.number, name: props.item.name, attempts: props.item.attempts })
    }, [props.item])

    return (
        <Modal
            visible={props.item}
            title={`Edit number ${props.item.number}`}
            okText="Save"
            onCancel={props.onCancel}
            onOk={() => {
                form
                    .validateFields()
                    .then(values => {
                        props.onSubmit(values)
                    }).catch(err => console.log(err))
            }}
        >
            <Form
                form={form}
            >
                <Form.Item name="number" label="Number">
                    <Input />
                </Form.Item>
                <Form.Item name="name" label="Name">
                    <Input />
                </Form.Item>
                <Form.Item name="city" label="Location">
                    <Input />
                </Form.Item>
                <Form.Item name="attempts" label="Attempts">
                    <InputNumber max={10} min={1} />
                </Form.Item>
            </Form>
        </Modal>
    )
}

export default EditCampaignNumber