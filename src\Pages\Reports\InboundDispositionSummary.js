import { Button, Space, Table, Typography, Select, Form, Modal, Input, DatePicker } from "antd";
import { useDispatch, useSelector } from "react-redux";
import React, { useEffect, useRef } from "react";
import { getInboundSummaryDisposition, getInboundSummaryDispositionFilter, getInboundSummaryDispositionReset } from "../../Actions/InboundDispositionSummaryActions";
import { DownloadOutlined, SettingOutlined, ReloadOutlined, SearchOutlined, FilterOutlined, SyncOutlined } from "@ant-design/icons";
import { CSVLink } from "react-csv";
import { useState } from "react";
import apiClient from "../../Shared/apiClient";
import Highlighter from "react-highlight-words";
export const InboundDispositionSummary = () => {

    const dispatch = useDispatch()
    const inboundDispositionSummaryState = useSelector(state => state.InboundDispositionSummaryReducer)
    const [resetFilter, setResetFilter] = useState(false)
    const [filterVisible, setFilterVisible] = useState(false)

    // useEffect(() => {

    //     dispatch(getInboundSummaryDisposition())
    //     console.log("kk", inboundDispositionSummaryState)
    // }, []);

    const [searchText, setSearchText] = useState('')
    const [searchedColumn, setSearchedColumn] = useState('')
    const searchInput = useRef()
    const [form] = Form.useForm()

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    ref={searchInput}
                    placeholder={`Search`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText(selectedKeys[0])
                            setSearchedColumn(dataIndex)
                        }}
                    >
                        Filter
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        setSearchText(selectedKeys[0])
        setSearchedColumn(dataIndex)
    };

    const handleReset = clearFilters => {
        clearFilters({ confirm: true });
        setSearchText('')
    }

    const columns = [
        {
            title: 'ID',
            dataIndex: 'id',
            key: 'id',
            ...getColumnSearchProps('id')
        },
        {
            title: 'Call Status',
            dataIndex: 'call_status',
            key: 'call_status',
            ...getColumnSearchProps('call_status')
        },
        {
            title: 'Count',
            dataIndex: 'count',
            key: 'count',
            ...getColumnSearchProps('count')
        }
    ]

    const resetFormFilter = () => {
        // dispatch(getInboundSummaryDisposition())
        dispatch(getInboundSummaryDispositionReset())
        form.resetFields();
        setResetFilter(true)
    }


    return (<>

        <Table
            dataSource={inboundDispositionSummaryState.data}
            loading={{ spinning: inboundDispositionSummaryState.isLoading, indicator: <SyncOutlined spin /> }}
            columns={columns}
            title={data => <>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    Inbound Disposition Summary
                    <Space>
                        <Button danger type="primary" onClick={() => resetFormFilter()} icon={<ReloadOutlined />}>Reset Filter</Button>
                        <Button onClick={() => setFilterVisible(true)} icon={<FilterOutlined />}>Filter</Button>
                        <CSVLink style={{ textDecoration: 'none', color: '#fff' }} data={data} filename="InboundDispositionSummary.csv">
                            <Button icon={<DownloadOutlined />} type="primary" disabled={inboundDispositionSummaryState.data.length == 0}>
                                Download
                            </Button>
                        </CSVLink>
                    </Space>
                </div>
            </>}
            summary={data => {
                let total = 0
                data.forEach(item => total += item.count)
                return (<>
                    <Table.Summary.Row>
                        <Table.Summary.Cell colSpan={2}>Total</Table.Summary.Cell>
                        <Table.Summary.Cell>
                            <Typography.Text strong>{total}</Typography.Text>
                        </Table.Summary.Cell>
                    </Table.Summary.Row>
                </>)
            }}
        />
        <InboundDispositionFilter form={form} buttonLoading={inboundDispositionSummaryState.isLoading} resetField={resetFilter} visible={filterVisible} setVisible={setFilterVisible} />

    </>
    )
}

const InboundDispositionFilter = ({ visible, form, setVisible, resetField, buttonLoading }) => {

    const dispatch = useDispatch()
    const [queues, setQueues] = useState([])

    useEffect(() => {
        if (resetField) {
            form.resetFields()
        }
    }, [resetField])

    useEffect(() => {
        apiClient.get('api/queue').then((res) => setQueues(res.data), console.log('queues', queues))
    }, [])

    return (
        <Modal
            visible={visible}
            onCancel={() => setVisible(false)}
            title="Inbound Disposition Summary Filter"
            size="small"
            onOk={() => {
                form.validateFields()
                    .then(result => dispatch(getInboundSummaryDispositionFilter(result)))
                    .catch(e => console.log(e))
                setVisible(false)
            }
            }
            okText="Submit"
            oKButtonProps={{
                loading: buttonLoading
            }}
        >
            <Form
                layout="vertical"
                form={form}
            >
                <Form.Item name="date" label="Date Range">
                    <DatePicker.RangePicker />
                </Form.Item>
                <Form.Item name="queue" label="Queue" >
                    <Select placeholder="queues" style={{ width: '50%' }}>
                        {queues.map((queue, index) => <Select.Option key={index} value={queue.name}>
                            {queue.name}
                        </Select.Option>)}
                    </Select>
                </Form.Item>

            </Form>
        </Modal>
    )
}