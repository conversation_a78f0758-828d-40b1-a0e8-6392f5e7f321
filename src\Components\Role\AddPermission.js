import { useDispatch, useSelector } from "react-redux";
import { Checkbox, Divider, Modal, Spin } from "antd";
import { useEffect, useState } from 'react'
import { getPermissionsInRole } from "../../Actions/PermissionActions";
import { getPermissionByRole } from "../../Actions/RoleActions";


const AddPermission = ({ record, isLoading, visible, setVisible, onCreate }) => {

    const dispatch = useDispatch()
    const permission = useSelector(state => state.PermissionReducer)
    const role = useSelector(state => state.RoleReducer)
    const [checkAll, setCheckAll] = useState(false);
    const [defaultCheckedList, setDefaulCheckedList] = useState([])
    const [checkedList, setCheckedList] = useState(defaultCheckedList);
    const [plainOptions, setPlainOptions] = useState(null)
    const [indeterminate, setIndeterminate] = useState(true);

    useEffect(() => {
        if (record && visible) {
            dispatch(getPermissionByRole(record.id))
            dispatch(getPermissionsInRole())
        }
    }, [visible])

    useEffect(() => {
        if (role.rolePermission) {
            setDefaulCheckedList(role.rolePermission)
            console.log(role.rolePermission)
            setCheckedList(role.rolePermission)
        }
    }, [role])

    useEffect(() => {
        if (permission.permissions) {
            setPlainOptions(permission.permissions.map(value => value.name))
        }
    }, [permission])

    const onChange = list => {
        console.log(list)
        setCheckedList(list);
        setIndeterminate(!!list.length && list.length < plainOptions.length);
        setCheckAll(list.length === plainOptions.length);
    };

    const onCheckAllChange = e => {
        console.log(e.target.checked)
        setCheckedList(e.target.checked ? plainOptions : []);
        setIndeterminate(false);
        setCheckAll(e.target.checked);
    };

    return (
        <Modal
            visible={visible}
            title={'Add Permission'}
            okText="Submit"
            cancelText="Cancel"
            closable={true}
            onCancel={() => {
                //form.resetFields()
                setVisible(false)
            }}
            onOk={() => { onCreate(checkedList) }}
        >
            <Spin spinning={permission.isLoading || isLoading}>
                <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
                    Check all
                </Checkbox>
                <Divider />
                <Checkbox.Group options={plainOptions} value={checkedList} onChange={onChange} />
            </Spin>
        </Modal>
    )
}
export default AddPermission