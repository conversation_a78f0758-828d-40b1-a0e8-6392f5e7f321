import { Button, Descriptions, Modal, Space, Spin, Table } from "antd";
import { DesktopOutlined, DownloadOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import React, { useState, useEffect } from 'react'
import {
    CdrReportFilter,
    getAllReportsPaginate
} from "../../Actions/CallDetailReportsActions";
import { CSVLink, CSVDownload } from "react-csv";
import CallDetailReportFilter from "../../Components/Reports/CdrReportFilter"
import env from "react-dotenv"
import generateCDR_PDF from "./PDFGenerator/generateCDR_PDF";
import { baseURL } from "../../Shared/baseUrl";

const CallDetailRecords = () => {

    const report = useSelector(state => state.CdrReducer)
    const [record, setRecord] = useState(null)
    const [showDetails, setShowDetails] = useState(false)
    const [filter, setFilter] = useState(false)
    const [loading, isLoading] = useState(false)
    const [filteredPaginate, setFilteredPaginate] = useState(false)
    const [values, setValues] = useState()
    const dispatch = useDispatch()

    const getData = (page, pageSize = 10) => {
        const data = getRandomuserParams({})
        dispatch(getAllReportsPaginate(page, pageSize, data))
    }

    const getRandomuserParams = params => ({
        results: params?.pagination?.pageSize,
        page: params?.pagination?.current,
        ...params,
    });

    useEffect(() => {
        getData(1, 10)
    }, [])

    function onFilter(values, pagination) {
        const data = getRandomuserParams({})
        setValues(values)
        let obj = {
            values,
            pagination,
            data
        }
        dispatch(CdrReportFilter(obj))
        setFilter(false)
    }

    const handleTableChange = (pagination) => {
        if (filteredPaginate)
            getData(pagination.current, pagination.pageSize)
        else
            onFilter(values, pagination)
    }

    return (
        <Spin spinning={report.isLoading}>
            <CallDetailReportFilter setVisible={setFilter} setFilteredPaginate={setFilteredPaginate} isLoading={report.isLoading} onCancel={() => setFilter(false)} visible={filter} onCreate={onFilter} record={"ok"} />
            <Button onClick={() => setFilter(true)} type="primary" icon={<PlusCircleOutlined />}>
                Filter
            </Button>
            <Modal
                width="100%"
                visible={showDetails}
                closable={true}
                onCancel={() => setShowDetails(false)}
                onOk={() => setShowDetails(false)}
                centered
            >
                <Descriptions style={{ padding: 20 }} title="CDR Info" bordered size="small">
                    {record && Object.keys(record).map((key, index) => (
                        <Descriptions.Item key={key} label={key}>
                            {key === "recordingfile" && record[key] ? <audio preload="auto" controls src={process.env.REACT_APP_baseURL + '/api/download/' + record[key]} /> : record[key]}
                        </Descriptions.Item>))}
                </Descriptions>
            </Modal>

            <div style={{ float: 'right' }}>
                <div style={{ marginBottom: 16 }}>
                    <Space>

                        {report.data &&
                            <Button type={"primary"} onClick={(<CSVDownload data={report.data} target="_blank" />)}>
                                <CSVLink data={report.data} filename={"CDR_Reports.csv"}> Download CSV</CSVLink>
                            </Button>
                        }
                        {report.data &&
                            <Button type={"default"} onClick={() => {
                                generateCDR_PDF(report.data)
                            }} target="_blank">Generate PDF</Button>
                        }
                    </Space>
                </div>
            </div>

            <Table scroll={{ x: 800 }} size="small" bordered dataSource={report.cdr} loading={loading} onChange={handleTableChange} pagination={report.pagination}>
                <Table.Column title="Download" align="center" key="download" render={record => (record.recordingfile ? <Button target="_blank" shape="round" block href={process.env.REACT_APP_baseURL + '/api/download/' + record.recordingfile} type="primary" icon={<DownloadOutlined />} /> : '')} />
                <Table.Column dataIndex="src" key="src" title="src" />
                <Table.Column dataIndex="dst" key="dst" title="dst" />

                <Table.Column dataIndex="disposition" key="disposition" title="disposition" sorter={{
                    compare: (a, b) => a.disposition.length - b.disposition.length,
                }} />

                <Table.Column dataIndex="duration" key="duration" title="duration" sorter={{
                    compare: (a, b) => a.duration - b.duration,
                    sorter: (a, b) => a.duration - b.duration,
                }} />

                <Table.Column dataIndex="start" key="Date" title="Date" sorter={{
                    compare: (a, b) => Date.parse(a.start) - Date.parse(b.start)
                }} />

                <Table.Column align="center" title="Actions" render={(text, record) => (
                    <Space>
                        <Button onClick={() => {
                            setRecord(record)
                            setShowDetails(true)
                        }} type="outlined" icon={<DesktopOutlined />}>Details</Button>
                    </Space>
                )} />
            </Table>
        </Spin>
    )
}

export default CallDetailRecords
