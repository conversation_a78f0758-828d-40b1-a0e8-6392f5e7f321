import { useSelector, useDispatch } from "react-redux";
import { useState, useEffect } from "react"
import { createQueue, deleteQueue, fetchAllQueues, getEnumValues, getQueues, updateQueue } from "../Actions/QueueActions";
import { Button, Col, Descriptions, Form, Input, Modal, Radio, Row, Select, Space, Spin, Table } from "antd";
import {
    DeleteOutlined,
    DesktopOutlined,
    EditOutlined,
    PlusOutlined,
} from "@ant-design/icons"
import { startCase } from "lodash"
import { openNotificationWithIcon } from "../Shared/notification";
import Text from "antd/es/typography/Text";
import apiClient from "../Shared/apiClient";

const Queue = () => {

    const QueueReducer = useSelector(state => state.QueueReducer)
    const dispatch = useDispatch()
    const [showDetails, setShowDetails] = useState(false)
    const [showEdit, setShowEdit] = useState(false)
    const [showCreate, setShowCreate] = useState(false)
    const [queues, setQueues] = useState([])
    const [record, setRecord] = useState(null)
    const [form] = Form.useForm()

    useEffect(() => {
        // dispatch(getQueues())
        dispatch(fetchAllQueues())
        dispatch(getEnumValues())
    }, [])

    useEffect(() => {
        if (QueueReducer.errMess) {
            openNotificationWithIcon('error', QueueReducer.errMess)
            // If we're in create mode and there's an error, keep the modal open
            if (showCreate) {
                form.setFields([
                    {
                        name: 'name',
                        errors: [QueueReducer.errMess.includes('duplicate') ? 'Queue name already exists' : QueueReducer.errMess]
                    }
                ])
            }
        }
    }, [QueueReducer.errMess])

    useEffect(() => {
        if (QueueReducer.message) {
            openNotificationWithIcon('success', QueueReducer.message)
            dispatch(getQueues())
        }
    }, [QueueReducer.message])

    const handleDetails = () => {
        setShowDetails(false)
    }

    const handleSubmit = values => {
        setShowEdit(false)
        dispatch(updateQueue(record.name, values))
    }

    const handleCreate = values => {
        // Check for duplicate name before submitting
        const isDuplicate = QueueReducer.queues.some(
            queue => queue.name.toLowerCase() === values.name.toLowerCase()
        )

        if (isDuplicate) {
            form.setFields([
                {
                    name: 'name',
                    errors: ['Queue name already exists']
                }
            ])
            return
        }

        dispatch(createQueue(values))
        setShowCreate(false)
        setRecord([])
        // fetchAllQueues()
    }

    const handleShowCreate = () => {
        setRecord(QueueReducer.column)
        // setRecord
        form.resetFields()
        setShowCreate(true)
    }

    // Custom validator for queue name
    const validateQueueName = (_, value) => {
        if (!value) {
            return Promise.reject('Queue name is required')
        }

        // Only check for duplicates on create, not edit
        if (showCreate && QueueReducer.queues) {
            const isDuplicate = QueueReducer.queues.some(
                queue => queue.name.toLowerCase() === value.toLowerCase()
            )

            if (isDuplicate) {
                return Promise.reject('Queue name already exists')
            }
        }

        return Promise.resolve()
    }

    return (
        <Spin spinning={QueueReducer.isLoading}>
            <Modal
                width="100%"
                visible={showDetails}
                closable={true}
                onCancel={handleDetails}
                onOk={handleDetails}
                centered
            >
                <Descriptions style={{ padding: 20 }} title="Queue Info" bordered size="small" >
                    {record && Object.keys(record).map((key, index) => (
                        <Descriptions.Item key={index} label={startCase(key.replace(/_/g, ' ').toLowerCase())}>
                            {record[key]}
                        </Descriptions.Item>))}
                </Descriptions>
            </Modal>
            <Modal
                centered
                title="Edit Queue"
                width="100%"
                visible={showEdit}
                destroyOnClose={true}
                closable={true}
                okText="Update"
                onOk={() => {
                    form.validateFields()
                        .then(values => handleSubmit(values))
                        .catch(error => console.log(error))
                }}
                onCancel={() => {
                    setShowEdit(false)
                }}
            >
                <Form
                    form={form}
                    layout="inline"
                    name="form_in_modal"
                // initialValues={record}
                >
                    <Row gutter={[16, 24]}>
                        {record && Object.keys(record).map((key, index, array) => <Col span={8} key={key}>
                            <Form.Item key={key}
                                name={key}
                                label={startCase(key.replace(/_/g, ' ').toLowerCase())}
                                rules={key === 'name' ? [
                                    { required: true, message: 'Queue name is required' }
                                ] : []}
                            >
                                {QueueReducer.enums[key] ? <Select>
                                    {QueueReducer.enums[key].map((value, index) => (
                                        <Select.Option key={value} value={value}>{value}</Select.Option>
                                    ))}
                                </Select> : <Input />}
                            </Form.Item>
                        </Col>)}
                    </Row>
                </Form>
            </Modal>
            <Modal
                centered
                title="Add Queue"
                width="100%"
                visible={showCreate}
                destroyOnClose={true}
                closable={true}
                okText="Submit"
                onOk={() => {
                    form.validateFields()
                        .then(values => handleCreate(values))
                        .catch(error => console.log(error))
                }}
                onCancel={() => {
                    form.resetFields();
                    setShowCreate(false)
                }}
            >
                <Form
                    form={form}
                    layout="inline"
                    name="form_in_modal"
                >
                    <Row gutter={[16, 24]}>
                        {record && Object.keys(record).map((key, index, array) => <Col span={8} key={key}>
                            <Form.Item key={key}
                                name={key}
                                label={startCase(key.replace(/_/g, ' ').toLowerCase())}
                                rules={key === 'name' ? [
                                    // { required: true, message: 'Queue name is required' },
                                    { validator: validateQueueName }
                                ] : []}
                            >
                                {QueueReducer.enums[key] ? <Select>
                                    {QueueReducer.enums[key].map((value, index) => (
                                        <Select.Option key={value} value={value}>{value}</Select.Option>
                                    ))}
                                </Select> : <Input />}
                            </Form.Item>
                        </Col>)}
                    </Row>
                </Form>
            </Modal>

            <Space style={{ marginBottom: 16 }}>
                <Button onClick={handleShowCreate} icon={<PlusOutlined />} type="primary">Add New</Button>
            </Space>

            <Table scroll={{ x: 800 }} size="small" bordered dataSource={QueueReducer.allQueues}>
                <Table.Column dataIndex="name" key="name" title="Name" />
                <Table.Column dataIndex="strategy" key="strategy" title="Strategy" />
                <Table.Column dataIndex="servicelevel" key="servicelevel" title="Service Level" />
                <Table.Column align="center" title="Actions" render={(text, record) => (
                    <Space>
                        <Button onClick={() => {
                            setRecord(record)
                            setShowDetails(true)
                        }} type="outlined" icon={<DesktopOutlined />}>Details</Button>
                        <Button onClick={() => {
                            form.resetFields()
                            form.setFieldsValue(record)
                            setRecord(record)
                            setShowEdit(true)
                        }} icon={<EditOutlined />} type="primary">Edit</Button>
                        <Button onClick={() => {
                            dispatch(deleteQueue(record.name))
                        }} icon={<DeleteOutlined />} type="danger">Delete</Button>
                    </Space>
                )} />
            </Table>
        </Spin>
    )
}

export default Queue
