import * as ActionTypes from "../Constants/AgentRoutesConstant"
import apiClient from "../Shared/apiClient";
import {cookie} from "../Shared/constants";
import {AGENT} from "../Endpoints/AgentRoutes";
import {logoutUser} from "./UserActions";
import {handleError} from "../Shared/handleError";

export const getSupervisor = () => dispatch => {
    dispatch(supervisorLoading())
    apiClient.get(cookie).then(() => apiClient.get(`api/supervisor`).then(response => dispatch(supervisorSuccess(response.data))).catch(e => dispatch(supervisorFailed(handleError(e)))).catch(e => dispatch(supervisorFailed(handleError(e)))))
}

export const createSupervisor = data => dispatch => {
    dispatch(supervisorLoading())
    apiClient.get(cookie).then(() => {
        apiClient.post(`api/supervisor`, data).then(response => dispatch(supervisorCreateSuccess(response.data)))
            .catch(error => {
                if(error.response) {
                    if (error.response.status === 401)
                        dispatch(logoutUser())
                    dispatch(supervisorFailed(error.response))
                }
                else
                    dispatch(supervisorFailed(error.message))
            })
    }).catch(error => {
        if(error.response)
            dispatch(supervisorFailed(error.response))
        else
            dispatch(supervisorFailed(error.message))
    })
}

export const getDataByID = id => dispatch => {
    dispatch(supervisorLoading())
    apiClient.get(cookie).then(() => {
        //dispatch(supervisorUpdateSuccess(response.data))
        apiClient(`api/supervisor/${id}/edit`).then(response => dispatch(getDataByIDSupervisor(response.data)))
            .catch(error => {
                if(error.response) {
                    if (error.response.status === 401)
                        dispatch(logoutUser())
                    dispatch(supervisorFailed(error.response))
                }
                else
                    dispatch(supervisorFailed(error.message))
            })
    }).catch(error => {
        if(error.response)
            dispatch(supervisorFailed(error.response))
        else
            dispatch(supervisorFailed(error.message))
    })
}

export const updateSupervisor = (id, data) => dispatch => {
    dispatch(supervisorLoading())
    apiClient.get(cookie).then(() => {
        apiClient.put(`api/supervisor/${id}`, data).then(response => dispatch(supervisorUpdateSuccess(response.data)))
            .catch(error => {
                if(error.response) {
                    if (error.response.status === 401)
                        dispatch(logoutUser())
                    dispatch(supervisorFailed(error.response))
                }
                else
                    dispatch(supervisorFailed(error.message))
            })
    }).catch(error => {
        if(error.response)
            dispatch(supervisorFailed(error.response))
        else
            dispatch(supervisorFailed(error.message))
    })
}

// export const deleteSupervisor = id => dispatch => {
//     dispatch(supervisorLoading())
//     apiClient.get(cookie).then(() => {
//         apiClient.delete(`api/supervisor/${id}`)
//             .then(response => dispatch(supervisorDeleteSuccess(response.data)))
//             .catch(error => {
//                 if(error.response) {
//                     if (error.response.status === 401)
//                         dispatch(logoutUser())
//                     dispatch(supervisorFailed(error.response))
//                 }
//                 else
//                     dispatch(supervisorFailed(error.message))
//             })
//     }).catch(error => {
//         if(error.response)
//             dispatch(supervisorFailed(error.response))
//         else
//             dispatch(supervisorFailed(error.message))
//     })
// }

export const deleteSupervisor = id => dispatch => {
    return new Promise((resolve, reject) => {
        dispatch(supervisorLoading());
        apiClient.get(cookie).then(() => {
            apiClient.delete(`api/supervisor/${id}`)
                .then(response => {
                    // Pass both the message and the full response
                    dispatch(supervisorDeleteSuccess({
                        message: response.data.message || response.data,
                        id: id
                    }));
                    resolve(response.data);
                })
                .catch(error => {
                    const errorMessage = error.response?.data?.message || 
                                       error.response?.data || 
                                       error.message;
                    if(error.response) {
                        if (error.response.status === 401) {
                            dispatch(logoutUser());
                        }
                        dispatch(supervisorFailed(errorMessage));
                    }
                    else {
                        dispatch(supervisorFailed(errorMessage));
                    }
                    reject(errorMessage);
                });
        }).catch(error => {
            const errorMessage = error.response?.data?.message || 
                               error.response?.data || 
                               error.message;
            dispatch(supervisorFailed(errorMessage));
            reject(errorMessage);
        });
    });
}

const supervisorLoading = () => ({
    type: "supervoiserLoading"
})

const getDataByIDSupervisor = data => ({
    type: "getDataByIDSupervisor",
    payload: data
})

const supervisorSuccess = data => ({
    type: "supervisorSuccess",
    payload: data
})

const supervisorFailed = error => ({
    type: "supervisorFailed",
    payload: error
})

const supervisorCreateSuccess = message => ({
    type: "supervisorCreateSuccess",
    payload: message
})

const supervisorUpdateSuccess = message => ({
    type: "supervisorUpdateSuccess",
    payload: message
})

const supervisorDeleteSuccess = message => ({
    type: "supervisorDeleteSuccess",
    payload: message
})