import * as ActionTypes from "../Constants/CallDetailRecordReportConstants";
import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";

export const getCallDetailRecordReport = (payload) => (dispatch) => {
    dispatch(callDetailRecordReportLoading());
    let route = "/api/report/call-detail-records";
    const { current, pageSize } = payload;

    if (current >= 1) {
        route = `${route}?page=${current}`;
    }

    if (pageSize) {
        route = `${route}&records=${pageSize}`;
    }
    apiClient
        .post(route)
        .then((r) => dispatch(callDetailRecordReportSuccess(r.data)))
        .catch((e) => dispatch(callDetailRecordReportFailed(handleError(e))))
        .catch((e) => dispatch(callDetailRecordReportFailed(handleError(e))));
};

export const getCallDetailRecordReportFiltered = (payload) => (dispatch) => {
    dispatch(callDetailRecordReportLoading());
    let route = "/api/report/call-detail-records-filtered";
    const { current, pageSize } = payload;
    if (current >= 1) {
        route = `${route}?page=${current}`;
    }
    if (pageSize) {
        route = `${route}&records=${pageSize}`;
    }
    apiClient
        .post(route, payload.data)
        .then((r) => dispatch(callDetailRecordReportSuccess(r.data)))
        .catch((e) => dispatch(callDetailRecordReportFailed(handleError(e))))
        .catch((e) => dispatch(callDetailRecordReportFailed(handleError(e))));
};

const callDetailRecordReportSuccess = (data) => ({
    type: ActionTypes.CALL_DETAIL_REPORT_SUCCESS,
    payload: data,
});

const callDetailRecordReportFailed = (err) => ({
    type: ActionTypes.CALL_DETAIL_REPORT_FAILED,
    payload: err,
});

const callDetailRecordReportLoading = () => ({
    type: ActionTypes.CALL_DETAIL_REPORT_LOADING,
});
