import apiClient from "../Shared/apiClient";
import { handleError } from "../Shared/handleError";
import { openNotificationWithIcon } from "../Shared/notification";

export const getTrunkPerHourReport = data => dispatch => {
    dispatch(trunkHourReportLoading())
    apiClient.post(`/api/report/trunk-per-hour`, data).then(r => dispatch(trunkHourReportSuccess(r.data))).catch(e => {
        dispatch(trunkHourReportFailed(handleError(e)))
        openNotificationWithIcon('error', handleError(e))
    })
}
export const trunkPerHourReset = () => dispatch => {
    dispatch(trunkHourPerReset())
}

const trunkHourReportSuccess = data => ({
    type: "TRUNK_HOUR_REPORT_SUCCESS",
    payload: data
})

const trunkHourReportFailed = err => ({
    type: "TRUNK_HOUR_REPORT_FAILED",
    payload: err
})

const trunkHourReportLoading = () => ({
    type: "TRUNK_HOUR_REPORT_LOADING"
})
const trunkHourPerReset = () => ({
    type: "TRUNK_HOUR_REPORT_RESET"
})
