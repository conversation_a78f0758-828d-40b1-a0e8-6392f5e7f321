import { useState, useEffect } from "react";
import {
    Checkbox,
    DatePicker,
    Divider,
    Form,
    Input,
    Modal,
    Radio,
    Select,
    Space,
    Spin,
    Typography,
} from "antd";
import { useDispatch, useSelector } from "react-redux";
import { deleteFormField, getFormFields } from "../../Actions/FormFieldActions";
import { DeleteOutlined, DribbbleOutlined } from "@ant-design/icons";
import apiClient from "../../Shared/apiClient";
import { openNotificationWithIcon } from "../../Shared/notification";

const { Text } = Typography;

export const Functions = ({
    functionsVisible,
    setFunctionsVisible,
    formId,
}) => {
    const dispatch = useDispatch();
    const formFieldState = useSelector((state) => state.FormFieldReducer);

    useEffect(() => {
        if (functionsVisible) dispatch(getFormFields(formId));
    }, [functionsVisible]);

    useEffect(() => {
        window.onbeforeunload = function () {
            return false;
        };
    }, []);

    //   const renderField = (value) => {
    //     switch (value.type) {
    //       case "checkbox":
    //         return (
    //           <Checkbox.Group key={value.id}>
    //             {value.form_field_options &&
    //               value.form_field_options.map((value, index) => (
    //                 <Checkbox value={value.label} key={value.id}>
    //                   {value.label}
    //                 </Checkbox>
    //               ))}
    //           </Checkbox.Group>
    //         );
    //       case "radio":
    //         return (
    //           <Radio.Group key={value.id}>
    //             {value.form_field_options &&
    //               value.form_field_options.map((value, index) => (
    //                 <Radio value={value.label} key={value.id}>
    //                   {value.label}
    //                 </Radio>
    //               ))}
    //           </Radio.Group>
    //         );
    //       case "input":
    //         return <Input key={value.id} />;
    //       case "textarea":
    //         return <Input.TextArea key={value.id} />;
    //       case "select":
    //         return (
    //           <Select>
    //             {value.form_field_options &&
    //               value.form_field_options.map((value, index) => (
    //                 <Select.Option key={value.id}>{value.label}</Select.Option>
    //               ))}
    //           </Select>
    //         );
    //       case "date":
    //         return <DatePicker key={value.id} style={{ width: "100%" }} />;
    //     }
    //   };

    const RenderedLabel = (props) => (
        <Space split={<Divider type="vertical" />}>
            {props.label}
            {props.label === "Phone Number" ? null : (
                <DeleteOutlined
                    onClick={() => dispatch(deleteFormField(props.id, formId))}
                />
            )}
        </Space>
    );


    return (
        <Modal
            visible={functionsVisible}
            onCancel={() => setFunctionsVisible(false)}
            title="Form Functions"
            onOk={() => setFunctionsVisible(false)}
        >
            <Spin
                indicator={<DribbbleOutlined />}
                spinning={formFieldState?.isLoading}
            >
                {formFieldState?.fields?.form_function?.length > 0 &&
                    formFieldState.fields.form_function.map((value, index) => (
                        <>
                            <Text key={index}>
                                Function:{" "}
                                <Text strong style={{ paddingRight: "1rem" }}>
                                    {value.function_type.function_name}
                                </Text>{" "}
                                Reference Field:{" "}
                                <Text strong style={{ paddingRight: "1rem" }}>
                                    {value.reference_field?.label
                                        ? value.reference_field?.label
                                        : "Not Set"}
                                </Text>
                            </Text>
                            <DeleteOutlined
                                onClick={() =>
                                    apiClient
                                        .delete(`api/formFunction/${value.id}`)
                                        .then(() =>
                                            dispatch(getFormFields(formId))
                                        ).catch((err) =>
                                            openNotificationWithIcon("Error", err.message)
                                        )
                                }
                            />
                        </>
                    ))}
            </Spin>
        </Modal>
    );
};
