import * as ActionTypes from "../Constants/CallDetailRecordReportConstants"
const initialState = {
    data: [],
    isLoading: false,
    errMess: ''
}

export const CallDetailRecordReportReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.CALL_DETAIL_REPORT_LOADING:
            return {...state, isLoading: true, data: []}
        case ActionTypes.CALL_DETAIL_REPORT_SUCCESS:
            return {...state, isLoading: false, data: action.payload, errMess: ''}
        case ActionTypes.CALL_DETAIL_REPORT_FAILED:
            return {...state, isLoading: false, errMess: action.payload, data: []}
    }
}